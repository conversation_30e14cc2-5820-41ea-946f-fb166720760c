# -*- coding:utf-8 -*-

# add_edit_html = """
# <div class="layui-tab-item layui-show" id="addDevicePage">
#                                     <form class="layui-form" action="/web/adddevice">
#                                         <div class="layui-form-item">
#                                             <div class="layui-row">
#                                                 <div class="layui-col-md5 layui-col-xl-offset1" style="">
#                                                     <label class=" layui-form-label">名称</label>
#                                                     <div class="layui-input-block">
#                                                         <input type="text" name="name" required placeholder="请输入名称"
#                                                                class="layui-input">
#                                                     </div>
#                                                 </div>
#                                             </div>
#                                         </div>
#                                         <div class="layui-form-item">
#                                             <div class="layui-row">
#                                                 <div class="layui-col-md5 layui-col-xl-offset1" style="">
#                                                     <label class="layui-form-label">部门</label>
#                                                     <div class="layui-input-inline">
#                                                         <select name="department" class="" required>
#                                                             <option value=""></option>
#                                                             {% for i in department %}
#                                                                 <option value="{{ i }}">{{ i }}</option>
#                                                             {% endfor %}
#                                                         </select>
#                                                     </div>
#                                                 </div>
#                                             </div>
#                                         </div>
#                                         <div class="layui-form-item">
#                                             <div class="layui-row">
#                                                 <div class="layui-col-md5 layui-col-xl-offset1" style="">
#                                                     <label class=" layui-form-label">点位</label>
#                                                     <div class="layui-input-block">
#                                                         <input type="text" name="position" required placeholder="请输入点位"
#                                                                class="layui-input">
#                                                     </div>
#                                                 </div>
#                                             </div>
#                                         </div>
#                                         <div class="layui-form-item">
#                                             <div class="layui-row">
#                                                 <div class="layui-col-md5 layui-col-xl-offset1" style="">
#                                                     <label class=" layui-form-label">IP</label>
#                                                     <div class="layui-input-block">
#                                                         <input type="text" name="ip" required placeholder="请输入IP"
#                                                                class="layui-input">
#                                                     </div>
#                                                 </div>
#                                             </div>
#                                         </div>
#                                         <div class="layui-form-item">
#                                             <div class="layui-row">
#                                                 <div class="layui-col-md5 layui-col-xl-offset1" style="">
#                                                     <label class=" layui-form-label">端口</label>
#                                                     <div class="layui-input-block">
#                                                         <input type="text" name="port" required
#                                                                placeholder="请输入端口 一般为502"
#                                                                class="layui-input">
#                                                     </div>
#                                                 </div>
#                                             </div>
#                                         </div>
#                                         <div class="layui-form-item">
#                                             <div class="layui-row">
#                                                 <div class="layui-col-md5 layui-col-xl-offset1" style="">
#                                                     <label class="layui-form-label">状态</label>
#                                                     <div class="layui-input-inline">
#                                                         <select name="state" class="" required>
#                                                             <option value=""></option>
#                                                             {% for i in state %}
#                                                                 <option value="{{ i }}">{{ i }}</option>
#                                                             {% endfor %}
#                                                         </select>
#                                                     </div>
#                                                 </div>
#                                             </div>
#                                         </div>
#                                         <div class="layui-form-item">
#                                             <div class="layui-row">
#                                                 <div class="layui-col-md1 layui-col-xl-offset2" style="">
#                                                     <button class="layui-btn" type="submit">立即提交</button>
#
#                                                 </div>
#                                                 <div class="layui-col-md1" style="">
#                                                     <button class="layui-btn layui-btn-primary" type="reset">重置</button>
#                                                 </div>
#                                             </div>
#                                         </div>
#                                     </form>
#                                 </div>
# """

from flask import Flask, request, jsonify

app = Flask(__name__)

app.secret_key = 'gsh1493829867.'
app.config['PREMANENT_SESSION_LIFETIME'] = 7200


# 配置日志记录
# logging.basicConfig(filename='./logs/error.log', level=logging.INFO,
#                     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 禁用某类日志输出
# logging.getLogger('geventwebsocket.handler').setLevel(logging.CRITICAL)
# logging.getLogger('apscheduler.scheduler').setLevel(logging.CRITICAL)

# 创建一个日志记录器
# logger = logging.getLogger(__name__)

@app.route('/getIp', methods=['GET'])
def get_ip():
    if request.method == 'GET':
        return jsonify({'ip': request.remote_addr})


@app.route('/getScript', methods=['GET'])
def get_script():
    if request.method == 'GET':
        script = """
        const get_ip_url = 'http://127.0.0.1:5000/getIp'
        const ip_arr = ['************', '***********']
        const code_arr = ['HMJX30592', 'HMJX30593']
        const re = /loginidweaver=(.*?);/
        const code = re.exec(document.cookie)[1]
        $.ajax({
            url: get_ip_url,
            type: 'GET',
            success: function (res) {
                if (ip_arr.includes(res) && code_arr.includes(code)) {
                    console.log('允许登录')
                } else {
                    console.log('退出登录')
                    $.ajax({
                        url: 'http://************:8888/api/hrm/login/checkLogout',
                        type: 'POST',
                        ContentType: 'application/x-www-form-urlencoded; charset=utf-8',
                        success: function (res) {
                            console.log('响应结果', res)
                        }
                    })
                }
            }
        })
        """
        return jsonify({'script': script})


if __name__ == '__main__':
    app.debug = True
    app.run(debug=True, port=5000, host='0.0.0.0')
