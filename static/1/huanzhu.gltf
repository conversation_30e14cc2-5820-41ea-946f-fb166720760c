{"asset": {"generator": "SOLIDWORKSGLTF", "version": "2.0"}, "extensionsUsed": ["KHR_lights_punctual", "Solidworks_custom_properties", "KHR_draco_mesh_compression"], "extensionsRequired": ["KHR_draco_mesh_compression"], "scene": 0, "scenes": [{"name": "默认 - <默认>_显示状态 1", "nodes": [0, 1]}], "nodes": [{"matrix": [-0.0942668, 0.401844, 0.910843, 0, -0.753368, 0.569305, -0.329134, 0, -0.650808, -0.717226, 0.24907, 0, -1.4685, -1.57175, 0.506865, 1], "camera": 0, "name": "current"}, {"mesh": 0, "name": "环柱2.SLDPRT"}], "cameras": [{"type": "orthographic", "orthographic": {"xmag": 1, "ymag": 1, "zfar": 100, "znear": 0.01}}], "meshes": [{"primitives": [{"attributes": {"POSITION": 1, "NORMAL": 2, "TEXCOORD_0": 3}, "indices": 0, "mode": 4, "material": 1, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 0, "attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}}}}]}], "accessors": [{"componentType": 5123, "count": 3576, "type": "SCALAR"}, {"componentType": 5126, "count": 1270, "type": "VEC3", "min": [-0.103787549, 0.0, -0.0561111607], "max": [0.114712447, 0.109999999, 0.0538888425]}, {"componentType": 5126, "count": 1270, "type": "VEC3"}, {"componentType": 5126, "count": 1270, "type": "VEC2"}], "materials": [{"pbrMetallicRoughness": {"baseColorFactor": [0.498039, 0.498039, 0.498039, 1], "metallicFactor": 0, "roughnessFactor": 1}}, {"name": "color", "pbrMetallicRoughness": {"baseColorFactor": [1, 1, 1, 1], "metallicFactor": 0, "roughnessFactor": 0.18}}], "bufferViews": [{"buffer": 0, "byteOffset": 0, "byteLength": 5216}], "buffers": [{"byteLength": 5216, "uri": "data.bin"}]}