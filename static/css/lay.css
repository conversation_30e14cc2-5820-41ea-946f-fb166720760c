.tab-input {
    padding-right: 10px;
}

#left-nav {
    width: 100%;
    height: 100%
}

.input-label {
    padding-left: 1pt;
    padding-right: 0;
    text-align: center;
}

.tab-child {
    padding-right: 0;
    padding-left: 0;
}

.search-input {
    width: 60%;
}

.layui-select-title {
    /*padding-right: 10px;*/
    padding-right: 0;
}

.layui-input-inline {
    width: 60% !important;
}

.tab-layui-form-item {
    margin-bottom: 0;
}
.data-tab {
    padding: 0;
    padding-top: 10px
}

.search-label {
    width: 65px !important;
}

.info-list-btn{
    width: 100px;
    height: 100px;
    padding: 0;
}


/*数据面板*/
.compact-data-module{
    font-family: 'Helvetica Neue', Arial, sans-serif;
    /*max-width: 900px;*/
    margin-left: 20px;
    margin-top: 20px;
    width: 260px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    font-size: 13px;
}
.module-header{
    padding: 5px 15px;
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    color: white;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.module-body{
    padding: 0 12px 12px 15px;
}
.data-grid{
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}
.data-label{
    color: #666;
    font-size: 12px;
    margin-bottom: 2px;
}
.data-value{
    font-size: 13px;
    font-weight: bold;
    color: #333;
    display: flex;
    align-items: center;
}
.value-unit{
    font-size: 11px;
    color: #999;
    margin-left: 2px;
    font-weight: normal;
}
.status-indicator{
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}
.status-normal{
    background: #52c41a;
}
.status-warning{
    background: #faad14;
}
.status-danger{
    background: #f5222d;
}
.location-info{
    grid-column: span 2;
    padding-bottom: 8px;
    border-bottom: 1px dashed #eee;
    margin-bottom: 8px;
}
.updata-time{
    font-size: 11px;
    color: #888;
    text-align: right;
    margin-top: 5px;
}
.card-ip{
    font-size: 8px;
    color: #888;
    text-align: left;
    margin-top: 3px;
    padding-left: 15px;
    padding-right: 15px;
}

/*右侧tab页面滚动*/
.scrollable-tab{
    display: flex;
    flex-direction: column;
    height: 100vh;
    margin-top: 0;
}


.layui-tab-content {
    flex: 1;
    overflow: auto;
    padding: 15px;
}

/*.data-tab {*/
/*    flex: 1;*/
/*    overflow: auto;*/
/*    padding: 15px;*/
/*}*/

#card_page_index {
    text-align: center;
    margin-top: 15px;
}

.card-page {
    height: 426px;
}
#crane_card_page_index{
    text-align: center;
    margin-top: 15px;
}
.crane-card-page{
    height: 426px;
}
body {
    /*overflow: hidden;*/
}

