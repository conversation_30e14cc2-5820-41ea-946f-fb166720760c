* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

body {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #0a0a0a;
    position: relative;
    overflow: hidden;
}

/* 主背景渐变 */
body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.4) 0%, transparent 50%),
        linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    25% {
        background-position: 100% 0%;
    }
    50% {
        background-position: 100% 100%;
    }
    75% {
        background-position: 0% 100%;
    }
}

/* 动态网格背景 */
body::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(102, 126, 234, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(102, 126, 234, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 15s linear infinite;
    pointer-events: none;
}

@keyframes gridMove {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(50px, 50px);
    }
}

/* 浮动粒子效果 */
.particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #667eea;
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
    animation-duration: 8s;
}

.particle:nth-child(2) {
    left: 20%;
    animation-delay: 1s;
    animation-duration: 10s;
}

.particle:nth-child(3) {
    left: 30%;
    animation-delay: 2s;
    animation-duration: 7s;
}

.particle:nth-child(4) {
    left: 40%;
    animation-delay: 3s;
    animation-duration: 9s;
}

.particle:nth-child(5) {
    left: 50%;
    animation-delay: 4s;
    animation-duration: 6s;
}

.particle:nth-child(6) {
    left: 60%;
    animation-delay: 5s;
    animation-duration: 8s;
}

.particle:nth-child(7) {
    left: 70%;
    animation-delay: 6s;
    animation-duration: 10s;
}

.particle:nth-child(8) {
    left: 80%;
    animation-delay: 7s;
    animation-duration: 7s;
}

.particle:nth-child(9) {
    left: 90%;
    animation-delay: 8s;
    animation-duration: 9s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(100vh) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) scale(1);
        opacity: 0;
    }
}

/* 光效装饰 */
.light-effect {
    position: absolute;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: lightMove 12s ease-in-out infinite;
}

.light-effect:nth-child(1) {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.light-effect:nth-child(2) {
    top: 60%;
    right: 10%;
    animation-delay: 4s;
}

.light-effect:nth-child(3) {
    bottom: 20%;
    left: 20%;
    animation-delay: 8s;
}

@keyframes lightMove {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.5) rotate(180deg);
        opacity: 0.6;
    }
}

.login-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    width: 100%;
    max-width: 450px;
    transform: translateY(-5%);
    position: relative;
    z-index: 10;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 容器发光边框 */
.login-container::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #667eea, #764ba2, #4c51bf, #4338ca, #667eea);
    border-radius: 22px;
    z-index: -1;
    opacity: 0.5;
    animation: borderGlow 3s ease-in-out infinite;
    background-size: 400% 400%;
}

@keyframes borderGlow {
    0%, 100% {
        background-position: 0% 50%;
        opacity: 0.5;
    }
    50% {
        background-position: 100% 50%;
        opacity: 0.8;
    }
}

/* 容器内部光效 */
.login-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.1) 0%, 
        transparent 50%, 
        rgba(255, 255, 255, 0.05) 100%);
    border-radius: 20px;
    pointer-events: none;
}

.login-header {
    text-align: center;
    margin-bottom: 2.5rem;
    position: relative;
}

.login-header h1 {
    color: #ffffff;
    font-size: 2.2rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2, #4c51bf);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 8px rgba(102, 126, 234, 0.2);
    animation: titleGlow 4s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% {
        filter: drop-shadow(0 0 3px rgba(102, 126, 234, 0.15));
    }
    100% {
        filter: drop-shadow(0 0 6px rgba(102, 126, 234, 0.25));
    }
}

.login-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    opacity: 0.8;
}

.form-group {
    margin-bottom: 1.8rem;
    position: relative;
}

.form-control {
    width: 100%;
    padding: 1rem 1.2rem 1rem 3rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: #ffffff;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
    transition: color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 
        0 0 0 4px rgba(102, 126, 234, 0.2),
        0 8px 25px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.15);
}

.form-control:focus::placeholder {
    color: rgba(255, 255, 255, 0.8);
}

.btn-login {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-login::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.btn-login:hover::before {
    left: 100%;
}

.btn-login:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #764ba2, #667eea);
}

.btn-login:active {
    transform: translateY(-1px);
}

.form-footer {
    text-align: center;
    margin-top: 2rem;
    color: rgba(255, 255, 255, 0.6);
}

.form-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.form-footer a:hover {
    color: #764ba2;
}

/* 输入框图标 */
.form-group::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.6;
    transition: opacity 0.3s ease;
    filter: brightness(0) invert(1);
}

.form-group:focus-within::before {
    opacity: 1;
}

/* 用户名输入框图标 */
.form-group:first-of-type::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

/* 密码输入框图标 */
.form-group:last-of-type::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z'/%3E%3C/svg%3E");
}

/* 错误消息样式 */
.alert-error {
    background: rgba(220, 53, 69, 0.2);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #ff6b6b;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(10px);
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 加载动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-container {
    animation: fadeInUp 0.8s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .login-container {
        margin: 20px;
        padding: 2rem;
        transform: none;
    }
    
    .login-header h1 {
        font-size: 1.8rem;
    }
    
    .form-control {
        padding: 0.9rem 1rem 0.9rem 2.5rem;
    }
    
    .btn-login {
        padding: 0.9rem;
    }
    
    .particle {
        display: none;
    }
}

/* 添加扫描线效果 */
.scan-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #667eea, transparent);
    animation: scan 3s linear infinite;
    opacity: 0.5;
}

@keyframes scan {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(100vh);
    }
}
