// // 侧边栏收纳与展开
// const sidebar = document.getElementById('sidebar');
// const topbar = document.getElementById('topbar');
// const content = document.getElementById('content');
// const menuToggle = document.getElementById('menu-toggle');
// const pageTitle = document.getElementById('page-title');

// menuToggle.addEventListener('click', () => {
//   sidebar.classList.toggle('collapsed');
//   topbar.classList.toggle('collapsed');
//   content.classList.toggle('collapsed');
// });

// // 页面切换与高亮显示
// const navLinks = document.querySelectorAll('.sidebar ul li a');
// const pages = document.querySelectorAll('.content .card');

// navLinks.forEach(link => {
//   link.addEventListener('click', (e) => {
//     e.preventDefault();
//     navLinks.forEach(link => link.classList.remove('active'));
//     link.classList.add('active');
//     pageTitle.textContent = link.querySelector('span').textContent;
//     const targetPage = link.getAttribute('data-page');
//     pages.forEach(page => {
//       page.style.display = page.id === `${targetPage}-page` ? 'block' : 'none';
//     });
//   });
// }); 

// // 添加分组筛选功能
// document.getElementById('group-filter').addEventListener('change', function() {
//   const group = this.value;
//   const rows = document.querySelectorAll('#users-page tr[data-group]');

//   rows.forEach(row => {
//     if (group === 'all' || row.dataset.group === group) {
//       row.style.display = 'table-row';
//     } else {
//       row.style.display = 'none';
//     }
//   });
// });





