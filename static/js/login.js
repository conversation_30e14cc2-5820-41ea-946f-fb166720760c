class AuthManager {
    constructor() {
        this.form = document.getElementById('loginForm');
        this.errorEl = document.getElementById('error-message');
        this.initEventListeners();
    }

    initEventListeners() {
        this.form.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleLogin();
        });
    }

    async handleLogin() {
        this.clearErrors();

        const credentials = {
            username: this.form.username.value.trim(),
            password: this.form.password.value
        };

        try {
            const response = await fetch('/web/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin',
                body: JSON.stringify(credentials)
            });

            const result = await response.json();

            if (result.success) {
                window.location.href = result.redirect;
            } else {
                this.showError(result.error || '登录失败，请检查账号或密码是否正确');
            }
        } catch (error) {
            console.log(error)
            this.showError('网络连接异常，请检查网络后重试');
        }
    }

    clearErrors() {
        this.errorEl.textContent = '';
        this.errorEl.style.display = 'none';
    }

    showError(message) {
        this.errorEl.textContent = message;
        this.errorEl.style.display = 'block';
        this.form.password.value = '';
        this.form.password.focus();
    }
}

// 初始化登录管理器
document.addEventListener('DOMContentLoaded', () => {
    new AuthManager();
});
