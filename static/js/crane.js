
document.addEventListener("DOMContentLoaded", function () {
// 起重机表格初始化
    export function init_crane_table() {
        layui.use('table', function () {
            let AirCondition = layui.table;
            AirCondition.render({
                elem: '#crane',
                id: 'crane',
                // {#height: 100%,#}
                url: '/device/AirConditionData',
                limits: [10, 15, 20, 30, 50],
                page: true,
                cols: [[
                    {type: 'checkbox'},
                    {
                        field: 'device_name',
                        title: "名称",
                        width: 120,
                        sort: true,
                        align: 'center'
                    },
                    {
                        field: 'department',
                        title: "起重机型号",
                        width: 120,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'position',
                        title: "起重机点位",
                        width: 120,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'ip',
                        title: "IP",
                        width: 150,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'port',
                        title: "端口",
                        width: 80,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'port1',
                        title: "xxx传感器",
                        width: 80,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'port2',
                        title: "xxx传感器",
                        width: 80,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'state',
                        title: "状态",
                        width: 80,
                        sort: false,
                        align: 'center'
                    },
                    {
                        field: 'update_time',
                        title: "上次更新时间",
                        width: 150,
                        sort: true,
                        align: 'center'
                    },
                    {
                        field: 'creat_time',
                        title: "创建时间",
                        width: 150,
                        sort: true,
                        align: 'center'
                    },
                    {
                        field: 'creat_role',
                        title: "创建用户",
                        width: 120,
                        sort: true,
                        align: 'center'
                    },
                    {
                        field: 'operation',
                        title: "操作",
                        toolbar: '#op-btn',
                        width: 120,
                        sort: false, align: 'center'
                    },
                ]],
            })
        });
    }

})