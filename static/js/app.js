// 在文档就绪函数外部定义用户组映射
const userGroups = {
    admin: '管理组',
    vip: 'VIP用户',
    normal: '普通用户'
};

// const my_url = my_url  + ''

const my_url = 'https://sy.giaonice.online'



// 添加数据缓存对象
const dataCache = {
    vehicle: null,
    meal: null
};

$(document).ready(function () {
    // 隐藏所有页面
    $('.content .card').hide();
    // 默认显示仪表盘
    $('#dashboard-page').show();
    $('#page-title').text('仪表盘');
    // 激活仪表盘菜单项
    $('.sidebar a').removeClass('active');
    $('.sidebar a[data-page="dashboard"]').addClass('active');

    // 初始化图表数据
    loadDashboardData();

    // 侧边栏收纳与展开
    const $sidebar = $('#sidebar');
    const $topbar = $('#topbar');
    const $content = $('#content');

    $('#menu-toggle').click(function () {
        $sidebar.add($topbar).add($content).toggleClass('collapsed');
    });

    // 页面切换与高亮显示
    $('.sidebar a[data-page]').click(async function (e) {
        e.preventDefault();
        const $link = $(this);
        const targetPage = $link.data('page');

        // 清空搜索框和搜索结果
        clearSearchOnPageChange();

        // 如果正在离开数据页面，重置多选状态
        if ($('#data-page').is(':visible')) {
            resetMultiSelect();
        }

        // 移除所有active状态
        $('.sidebar a').removeClass('active');
        $link.addClass('active');

        // 隐藏所有页面
        $('.content .card').hide();
        $(`#${targetPage}-page`).show();
        $('#page-title').text($link.find('span').text());

        // 仅当切换到用户管理时加载用户数据
        if (targetPage === 'users') {
            try {
                // 显示加载状态
                $('#users-page .table-container').addClass('loading');

                // 使用jQuery AJAX发送POST请求
                const response = await $.ajax({
                    url: my_url + '/html/user_list',
                    method: 'POST',
                    dataType: 'json',
                    contentType: 'application/json'
                });

                // 清空现有数据
                const $tbody = $('#users-page tbody').empty();

                // 遍历数据生成表格行
                $.each(response.data || [], function (index, user) {
                    var meal
                    var car
                    if (user.meal_push == 1) {
                        meal = 'checked';
                    }
                    if (user.car_push == 1) {
                        car = 'checked';
                    }
                    if (user.uid == undefined || user.uid == null) {
                        user.uid = '未订阅消息通知功能';
                    }
                    $tbody.append(`
            <tr data-id="${user.id}" data-group="${user.group}" data-openid="${user.openid}">
              <td>${user.user}</td>
              <td>${user.openid}</td>
              <td>${user.uid}</td>
              <td>${user.register}</td>
              <td>
                <label class="switch">
                  <input type="checkbox" class="meal-push" data-userid="${user.id}" ${meal} id="meal-push">
                  <span class="slider"  ></span>
                </label>
              </td>
              <td>
                <label class="switch">
                  <input type="checkbox" class="car-push" data-userid="${user.id}" ${car} id="car-push">
                  <span class="slider" ></span>
                </label>
              </td>
              <td>
                <button class="btn delete-btn" data-action="delete">
                  <i class="fas fa-trash"></i>
                </button>
              </td>
            </tr>
          `);
                });

                showToast('已加载' + (response.data?.length || 0) + '条用户数据');
            } catch (error) {
                console.error('数据加载失败:', error);
                showToast('数据加载失败: ' + (error.responseJSON?.message || error.statusText));
            } finally {
                $('#users-page .table-container').removeClass('loading');
            }
        }

        // 新增数据管理页面加载
        if (targetPage === 'data') {
            try {
                // 显示加载状态
                $('#data-page .table-container').addClass('loading');
                dataCache.vehicle = null;
                // 请求留餐数据
                const response = await $.ajax({
                    url: my_url + '/html/meal_record',
                    method: 'POST',
                    dataType: 'json',
                    contentType: 'application/json'
                });

                if (response.state === 'ok') {
                    const $tbody = $('#meal-data-table tbody').empty();

                    response.data.forEach(item => {
                        $tbody.append(`
                            <tr>  
                                <td>${item.user}</td> 
                                <td>${item.meal_num}</td>
                                <td>${item.meal_time}</td>
                                <td>${item.sub_time}</td>
                                <td>
                                    <button class="btn delete-btn"><i class="fas fa-trash"></i></button>
                                </td>
                            </tr>
                        `);
                    });

                    showToast(`已加载 ${response.data.length} 条留餐记录`);
                }
            } catch (error) {
                console.error('数据加载失败:', error);
                showToast('数据加载失败: ' + (error.responseJSON?.message || error.statusText));
            } finally {
                $('#data-page .table-container').removeClass('loading');
            }
        }

        if (targetPage === 'orders') {
            $('#orders-page .meal-table').show();
            $('#orders-page .vehicle-table').hide();
        }

        // 当切换到仪表盘时初始化图表
        if (targetPage === 'dashboard') {
            setTimeout(() => {

            }, 50);
        }
    });

    // 事件委托处理推送按钮状态变化
    $('#users-page').off('change', '.meal-push, .car-push').on('change', '.meal-push, .car-push', handlePushChange);



    // 数据管理选项卡切换
    $('#data-page .tabs button').click(function () {
        const targetType = $(this).data('type');
        $(this).addClass('active').siblings().removeClass('active');
        $(`.${targetType}-table`).show().siblings('.table-container').hide();

        // 重置多选状态
        resetMultiSelect();

        // 获取当前搜索框的值
        const searchText = $('.search-box').val().trim();

        // 如果搜索框有内容，对当前显示的表格进行搜索
        if (searchText) {
            if (targetType === 'vehicle') {
                filterTable(searchText, 'vehicle-data-table');
            } else {
                filterTable(searchText, 'meal-data-table');
            }
        }

        // 只在数据未缓存时加载用车数据
        if (targetType === 'vehicle' && !dataCache.vehicle) {
            $('#data-page .table-container').addClass('loading');
            loadVehicleData().then(() => {
                $('#data-page .table-container').removeClass('loading');
            });
        }
    }).first().click();

    // 初始化用户搜索（带防抖）
    initUserSearch();

    // 在文档就绪时初始化
    initGlobalSearch();

    // 修改删除按钮处理逻辑
    $('#data-page').on('click', '.delete-btn', async function () {
        console.log('删除按钮被点击'); // 调试日志1
        const $row = $(this).closest('tr');
        console.log('当前行数据:', $row.html()); // 调试日志2
        const dataType = $row.closest('table').hasClass('meal-table') ? 'meal' : 'car';
        if (dataType == 'car') {
            var subTime = $row.find('td:eq(5)').text();
        } else {
            var subTime = $row.find('td:eq(3)').text();  // 假设提交时间在第5列
        }

        if (!confirm('确定要删除这条记录吗？')) return;

        try {
            const response = await $.ajax({
                url: '/html/delete_record',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    type: dataType,
                    sub_time: subTime
                })
            });

            if (response.state === 'ok') {
                $row.remove();
                showToast('删除成功');
            }
        } catch (error) {
            console.error('删除失败:', error);
            console.log('发送请求参数:', {  // 调试日志3
                record_type: dataType,
                submission_time: subTime
            });
            showToast('删除失败: ' + (error.responseJSON?.message || error.statusText));
        }
    });

    // 初始化搜索功能
    initSearch();

    // 添加多选相关功能
    initMultiSelect();

    // 移动端检测
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile) {
        // 初始化时收起侧边栏
        $('#sidebar').addClass('collapsed');
        $('#topbar').addClass('collapsed');
        $('#content').addClass('collapsed');

        // 添加遮罩层（如果HTML中还没有）
        if (!$('.sidebar-overlay').length) {
            $('body').append('<div class="sidebar-overlay"></div>');
        }

        // 菜单按钮点击事件
        $('#menu-toggle').off('click').on('click', function (e) {
            e.preventDefault();
            const $sidebar = $('#sidebar');
            const $overlay = $('.sidebar-overlay');

            if ($sidebar.hasClass('expanded')) {
                // 收起侧边栏
                $sidebar.removeClass('expanded');
                $overlay.removeClass('active');
                $('body').css('overflow', '');
            } else {
                // 展开侧边栏
                $sidebar.addClass('expanded');
                $overlay.addClass('active');
                $('body').css('overflow', 'hidden'); // 防止背景滚动
            }
        });

        // 遮罩层点击事件
        $('.sidebar-overlay').on('click', function () {
            $('#sidebar').removeClass('expanded');
            $(this).removeClass('active');
            $('body').css('overflow', '');
        });

        // 侧边栏菜单项点击事件
        $('.sidebar a').on('click', function () {
            if (isMobile) {
                // 点击菜单项后收起侧边栏
                $('#sidebar').removeClass('expanded');
                $('.sidebar-overlay').removeClass('active');
                $('body').css('overflow', '');
            }
        });
    }

    // 窗口大小改变时的处理
    $(window).on('resize', function () {
        const isMobileView = window.innerWidth <= 768;
        if (!isMobileView) {
            // 如果切换到桌面视图，移除移动端相关类
            $('#sidebar').removeClass('expanded');
            $('.sidebar-overlay').removeClass('active');
            $('body').css('overflow', '');
        }
    });

    // 修改下载按钮点击事件
    $('.download-btn').click(async function () {
        // 获取当前显示的表格
        const $activeTable = $('.data-table:visible');
        const tableId = $activeTable.attr('id');
        const isVehicleTable = tableId === 'vehicle-data-table';

        try {
            // 创建工作簿
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet(isVehicleTable ? '用车记录' : '留餐记录');

            // 设置表头
            const headers = isVehicleTable ?
                ['申请人', '用车人数', '用车地点', '用车事由/工号', '用车日期段', '提交时间'] :
                ['申请人', '餐数', '留餐时间', '提交时间'];

            // 添加表头行
            worksheet.addRow(headers);

            // 设置表头样式
            const headerRow = worksheet.getRow(1);
            headerRow.eachCell((cell) => {
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: {argb: '4F81BD'}
                };
                cell.font = {
                    bold: true,
                    color: {argb: 'FFFFFF'}
                };
                cell.alignment = {
                    vertical: 'middle',
                    horizontal: 'center'
                };
            });
            headerRow.height = 25;

            // 收集并添加数据行
            $activeTable.find('tbody tr:visible').each(function () {
                const rowData = [];
                const startIndex = $(this).find('.select-column').length;
                $(this).find('td').slice(startIndex, -1).each(function () {
                    const fullText = $(this).attr('data-fulltext');
                    rowData.push(fullText || $(this).text().trim());
                });
                worksheet.addRow(rowData);
            });

            // 设置数据行样式
            worksheet.eachRow((row, rowNumber) => {
                if (rowNumber > 1) { // 跳过表头
                    row.height = 20;
                    row.eachCell((cell) => {
                        cell.alignment = {
                            vertical: 'middle',
                            horizontal: 'center'
                        };
                        // 设置斑马纹
                        if (rowNumber % 2 === 0) {
                            cell.fill = {
                                type: 'pattern',
                                pattern: 'solid',
                                fgColor: {argb: 'F2F2F2'}
                            };
                        }
                    });
                }
            });

            // 设置列宽
            const colWidths = isVehicleTable ?
                [15, 12, 20, 30, 15, 20] :
                [15, 10, 20, 20];

            colWidths.forEach((width, i) => {
                worksheet.getColumn(i + 1).width = width;
            });

            // 冻结首行
            worksheet.views = [
                {state: 'frozen', xSplit: 0, ySplit: 1, topLeftCell: 'A2', activeCell: 'A2'}
            ];

            // 生成文件名
            const now = new Date();
            const timestamp = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}`;
            const filename = `${isVehicleTable ? '用车记录' : '留餐记录'}_${timestamp}.xlsx`;

            // 导出文件
            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            window.URL.revokeObjectURL(url);

            showToast('导出成功');
        } catch (error) {
            console.error('导出失败:', error);
            showToast('导出失败: ' + error.message);
        }
    });

    // 在用户管理页面添加操作栏
    $('#users-page .table-container').before(`
        <div class="action-bar">
            <div class="right-actions">
                <button class="btn download-btn" id="users-download-btn">
                    <i class="fas fa-download"></i> 下载数据
                </button>
            </div>
        </div>
    `);

    // 用户数据下载按钮点击事件
    $('#users-download-btn').click(async function () {
        try {
            // 创建工作簿
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('用户数据');

            // 设置表头
            const headers = ['用户名', 'OPENID', '推送UID', '注册时间', '留餐推送', '用车推送'];
            worksheet.addRow(headers);

            // 设置表头样式
            const headerRow = worksheet.getRow(1);
            headerRow.eachCell((cell) => {
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: {argb: '4F81BD'}
                };
                cell.font = {
                    bold: true,
                    color: {argb: 'FFFFFF'}
                };
                cell.alignment = {
                    vertical: 'middle',
                    horizontal: 'center'
                };
            });
            headerRow.height = 25;

            // 收集并添加数据行
            $('#users-table tbody tr:visible').each(function () {
                const rowData = [
                    $(this).find('td:eq(0)').text(), // 用户名
                    $(this).find('td:eq(1)').text(), // OPENID
                    $(this).find('td:eq(2)').text(), // 推送UID
                    $(this).find('td:eq(3)').text(), // 注册时间
                    $(this).find('td:eq(4) input').prop('checked') ? '是' : '否', // 留餐推送
                    $(this).find('td:eq(5) input').prop('checked') ? '是' : '否'  // 用车推送
                ];
                worksheet.addRow(rowData);
            });

            // 设置数据行样式
            worksheet.eachRow((row, rowNumber) => {
                if (rowNumber > 1) { // 跳过表头
                    row.height = 20;
                    row.eachCell((cell) => {
                        cell.alignment = {
                            vertical: 'middle',
                            horizontal: 'center'
                        };
                        // 设置斑马纹
                        if (rowNumber % 2 === 0) {
                            cell.fill = {
                                type: 'pattern',
                                pattern: 'solid',
                                fgColor: {argb: 'F2F2F2'}
                            };
                        }
                    });
                }
            });

            // 设置列宽
            const colWidths = [15, 30, 30, 20, 12, 12];
            colWidths.forEach((width, i) => {
                worksheet.getColumn(i + 1).width = width;
            });

            // 冻结首行
            worksheet.views = [
                {state: 'frozen', xSplit: 0, ySplit: 1, topLeftCell: 'A2', activeCell: 'A2'}
            ];

            // 生成文件名
            const now = new Date();
            const timestamp = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}`;
            const filename = `用户数据_${timestamp}.xlsx`;

            // 导出文件
            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            window.URL.revokeObjectURL(url);

            showToast('导出成功');
        } catch (error) {
            console.error('导出失败:', error);
            showToast('导出失败: ' + error.message);
        }
    });

    // 添加用户删除功能
    $('#users-page').on('click', '[data-action]', async function () {
        const action = $(this).data('action');
        const $row = $(this).closest('tr');
        const openid = $row.data('openid'); // 从行数据获取openid
        try {
            if (action === 'delete') {
                if (!confirm('确定要删除该用户吗？')) return;

                const response = await $.ajax({
                    url: 'http://139.224.228.11:5003/html/delete',
                    url: my_url + '/html/delete',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        openid: openid,
                    })
                });
                console.log(response)
                if (response.state == 'ok') {
                    $row.remove();
                    showToast('用户删除成功');
                }
            }
        } catch (error) {
            console.error('操作失败:', error);
            showToast('操作失败，请稍后重试');
        }
    });
});

// 添加简单的通知功能
function showToast(message) {
    const $toast = $(`<div class="toast">${message}</div>`);
    $('body').append($toast);
    setTimeout(() => $toast.remove(), 3000);
}

// 初始化用户搜索（带防抖）
function initUserSearch() {
    const $searchInput = $('.search-box');
    let timeoutId;
    let lastSearch = '';

    $searchInput.on('input', function () {
        const searchTerm = $(this).val().trim();
        if (searchTerm === lastSearch) return;

        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
            filterUsers(searchTerm);
            lastSearch = searchTerm;
        }, 200);
    });

    // 添加清除按钮
    $searchInput.wrap('<div class="search-wrapper"></div>');
    $searchInput.after('<button class="clear-search">&times;</button>');

    $('.clear-search').click(function () {
        $searchInput.val('').trigger('input');
        $(this).hide();
    });

    $searchInput.on('input', function () {
        $('.clear-search').toggle(!!$(this).val());
    });
}

// 优化后的筛选函数
function filterUsers(searchTerm) {
    const $rows = $('#users-page tbody tr');
    const hasSearch = searchTerm.length > 0;
    let matchCount = 0;

    $rows.each(function () {
        const $row = $(this);
        const $cells = $row.find('td:lt(3)'); // 搜索前3列（用户名、openid、uid）
        let isMatch = false;

        $cells.each(function () {
            const $cell = $(this);
            const originalText = $cell.data('original-text') || $cell.text();
            $cell.data('original-text', originalText);

            if (hasSearch) {
                const regex = new RegExp(escapeRegExp(searchTerm), 'gi');
                const highlighted = originalText.replace(regex, '<mark>$&</mark>');
                isMatch = isMatch || regex.test(originalText);
                $cell.html(highlighted);
            } else {
                $cell.text(originalText);
            }
        });

        $row.toggleClass('highlight-row', isMatch);
        $row.toggle(hasSearch ? isMatch : true);
        if (isMatch) matchCount++;
    });

    showSearchResultTip(matchCount, searchTerm);
}

// 显示搜索结果提示
function showSearchResultTip(count, term) {
    const $tip = $('#search-tip');
    if (term) {
        $tip.html(`找到 <strong>${count}</strong> 条匹配 "<span class="highlight">${term}</span>" 的结果`);
        $tip.fadeIn(200);
    } else {
        $tip.fadeOut(200);
    }
}

// 转义正则特殊字符
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// 更新推送处理函数
async function handlePushChange(event) {
    const $checkbox = $(event.currentTarget);
    const pushType = $checkbox.hasClass('meal-push') ? 'meal_push' : 'car_push';
    const isActive = $checkbox.prop('checked');
    const $row = $checkbox.closest('tr');
    const openid = $row.data('openid');

    try {
        const response = await $.ajax({
            url: my_url + '/notice/notice',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                openid: openid,
                type: pushType,
                status: isActive ? 1 : 0
            })
        });

        if (response.state === 'ok') {
            showToast(`${pushType === 'meal' ? '留餐' : '用车'}推送已${isActive ? '启用' : '关闭'}`);
        } else {
            $checkbox.prop('checked', !isActive); // 状态回滚
            showToast('更新失败: ' + (response.message || '未知错误'));
        }
    } catch (error) {
        console.error('更新推送状态失败:', error);
        $checkbox.prop('checked', !isActive); // 状态回滚
        showToast('更新失败: ' + (error.responseJSON?.message || error.statusText));
    }
}

// 修改初始化全局搜索函数
function initGlobalSearch() {
    const $globalSearch = $('.topbar .search-box'); // 确保选择器正确指向顶部搜索框
    let timeoutId;

    $globalSearch.off('input').on('input', function () {
        const term = $(this).val().trim();
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
            // 同时搜索用户表和数据表
            filterUsers(term);
            filterTable(term, 'meal-data-table');
            filterTable(term, 'vehicle-data-table');
        }, 300);
    });
}

// 修改通用筛选函数以支持多表搜索
function filterTable(searchTerm, tableId) {
    const $rows = $(`#${tableId} tbody tr`);
    const searchLower = searchTerm.toLowerCase();
    let matchCount = 0;

    // 扩展列配置以支持更多表格
    const columnMap = {
        'users-table': [0, 3],     // 用户表搜索用户名、注册时间
        'meal-data-table': [0, 2, 3],    // 留餐表搜索用户、留餐时间、提交时间
        'vehicle-data-table': [0, 2, 3, 5]  // 用车表搜索用户、用车事由、提交时间
    };

    console.log('当前表格:', tableId);
    console.log('搜索列:', columnMap[tableId]);
    console.log('首行数据:', $rows.first().find('td').map((i, td) => `${i}:${td.textContent}`).get());

    $rows.each(function () {
        const $row = $(this);
        const columns = columnMap[tableId] || [0];
        let isMatch = false;

        columns.forEach(colIndex => {
            const $cell = $row.find(`td:eq(${colIndex})`);
            const originalText = ($cell.data('original-text') || $cell.text()).toLowerCase();
            $cell.data('original-text', originalText);

            if (searchTerm) {
                const regex = new RegExp(escapeRegExp(searchTerm), 'gi');
                isMatch = isMatch || regex.test(originalText);
                $cell.html(originalText.replace(regex, '<mark>$&</mark>'));
            } else {
                $cell.text(originalText);
            }
        });

        $row.toggleClass('highlight-row', isMatch);
        $row.toggle(searchTerm ? isMatch : true);
        if (isMatch) matchCount++;
    });

    showSearchResultTip(matchCount, searchTerm, tableId);
}

// 修改结果提示函数以支持多表
function showSearchResultTip(count, term, tableId) {
    const tableNames = {
        'users-table': '用户',
        'meal-data-table': '留餐记录',
        'vehicle-data-table': '用车记录'
    };

    const $tip = $('#search-tip');
    if (term) {
        $tip.html(`在${tableNames[tableId]}中找到 <strong>${count}</strong> 条匹配 "<span class="highlight">${term}</span>" 的结果`);
        $tip.fadeIn(200);
    } else {
        $tip.fadeOut(200);
    }
}

// 修改用车数据加载函数
async function loadVehicleData() {
    // 如果已有缓存数据，直接返回
    if (dataCache.vehicle) {
        return;
    }

    try {
        const response = await $.ajax({
            url: my_url + '/html/car_record',
            method: 'POST',
            dataType: 'json',
            contentType: 'application/json'
        });

        if (response.state === 'ok') {
            // 缓存数据
            dataCache.vehicle = response.data;

            const $tbody = $('#vehicle-data-table tbody').empty();

            response.data.forEach(item => {
                $tbody.append(`
                    <tr>
                        <td>${item.user}</td>
                        <td>${item.num}</td>
                        <td>${item.place}</td>
                        <td data-fulltext="${item.reason}">${truncateText(item.reason, 20)}</td>
                        <td>${item.date}</td>
                        <td>${item.sub_time}</td>
                        <td>
                            <button class="btn delete-btn"><i class="fas fa-trash"></i></button>
                        </td>
                    </tr>
                `);
            });
            // 获取当前搜索框的值
            const searchText = $('.search-box').val().trim();

            // 如果搜索框有内容，对当前显示的表格进行搜索
            if (searchText) {

                filterTable(searchText, 'vehicle-data-table');

            }
        }
    } catch (error) {
        console.error('用车数据加载失败:', error);
        showToast('用车数据加载失败');
    }
}

// 添加刷新数据的方法（如果需要手动刷新）
function refreshVehicleData() {
    dataCache.vehicle = null;
    return loadVehicleData();
}

// 添加文字截断函数
function truncateText(text, maxLength) {
    return text.length > maxLength ? text.substr(0, maxLength) + '...' : text;
}

// 新增仪表盘数据请求函数
async function loadDashboardData() {
    try {
        const [userGrowth, reportTypes] = await Promise.all([
            $.ajax({
                url: my_url + '/html/user_growth',
                method: 'POST',
                dataType: 'json'
            }),
            $.ajax({
                url: my_url + '/html/report_types',
                method: 'POST',
                dataType: 'json'
            })
        ]);
        console.log(userGrowth, reportTypes)
        if (userGrowth.state === 'ok' && reportTypes.state === 'ok') {
            updateCharts(userGrowth.data, reportTypes.data);
        }
    } catch (error) {
        console.error('仪表盘数据加载失败:', error);
        showToast('数据加载失败');
    }
}

// 修改图表初始化函数
function updateCharts(growthData, typeData) {
    // 用户增长图表
    initGrowthChart(growthData);

    // 报备类型图表
    const typeChart = echarts.init(document.getElementById('reportTypeChart'));
    typeChart.setOption({
        dataset: {source: typeData},
        series: [{
            type: 'pie',
            radius: '65%',
            encode: {value: 'count', itemName: 'type'}
        }]
    });
}


// 修改后的图表初始化代码
function initGrowthChart(data) {
    const chart = echarts.init(document.getElementById('userGrowthChart'));

    const option = {
        title: {text: '用户增长趋势', left: 'center'},
        tooltip: {trigger: 'axis'},
        xAxis: {
            type: 'category',
            data: data.dates,
            axisLabel: {rotate: 45}
        },
        yAxis: {
            type: 'value',
            axisLabel: {formatter: '{value} 人'}
        },
        series: [{
            name: '用户数',
            type: 'line',
            smooth: true,
            data: data.values,
            itemStyle: {color: '#4CAF50'},
            areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {offset: 0, color: 'rgba(76, 175, 80, 0.3)'},
                    {offset: 1, color: 'rgba(76, 175, 80, 0)'}
                ])
            }
        }],
        grid: {
            left: '10%',
            right: '5%',
            bottom: '20%',
            containLabel: true
        }
    };

    try {
        chart.setOption(option);
    } catch (error) {
        console.error('图表配置错误:', error);
        chart.dispose();
        document.getElementById('userGrowthChart').innerHTML =
            `<div class="chart-error">图表初始化失败：${error.message}</div>`;
    }
}

// 清空搜索框和结果的函数
function clearSearchOnPageChange() {
    const $searchBox = $('.search-box');
    $searchBox.val('');
    clearSearch();
    $('.clear-search').hide();
}

// 修改搜索框初始化函数
function initSearch() {
    const $searchBox = $('.search-box');
    const $clearButton = $('<button class="clear-search" type="button">&times;</button>');
    const $searchTip = $('<div class="search-tip"></div>');

    // 添加清除按钮和搜索提示到DOM
    $('.search-container').append($clearButton).after($searchTip);

    // 移除搜索图标
    $('.search-container').removeClass('has-search-icon');

    // 搜索框输入事件
    let searchTimeout;
    $searchBox.on('input', function () {
        const searchText = $(this).val().trim();

        // 显示/隐藏清除按钮
        $clearButton.toggle(!!searchText);

        // 防抖处理
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            if (searchText) {
                // 根据当前激活的选项卡确定要搜索的表格
                const activeTab = $('#data-page .tabs button.active').data('type');
                if (activeTab === 'vehicle') {
                    filterTable(searchText, 'vehicle-data-table');
                } else {
                    filterTable(searchText, 'meal-data-table');
                }
            } else {
                clearSearch();
            }
        }, 300);
    });

    // 清除按钮点击事件
    $clearButton.on('click', function () {
        $searchBox.val('').focus();
        clearSearch();
        $(this).hide();
    });
}

// 执行搜索
function performSearch(searchText) {
    const currentPage = $('.sidebar a.active').data('page');
    let matchCount = 0;

    switch (currentPage) {
        case 'users':
            matchCount = searchUsers(searchText);
            break;
        case 'data':
            matchCount = searchData(searchText);
            break;
        // 可以添加其他页面的搜索逻辑
    }

    updateSearchTip(matchCount, searchText);
}

// 清除搜索结果
function clearSearch() {
    $('.search-tip').hide();
    // 重置表格显示
    $('.data-table tbody tr').show();
    // 清除高亮
    $('.data-table td').each(function () {
        const originalText = $(this).data('original-text');
        if (originalText) {
            $(this).text(originalText);
        }
    });
}

// 更新搜索提示
function updateSearchTip(count, searchText) {
    const $tip = $('.search-tip');
    if (count > 0) {
        $tip.html(`找到 <strong>${count}</strong> 条包含 "<span class="highlight">${searchText}</span>" 的结果`).show();
    } else {
        $tip.html(`未找到包含 "<span class="highlight">${searchText}</span>" 的结果`).show();
    }
}

// 添加多选相关功能
function initMultiSelect() {
    // 修改 action-bar 的结构，添加 download-btn
    const $actionBar = $(`
        <div class="action-bar">
            <div class="left-actions">
                <button class="btn multi-select-btn">
                    <i class="fas fa-check-square"></i> 多选
                </button>
                <button class="btn batch-delete-btn" style="display: none;">
                    <i class="fas fa-trash"></i> 批量删除
                </button>
            </div>
            <div class="right-actions">
                <button class="btn download-btn">
                    <i class="fas fa-download"></i> 下载数据
                </button>
            </div>
        </div>
    `);

    $('#data-page .tabs').after($actionBar);

    // 多选按钮点击事件
    $('.multi-select-btn').click(function () {
        const isActive = $(this).hasClass('active');
        $(this).toggleClass('active');
        $('.batch-delete-btn').toggle(!isActive);

        // 显示/隐藏复选框列
        const $tables = $('.data-table');
        if (!isActive) {
            // 添加复选框列
            $tables.each(function () {
                const $table = $(this);
                // 在表头添加复选框列
                if (!$table.find('th.select-column').length) {
                    $table.find('thead tr').prepend(`
                        <th class="select-column" style="width: 40px; padding: 8px; text-align: center;">
                            <input type="checkbox" class="select-all">
                        </th>
                    `);
                }
                // 在每行数据前添加复选框
                $table.find('tbody tr').prepend(`
                    <td class="select-column" style="width: 40px; padding: 8px; text-align: center;">
                        <input type="checkbox" class="select-item">
                    </td>
                `);
            });
        } else {
            // 移除复选框列
            $('.select-column').remove();
        }
    });

    // 全选/取消全选
    $(document).on('change', '.select-all', function () {
        const $table = $(this).closest('table');
        $table.find('.select-item').prop('checked', $(this).prop('checked'));
    });

    // 批量删除
    $('.batch-delete-btn').click(async function () {
        const $btn = $(this);
        const $activeTable = $('.data-table:visible');
        const selectedRows = $activeTable.find('.select-item:checked').closest('tr');

        if (selectedRows.length === 0) {
            showToast('请选择要删除的记录');
            return;
        }

        if (!confirm(`确定要删除选中的 ${selectedRows.length} 条记录吗？`)) return;

        // 禁用删除按钮并显示加载状态
        $btn.prop('disabled', true)
            .html('<i class="fas fa-spinner fa-spin"></i> 删除中...')
            .css('opacity', '0.7');

        // 给选中的行添加删除中的视觉效果
        selectedRows.addClass('deleting').css({
            'opacity': '0.6',
            'background-color': '#fff3f3'
        });

        const dataType = $activeTable.hasClass('meal-table') ? 'meal' : 'vehicle';
        const deleteList = selectedRows.map(function () {
            const $row = $(this);
            const subTimeIndex = dataType === 'meal' ? 4 : 6;
            return {
                type: dataType,
                sub_time: $row.find('td').eq(subTimeIndex).text()
            };
        }).get();

        try {
            const response = await $.ajax({
                url: '/html/batch_delete_record',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    records: deleteList
                })
            });

            if (response.state === 'ok') {
                // 删除成功后移除行
                selectedRows.fadeOut(300, function () {
                    $(this).remove();
                    // 如果删除后表格为空，重置多选状态
                    if ($activeTable.find('tbody tr').length === 0) {
                        resetMultiSelect();
                    }
                });
                showToast('批量删除成功');
            } else {
                throw new Error(response.message || '删除失败');
            }
        } catch (error) {
            console.error('批量删除失败:', error);
            showToast('批量删除失败: ' + (error.responseJSON?.message || error.statusText));

            // 恢复行的样式
            selectedRows.removeClass('deleting').css({
                'opacity': '',
                'background-color': ''
            });
        } finally {
            // 恢复删除按钮状态
            $btn.prop('disabled', false)
                .html('<i class="fas fa-trash"></i> 批量删除')
                .css('opacity', '');
        }
    });
}

// 重置多选状态
function resetMultiSelect() {
    $('.multi-select-btn').removeClass('active');
    $('.batch-delete-btn').hide();
    $('.select-column').remove();
}
