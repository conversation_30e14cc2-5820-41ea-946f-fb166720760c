$(document).ready(function () {
    // debugger
    const panzoom_ = panzoom(document.getElementById('zoomable-img'), {
        maxScale: 5,
        minScale: 1,
        contain: "inside",
        startScale: 1,
        startX: 0,
        startY: 0
    })

    const container = $('.panzoom-container')[0]

    panzoom_.on('transform', function (e) {
        const transform = panzoom_.getTransform();
        const {scale, x, y} = transform

        $('.absolute-element').each(function () {
            const $el = $(this);
            const originalLeft = parseFloat($el.data('original-left') || parseFloat($el.css('left')))
            const originalTop = parseFloat($el.data('original-top') || parseFloat($el.css('top')))

            const newLeft = originalLeft * scale + x;
            const newTop = originalTop * scale + y;

            $(this).css({
                transform: `translate(${newLeft}px, ${newTop}px) scale(${scale})`,
                transformOrigin: "0 0"
            });

        });

    });

    $('.absolute-element').each(function () {
        const $el = $(this)
        $el.data('original-left') || parseFloat($el.css('left'))
        $el.data('original-top') || parseFloat($el.css('top'))
    });

    $(container).on('wheel', panzoom_.zoomWithWheel);


})