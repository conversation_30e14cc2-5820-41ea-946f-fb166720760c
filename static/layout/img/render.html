<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
    <script src="https://assets.pyecharts.org/assets/v5/echarts.min.js" type="text/javascript"></script>


</head>
<body>
<div class="chart-container" id="0adb6c1e86fe404fbe59b58c91875193" style="width:900px; height:500px; "></div>
<script>
    var chart_0adb6c1e86fe404fbe59b58c91875193 = echarts.init(
        document.getElementById('0adb6c1e86fe404fbe59b58c91875193'), 'white', {renderer: 'canvas'});
    var option_0adb6c1e86fe404fbe59b58c91875193 = {
        "animation": true,
        "animationThreshold": 2000,
        "animationDuration": 1000,
        "animationEasing": "cubicOut",
        "animationDelay": 0,
        "animationDurationUpdate": 300,
        "animationEasingUpdate": "cubicOut",
        "animationDelayUpdate": 0,
        "aria": {
            "enabled": false
        },
        "color": [
            "#5470c6",
            "#91cc75",
            "#fac858",
            "#ee6666",
            "#73c0de",
            "#3ba272",
            "#fc8452",
            "#9a60b4",
            "#ea7ccc"
        ],
        "series": [
            {
                "type": "bar",
                "name": "\u5546\u5bb6A",
                "legendHoverLink": true,
                "data": [
                    114,
                    55,
                    27,
                    101,
                    125,
                    27,
                    105
                ],
                "realtimeSort": false,
                "showBackground": false,
                "stackStrategy": "samesign",
                "cursor": "pointer",
                "barMinHeight": 0,
                "barCategoryGap": "20%",
                "barGap": "30%",
                "large": false,
                "largeThreshold": 400,
                "seriesLayoutBy": "column",
                "datasetIndex": 0,
                "clip": true,
                "zlevel": 0,
                "z": 2,
                "label": {
                    "show": true,
                    "margin": 8,
                    "valueAnimation": false
                }
            },
            {
                "type": "bar",
                "name": "\u5546\u5bb6B",
                "legendHoverLink": true,
                "data": [
                    57,
                    134,
                    137,
                    129,
                    145,
                    60,
                    49
                ],
                "realtimeSort": false,
                "showBackground": false,
                "stackStrategy": "samesign",
                "cursor": "pointer",
                "barMinHeight": 0,
                "barCategoryGap": "20%",
                "barGap": "30%",
                "large": false,
                "largeThreshold": 400,
                "seriesLayoutBy": "column",
                "datasetIndex": 0,
                "clip": true,
                "zlevel": 0,
                "z": 2,
                "label": {
                    "show": true,
                    "margin": 8,
                    "valueAnimation": false
                }
            }
        ],
        "legend": [
            {
                "data": [
                    "\u5546\u5bb6A",
                    "\u5546\u5bb6B"
                ],
                "selected": {},
                "show": true,
                "padding": 5,
                "itemGap": 10,
                "itemWidth": 25,
                "itemHeight": 14,
                "backgroundColor": "transparent",
                "borderColor": "#ccc",
                "borderRadius": 0,
                "pageButtonItemGap": 5,
                "pageButtonPosition": "end",
                "pageFormatter": "{current}/{total}",
                "pageIconColor": "#2f4554",
                "pageIconInactiveColor": "#aaa",
                "pageIconSize": 15,
                "animationDurationUpdate": 800,
                "selector": false,
                "selectorPosition": "auto",
                "selectorItemGap": 7,
                "selectorButtonGap": 10
            }
        ],
        "tooltip": {
            "show": true,
            "trigger": "item",
            "triggerOn": "mousemove|click",
            "axisPointer": {
                "type": "line"
            },
            "showContent": true,
            "alwaysShowContent": false,
            "showDelay": 0,
            "hideDelay": 100,
            "enterable": false,
            "confine": false,
            "appendToBody": false,
            "transitionDuration": 0.4,
            "textStyle": {
                "fontSize": 14
            },
            "borderWidth": 0,
            "padding": 5,
            "order": "seriesAsc"
        },
        "xAxis": [
            {
                "show": true,
                "scale": false,
                "nameLocation": "end",
                "nameGap": 15,
                "gridIndex": 0,
                "inverse": false,
                "offset": 0,
                "splitNumber": 5,
                "minInterval": 0,
                "splitLine": {
                    "show": true,
                    "lineStyle": {
                        "show": true,
                        "width": 1,
                        "opacity": 1,
                        "curveness": 0,
                        "type": "solid"
                    }
                },
                "animation": true,
                "animationThreshold": 2000,
                "animationDuration": 1000,
                "animationEasing": "cubicOut",
                "animationDelay": 0,
                "animationDurationUpdate": 300,
                "animationEasingUpdate": "cubicOut",
                "animationDelayUpdate": 0,
                "data": [
                    "\u886c\u886b",
                    "\u6bdb\u8863",
                    "\u9886\u5e26",
                    "\u88e4\u5b50",
                    "\u98ce\u8863",
                    "\u9ad8\u8ddf\u978b",
                    "\u889c\u5b50"
                ]
            }
        ],
        "yAxis": [
            {
                "show": true,
                "scale": false,
                "nameLocation": "end",
                "nameGap": 15,
                "gridIndex": 0,
                "inverse": false,
                "offset": 0,
                "splitNumber": 5,
                "minInterval": 0,
                "splitLine": {
                    "show": true,
                    "lineStyle": {
                        "show": true,
                        "width": 1,
                        "opacity": 1,
                        "curveness": 0,
                        "type": "solid"
                    }
                },
                "animation": true,
                "animationThreshold": 2000,
                "animationDuration": 1000,
                "animationEasing": "cubicOut",
                "animationDelay": 0,
                "animationDurationUpdate": 300,
                "animationEasingUpdate": "cubicOut",
                "animationDelayUpdate": 0
            }
        ],
        "title": [
            {
                "show": true,
                "text": "\u67d0\u5546\u573a\u9500\u552e\u60c5\u51b5",
                "target": "blank",
                "subtarget": "blank",
                "padding": 5,
                "itemGap": 10,
                "textAlign": "auto",
                "textVerticalAlign": "auto",
                "triggerEvent": false
            }
        ]
    };
    chart_0adb6c1e86fe404fbe59b58c91875193.setOption(option_0adb6c1e86fe404fbe59b58c91875193);
</script>
</body>
</html>
