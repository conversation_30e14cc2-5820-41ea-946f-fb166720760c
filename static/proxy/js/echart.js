$(function () {

    function getData(){
        var result;
        // debugger
        $.ajax({
            dataType:'json',
            url:'Home/GetDashBoardData',
            async:false,
            success: function(e){
                result = e;
            }});
        return result;
    }
    
    var alldata =  getData();
    console.log(alldata)
    initData();
    echart_1();
    echart_2(); 
      echart_3();
      echart_4();

    echart_5();
    echart_6();

    function  initData() {
        $("#facilityCount").text(alldata.facilityCount)
        $("#equipmentCount").text(alldata.invtoryCount)
        $("#weaponCount").text(alldata.weaponCount)
        $("#facilityCanUseCount").text(alldata.facilityCanUseCount)
        $("#equipmentCanUseCount").text(alldata.invtoryNotUseCount)
        $("#weaponCanUseCount").text(alldata.weaponCanUseCount)
        
     
             
         
    }
    //echart_1
    function echart_1() {
   
        var pie_age =echarts.init(document.getElementById("chart_1"));
//var pie_age =echarts.init(document.getElementById("pie_age"),'shine'); 
        option = {
            tooltip: {
                trigger: 'item',
                formatter: "{a} <br/>{b}: {c} ({d}%)"
            },
            legend: {
                orient: 'vertical',
                x: 'left',
                data:['30天内','60天内','90天内','半年内','一年内'],
                textStyle: {color: '#fff'}
            },
            series: [
                {
                    name:'zzwq修期分布',
                    type:'pie',
                    radius: ['30%', '50%'],
                    avoidLabelOverlap: false,
                    center: ['50%', '40%'],
                    label: {
                        normal: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            show: true,
                            textStyle: {
                                fontSize: '20',
                                fontWeight: 'bold'
                            }
                        }
                        ,formatter:function (params) {
                            if(params.percent === 0 ){
                                params.percent = '';
                                return params.percent
                            }else{
                                return params.percent+'%';
                            }
                        }
                    },
                    labelLine: {
                        normal: {
                            show: true
                        }
                    },
                    data: [{value:alldata.weaponrepaircountbyday[0], name:'30天内'},
                {value:alldata.weaponrepaircountbyday[1], name:'60天内'},
                {value:alldata.weaponrepaircountbyday[2], name:'90天内'},
                {value:alldata.weaponrepaircountbyday[3], name:'半年内'},
                {value:alldata.weaponrepaircountbyday[4], name:'一年内'}
                // data: [{value:null, name:'30天内'},
                // {value:null, name:'60天内'},
                // {value:null, name:'90天内'},
                // {value:null, name:'半年内'},
                // {value:null, name:'一年内'}
                
            ]}]
        };
        pie_age.setOption(option); 
        window.addEventListener("resize",function(){
            pie_age.resize();
        });
    }

    //echart_2
    function echart_2() {
          
        //var line_time =echarts.init(document.getElementById("line_time"),'shine'); 
        var pie_age_chart_2 =echarts.init(document.getElementById("chart_2"),'macarons');
//var line_time =echarts.init(document.getElementById("line_time"),'infographic'); 
        var option = {
            // 给echarts图设置背景色
            //backgroundColor: '#FBFBFB',  // -----------> // 给echarts图设置背景色
            color: ['green'],

            tooltip : {
                trigger: 'axis',
                formatter: "{a} <br/>{b} : {c}台"
            },
            grid:{
                height:200,
                x:40,
                y:30,
                x2:5,
                y2:20

            },
            calculable: true,


            xAxis: [{
                type: 'category',
                data: ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'],
                axisLabel: {
                    color: "white", //刻度线标签颜色
                    interval:0,
                    rotate:1
                }
            }],
            yAxis: [{

                type: 'value',
                axisLabel: {
                    color: "#ff4b00" //刻度线标签颜色
                }
            }],
            series: [{
                name: 'zzwq修期分布（月）',
                type: 'line',
                  data: alldata.weaponrepairmonthCounts,
                // data:[0,0,0,0,0,0,0,0,0,0,0,0],
                connectNulls:true,
                showSymbol:false
            }]
        };

     console.log(alldata.weaponrepairmonthCounts)
        pie_age_chart_2.setOption(option);
        
    }

//     function echart_3() {
//
//         var pie_age =echarts.init(document.getElementById("chart_3"));
// //var pie_age =echarts.init(document.getElementById("pie_age"),'shine'); 
//         option = {
//             tooltip: {
//                 trigger: 'item',
//                 formatter: "{a} <br/>{b}: {c} ({d}%)"
//             },
//             legend: {
//                 orient: 'vertical',
//                 x: 'left',
//                 data:['30天内','60天内','90天内','半年内','一年内'],
//                 textStyle: {color: '#fff'}
//             },
//             series: [
//                 {
//                     name:'地面配套装备修期分布',
//                     type:'pie',
//                     radius: ['30%', '50%'],
//                     avoidLabelOverlap: false,
//                     center: ['50%', '40%'],
//                     label: {
//                         normal: {
//                             show: false,
//                             position: 'center'
//                         },
//                         emphasis: {
//                             show: true,
//                             textStyle: {
//                                 fontSize: '20',
//                                 fontWeight: 'bold'
//                             }
//                         }
//                     },
//                     labelLine: {
//                         normal: {
//                             show: true
//                         }
//                     },
//                     data: [{value:alldata.equipmentrepaircountbyday[0], name:'30天内'},
//                         {value:alldata.equipmentrepaircountbyday[1], name:'60天内'},
//                         {value:alldata.equipmentrepaircountbyday[2], name:'90天内'},
//                         {value:alldata.equipmentrepaircountbyday[3], name:'半年内'},
//                         {value:alldata.equipmentrepaircountbyday[4], name:'一年内'}
//
//                     ]}]
//         };
//         pie_age.setOption(option);
//         window.addEventListener("resize",function(){
//             pie_age.resize();
//         });
//     }

    function echart_3() {

        //var line_time =echarts.init(document.getElementById("line_time"),'shine'); 
        var pie_age_chart_3 =echarts.init(document.getElementById("chart_3"),'macarons');
//var line_time =echarts.init(document.getElementById("line_time"),'infographic'); 
        var option = {
            // 给echarts图设置背景色
            //backgroundColor: '#FBFBFB',  // -----------> // 给echarts图设置背景色
            color: ['green'],

            tooltip : {
                trigger: 'axis',
                formatter: "{a} <br/>{b} : {c}台"
            },
            grid:{
                height:200,
                x:40,
                y:30,
                x2:5,
                y2:20

            },
            calculable: true,


            xAxis: [{
                type: 'category',
                data: ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'],
                axisLabel: {
                    color: "white", //刻度线标签颜色
                    interval:0,
                    rotate:1
                }
            }],
            yAxis: [{

                type: 'value',
                axisLabel: {
                    color: "#ff4b00" //刻度线标签颜色
                }
            }],
            series: [{
                name: '物资消耗分布（月）',
                type: 'line',
                data: alldata.inventoryaddmonthCounts,
                connectNulls:true,
                showSymbol:false
            }]
        };


        pie_age_chart_3.setOption(option);

    }
    
    function echart_4() {

        //var line_time =echarts.init(document.getElementById("line_time"),'shine'); 
        var pie_age_chart_4 =echarts.init(document.getElementById("chart_4"),'macarons');
//var line_time =echarts.init(document.getElementById("line_time"),'infographic'); 
        var option = {
            // 给echarts图设置背景色
            //backgroundColor: '#FBFBFB',  // -----------> // 给echarts图设置背景色
            color: ['green'],

            tooltip : {
                trigger: 'axis',
                formatter: "{a} <br/>{b} : {c}台"
            },
            grid:{
                height:200,
                x:40,
                y:30,
                x2:5,
                y2:20

            },
            calculable: true,


            xAxis: [{
                type: 'category',
                data: ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'],
                axisLabel: {
                    color: "white", //刻度线标签颜色
                    interval:0,
                    rotate:1
                }
            }],
            yAxis: [{

                type: 'value',
                axisLabel: {
                    color: "#ff4b00" //刻度线标签颜色
                }
            }],
            series: [{
                name: '物资消耗分布（月）',
                type: 'line',
                data: alldata.inventoryusermonthCounts,
                connectNulls:true,
                showSymbol:false
            }]
        };


        pie_age_chart_4.setOption(option);
        
    }

//echart_3
    function echart_5() {
        var pie_age =echarts.init(document.getElementById("chart_5"));
//var pie_age =echarts.init(document.getElementById("pie_age"),'shine'); 
        option = {
            tooltip: {
                trigger: 'item',
                formatter: "{a} <br/>{b}: {c} ({d}%)"
            },
            legend: {
                orient: 'vertical',
                x: 'left',
                data:['30天内','60天内','90天内','半年内','一年内'],
                textStyle: {color: '#fff'}
            },
            series: [
                {
                    name:'设备修期分布',
                    type:'pie',
                    radius: ['30%', '50%'],
                    avoidLabelOverlap: false,
                    center: ['50%', '40%'],
                    label: {
                        normal: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            show: true,
                            textStyle: {
                                fontSize: '20',
                                fontWeight: 'bold'
                            }
                        }
                    },
                    labelLine: {
                        normal: {
                            show: true
                        }
                    },
                    data: [{value:alldata.facilityrepaircountbyday[0], name:'30天内'},
                        {value:alldata.facilityrepaircountbyday[1], name:'60天内'},
                        {value:alldata.facilityrepaircountbyday[2], name:'90天内'},
                        {value:alldata.facilityrepaircountbyday[3], name:'半年内'},
                        {value:alldata.facilityrepaircountbyday[4], name:'一年内'}

                    ]}]
        };
        pie_age.setOption(option);
        window.addEventListener("resize",function(){
            pie_age.resize();
        });
    }
    function echart_6() {

        //var line_time =echarts.init(document.getElementById("line_time"),'shine'); 
        var pie_age_chart6 =echarts.init(document.getElementById("chart_6"),'macarons');
//var line_time =echarts.init(document.getElementById("line_time"),'infographic'); 
        var option = {
            // 给echarts图设置背景色
            //backgroundColor: '#FBFBFB',  // -----------> // 给echarts图设置背景色
            color: ['green'],

            tooltip : {
                trigger: 'axis',
                formatter: "{a} <br/>{b} : {c}台"
            },
            grid:{
                height:200,
                x:40,
                y:30,
                x2:5,
                y2:20

            },
            calculable: true,


            xAxis: [{
                type: 'category',
                data: ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'],
                axisLabel: {
                    color: "white", //刻度线标签颜色
                    interval:0,
                    rotate:40
                }
            }],
            yAxis: [{

                type: 'value',
                axisLabel: {
                    color: "#ff4b00" //刻度线标签颜色
                }
            }],
            series: [{
                name: '地面配套**修期分布（月）',
                type: 'line',
                connectNulls:true,
                showSymbol:false,
                 
                // data: [1,2,3,4,5,6,7,8,9,10,11,12],
                data: alldata.facilityrepairmonthCounts,

            }]
        };


        pie_age_chart6.setOption(option);

    }

})
