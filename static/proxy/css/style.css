    body {color: #222;padding: 0;overflow-x:hidden;font-size: 13px}
    
	html,body {height:100%}
	
    /* fix for inputs inline shadow */
		input[type="text"], input[type="email"], input[type="search"], input[type="password"] {
			-webkit-appearance: none;
			-moz-appearance: none;
		}
    /* remove buttons borders (ie7) */
		a,button,input {outline:none !important}

	/* webkit extra margin fix */
		@media screen and (-webkit-min-device-pixel-ratio:0) {
			button {margin: 0}
		}

	/* external links */
		.external_link {background: url(../img/external_link.png) no-repeat right center;padding-right:12px}
	
	/* main headings*/
		.heading {border-bottom: 1px solid #dcdcdc;margin-bottom: 18px;padding-bottom: 5px}
		.heading h1,.heading h2,.heading h3,h1.heading,h2.heading,h3.heading,.modal-header h3,.modal-header h4 {font-family: 'PT Sans', sans-serif;font-weight:400}
		.heading .btn {vertical-align:bottom;margin-left:10px}
    
	/* fix for .label position */
		h1 + .label {margin:13px 10px 0 0}
		h2 + .label {margin:12px 10px 0 0}
		h3 + .label {margin:7px 10px 0 0}

    /* element separation */
		.sepH_a {margin-bottom:5px}.sepH_b {margin-bottom:10px}.sepH_c {margin-bottom:20px}
		.sepV_a {margin-right:5px}.sepV_b {margin-right:10px}.sepV_c {margin-right:20px}
		.sepH_a_line {padding-bottom:6px;margin-bottom:18px;border-bottom:1px solid #dcdcdc}
		.sepH_no {margin-bottom:0}
	
		.tac {text-align:center}
		.line_sep {border-bottom: 1px solid #dcdcdc;margin-bottom: 18px;padding-bottom: 5px}
		.sml_t {font-size:11px}
		.sml {font-size:11px;color:#9d9d9d}

	/* text-shadow */
		.ov_boxes .ov_text,.dshb_icoNav li a,.table th,.fc thead th{text-shadow: 1px 1px 0 #fff}
		.dataTables_wrapper .sorting_asc,.dataTables_wrapper .sorting_desc {text-shadow: 1px 1px 0 #eee}

	/* Bootstrap adjustments */
		
		h1,h2,h3,h4,h5,h6{font-family:inherit;font-weight:700;color:inherit;text-rendering:optimizelegibility;margin:0}
		h1 small,h2 small,h3 small,h4 small,h5 small,h6 small{font-weight:400;color:#999}
		h1{font-size:30px;line-height:36px}
		h2{font-size:24px;line-height:36px}
		h3{font-size:18px;line-height:27px}
		h4,h5,h6{line-height:18px}
		h6{font-size:11px;color:#999;text-transform:uppercase}
		h1 small,h2 small{font-size:18px}
		h3 small,h4{font-size:14px}
		h4 small,h5{font-size:12px}
		
		.navbar-fixed-top {margin:0;padding:0;min-height:40px}
		
		.full_width .container {max-width:1680px}
		.row + .row {margin-top:20px}
		.alert {padding:8px 14px}
		
		.modal-header {background:#e4e4e4;border-color:#d4d4d4;padding:5px 15px}
		.modal-header .close {margin-top:3px}
		.modal-title {font-size: 14px;line-height: 26px;text-transform: uppercase}
		.modal-footer {padding:7px 15px 8px}
		.modal-content {border:8px solid rgba(0, 0, 0, 0.5);-webkit-box-shadow: none;-moz-box-shadow: none;-ms-box-shadow: none;box-shadow: none}
		.modal-body{padding:20px 15px}
		.modal-backdrop, .modal-backdrop.fade.in {opacity:1;background:rgba(0,0,0,0.4)}
		.modal-open .modal {overflow-y: scroll}

		.btn-gebo {text-shadow: 0px -1px 0px #004f6f;color:#fff;background-color:#006d8d;background-image:-moz-linear-gradient(top, #1e8bab, #004f6f); background-image: -ms-linear-gradient(top, #1e8bab, #004f6f); background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#1e8bab), to(#004f6f)); background-image: -webkit-linear-gradient(top, #1e8bab, #004f6f); background-image: -o-linear-gradient(top, #1e8bab, #004f6f); background-image: linear-gradient(top, #1e8bab, #004f6f); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#1e8bab', endColorstr='#004f6f', GradientType=0); border-color: #006D8D #004f6f #004f6f #006D8D; border-color: rgba(0, 0, 0, 0.25) rgba(0, 0, 0, 0.35) rgba(0, 0, 0, 0.35) rgba(0, 0, 0, 0.25); }
        .btn-gebo:hover, .btn-gebo:active, .btn-gebo.active, .btn-gebo.disabled, .btn-gebo[disabled] {background-color: #004f6f}
        .btn-gebo:hover {color:#fff;text-shadow: 0px -1px 0px #003151}
        .btn-gebo:focus {color: #fff;-webkit-box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);-moz-box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05); border-color: #006D8D #004f6f #004f6f #006D8D; border-color: rgba(0, 0, 0, 0.25) rgba(0, 0, 0, 0.35) rgba(0, 0, 0, 0.35) rgba(0, 0, 0, 0.25); }
        .btn-gebo.active, .btn-gebo:active { background-image: none;-webkit-box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);-moz-box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05); color: #64d1f1; }

		
		.main_content .panel-heading .accordion-toggle {background-color:#f5f5f5;color:#222;text-decoration:none;background-image: url(../img/acc_icons.png);background-position: 98% 12px;background-repeat:no-repeat}
		.main_content .panel-heading .accordion-toggle:hover {background-color:#e5e5e5}
		.main_content .panel-heading .acc-in {background-position:98% -34px}
		
		.label {cursor:default;font-size:11px;padding:2px 4px}
		.label-danger,.badge-danger {background: #C62626}
		.label-success,.badge-success {background: #70A415}
		.label-warning,.badge-warning {background: #F5AA1A}
		.label-info,.badge-info {background: #058DC7}
		
		.nav-pills + .nav-pills {margin-left: 10px;padding-left:10px;border-left: 1px solid #dcdcdc}
		.nav-pills > li {line-height:18px}
		.nav-pills > li > a {padding: 5px 8px}
		
		.dropdown-menu li {padding:0 5px}
		.dropdown-menu li a {padding:3px 20px 3px 8px;position:relative;font-size:13px}
		.dropdown-menu li > label {margin-top:4px;margin-bottom:4px}
		
		.pagination > li > div > a,.pagination > li > div > span {float: left;padding: 0 14px;line-height: 38px;text-decoration: none;background-color: #ffffff;border: 1px solid #dddddd;border-left-width: 0}
		.pagination > li > div > a:hover,.pagination > .active > div > a,.pagination > .active > div > span {background-color: #f5f5f5}
		.pagination > .active > div > a,.pagination > .active > div > span {color: #999999;cursor: default}
		.pagination > .disabled > div > span,.pagination > .disabled > div > a,.pagination > .disabled > div > a:hover {color: #999999;cursor: default;background-color: transparent}
		.pagination > li:first-child > div > a,.pagination > li:first-child > div > span {border-left-width: 1px;-webkit-border-radius: 3px 0 0 3px;-moz-border-radius: 3px 0 0 3px;border-radius: 3px 0 0 3px}
		.pagination > li:last-child > div > a,.pagination > li:last-child > div > span {-webkit-border-radius: 0 3px 3px 0;-moz-border-radius: 0 3px 3px 0;border-radius: 0 3px 3px 0}
		
		.pagination {height:26px}
		.pagination a.active {background-color: #f5f5f5}
		.pagination a.active {color: #999;cursor: default}
		.pagination > li > a,.pagination > li > span,.pagination > li > div > a,.pagination > li > div > span {line-height: 24px;padding:0 10px}
		.pagination > li > div.page {display:inline}
		
		label {font-weight:normal;display:block}
		
		.navbar .nav > li > a,.navbar .nav > li > a:hover,.navbar  .nav > li > a:focus {color:#fff}
		.navbar-nav > li > a > .glyphicon {font-size: 12px;padding-right:2px}
		.dropdown-menu li a {-webkit-border-radius: 4px;-moz-border-radius: 4px;-ms-border-radius: 4px;border-radius: 4px}
		.navbar .divider-vertical {height: 40px;margin: 0 9px;border-width:0 1px;border-style:solid;border-color: transparent rgba(255,255,255,0.2) transparent rgba(0,0,0,0.2);}
		
		tr.rowlink td {cursor:pointer}
		tr.rowlink td.nolink {cursor:auto}
		.table tbody tr.rowlink:hover td {background-color: #efefef}
		a.rowlink {font: inherit;color: inherit;text-decoration: inherit}
		
		.modal-open .colorpicker.dropdown-menu,.modal-open .datepicker.dropdown-menu {z-index:10000}

		.alert-dismissable .close {right:-2px;top:-1px}
		.alert-heading {font-size:14px;margin:0 0 6px}
		.alert ul {margin:0 0 10px 20px;padding:0}
		.alert p + p {margin-top:10px}

		.panel-group .panel + .panel {margin-top:3px}
		.accordion .panel-heading {padding:0}
		.accordion .panel-heading > a {padding:8px 12px;display:block}

		.tab-content > .active {padding:8px}

	/* header */
		.navbar .brand {width: 200px;padding-bottom:0;padding-top:0;font: 100 18px/38px 'PT Sans', sans-serif;text-decoration:none;color: #fff;text-shadow:none}
		.navbar .user_menu {margin-left: 0}
		.navbar .user_menu .nb_boxes {margin:9px 0 0}
		.navbar .user_menu .nb_boxes .label {background: transparent;background:rgba(0,0,0,.2);padding:3px 4px}
		.navbar .user_menu .nb_boxes a {cursor:pointer;float:left;margin-right:8px}
		.navbar .user_menu .nb_boxes a:last-child {margin:0}
		.navbar .user_menu a.dropdown-toggle {white-space:nowrap}
		.navbar .user_menu .user_avatar {margin-right:6px;height:20px}
		.navbar .nav > li > a {text-shadow:none;padding:10px 10px 9px}
		.navbar .nav li.dropdown.open > .dropdown-toggle, .navbar .nav li.dropdown.active > .dropdown-toggle, .navbar .nav li.dropdown.open.active > .dropdown-toggle {background:none;color:#fff}
        .btn_menu {display:none}

	/* main content */
		#maincontainer {min-height:100%}
        #contentwrapper{float: left;width: 100%}
		.main_content{padding:64px 30px 30px;background:#fff;border-left:1px solid transparent;margin-left:240px}
		.main_content li{line-height:22px}
	
	/* typeahead */
		.twitter-typeahead { width: 100%; position: relative;vertical-align: middle }
		.twitter-typeahead .tt-query, .twitter-typeahead .tt-hint { margin-bottom: 0; width: 100%; position: absolute; top: 0; left: 0; }
		.twitter-typeahead .tt-hint { color: #a1a1a1; z-index: 1; height:30px; padding: 5px 12px; border: 1px solid transparent; }
		.twitter-typeahead .tt-query { z-index: 2 }
		.tt-dropdown-menu { min-width: 160px; margin-top: 2px; padding:0; background-color: #fff; border-width:0 1px 1px;border-style:solid;border-color:#ccc;border-color:rgba(0,0,0,.2);-webkit-box-shadow: 0 2px 4px rgba(0,0,0,.2);-moz-box-shadow: 0 2px 4px rgba(0,0,0,.2);box-shadow: 0 2px 4px rgba(0,0,0,.2);}
		.tt-suggestion { display: block; padding: 6px 14px;position:relative }
		.tt-suggestion.tt-is-under-cursor { color: #fff ; background-color: #0081c2 }
		.tt-suggestion.tt-is-under-cursor a,.tt-suggestion.tt-is-under-cursor .text-muted {color:#fff}
		.tt-suggestion p { margin: 0;line-height:16px }
		.tt-dropdown-menu .sg_desc {font-size:11px}
		.input-group .tt-hint {Height:34px}
		.input-group-sm .tt-hint {Height:30px;padding-left:10px}
	
	/* sidebar */
		.sidebar{margin-left:-100%;width:240px;float:left;position:relative;overflow-x:hidden;overflow-y:auto;border-right:1px solid #ccc}
		.sidebar_sep{background:#efefef;clear:both;float:none;height:5px;border-color:#ccc;border-style:solid;border-width:1px 0;margin:14px 0}
		.sidebar_switch{text-indent:-10000px;width:16px;position:fixed;left:220px;top:45px;height:14px;cursor:pointer;z-index:100;display:block;opacity:.5}
		.on_switch {background: url(../img/sidebar_switch.png) no-repeat 0 -19px}
		.off_switch {background: url(../img/sidebar_switch.png) no-repeat 0 -1px}
		.sidebar_inner form{padding:18px 15px 9px}
		.sidebar_inner form input{margin:0}
		.sidebar_inner form input.tt-query {padding-left:11px;padding-top:6px;font-size: 13px;line-height: 18px;}
	
		.sidebar_hidden .sidebar {display:none}
		.sidebar_hidden .sidebar_switch {left:4px;}
		.sidebar_hidden .main_content {margin:0;border-left:none}
		.sidebar_hidden {background: #fff}
		
		.sidebar_info {width:180px;position:relative;left:30px;bottom:10px}
        .sidebar_info ul {padding:5px 10px;margin:0;font-size:11px;background:#fafafa;border:1px solid #e5e5e5;-webkit-border-radius: 6px;-moz-border-radius: 6px;-ms-border-radius: 6px;border-radius: 6px}
        .sidebar_info ul li {line-height:26px;overflow:hidden;border-bottom:1px dashed #ccc}
        .sidebar_info ul li:last-child {border:none}
        .sidebar_info .act {float:right}
        
		.sidebar .panel-group {border-top: 1px solid #ccc;margin-bottom: 20px}
        .sidebar .panel-heading {text-shadow: 1px 1px 0 #efefef;background: #e0e0e0;-webkit-box-shadow: inset 0px 1px 0px 0px #ececec;box-shadow: inset 0px 1px 0px 0px #ececec;padding:0}
        .sidebar .panel-heading a:hover {background-color: #cfcfcf}
        .sidebar .panel {-webkit-border-radius:0;-moz-border-radius:0;border-radius:0;margin-bottom:0;border-color:#ccc;border-style:solid;border-width:0 0 1px}
        .sidebar .panel a {color:#222;text-decoration:none!important}
        .sidebar .panel .active a{color:#fff}
        .sidebar .panel .panel-heading a{color:#222}
        .sidebar .panel + .panel {margin-top:0}
		
		.sidebar .panel-heading .accordion-toggle {display:block;padding:8px 15px}		
		.sidebar .panel-heading .accordion-toggle > .glyphicon {font-size:12px;width: 20px;text-align: left}

		.sidebar .panel-body {border-top:1px solid #ccc;background:#fafafa}	
		.sidebar .nav-pills > li > a {padding:3px 15px}
		.sidebar .nav-header {color: #999999;display: block;font-size: 11px;font-weight: bold;line-height: 20px;padding: 3px 15px;text-transform: uppercase;margin: 4px -15px 4px}
		.sidebar li + .nav-header {margin-top:10px}

		/* accordion background animation */
		.sidebar .accordion-toggle {-webkit-transition:background-color 0.2s ease-in-out; -moz-transition:background-color 0.2s ease-in-out;  -o-transition:background-color 0.2s ease-in-out;  transition:background-color 0.2s ease-in-out;}
		.sdb_h_active a {background: url("../img/bullet_green.png") no-repeat 98% center}

	/* login page */
		.login_page {overflow:auto}
		.login_page body {height:100%;max-width:inherit;margin:0 20px}
		.login_page .login_box {position:relative;top:50%;width:380px;margin:0 auto 24px;background:#fff;border:1px solid #ccc;-webkit-border-radius: 6px;-moz-border-radius: 6px;-ms-border-radius: 6px;border-radius: 6px;-webkit-box-shadow: 0 0 6px rgba(0,0,0,0.2);-moz-box-shadow: 0 0 6px rgba(0,0,0,0.2);-ms-box-shadow: 0 0 6px rgba(0,0,0,0.2);box-shadow: 0 0 6px rgba(0,0,0,0.2)}
		.login_page .top_b {text-shadow:0 1px 0 rgba(255,255,255,.5);font: 100 18px/42px 'PT Sans', sans-serif;height:42px;padding:0 20px;background: #e0e0e0;border-bottom:1px solid #ccc;-moz-border-radius-topleft: 6px;-moz-border-radius-topright: 6px;-moz-border-radius-bottomright: 0px;-moz-border-radius-bottomleft: 0px;-webkit-border-radius: 6px 6px 0px 0px;border-radius: 6px 6px 0px 0px;font-size:15px}
		.login_page .cnt_b {padding:30px 0;width:66%;margin:0 auto}
		.login_page form {margin-bottom:0}
		.login_page .btm_b {padding:12px 20px;border-top:1px solid #e7e7e7;background:#f7f7f7;-moz-border-radius-topleft: 0px;-moz-border-radius-topright: 0px;-moz-border-radius-bottomright: 6px;-moz-border-radius-bottomleft: 6px;-webkit-border-radius: 0px 0px 6px 6px;border-radius: 0px 0px 6px 6px}
		.login_page .links_b {width:100%;font-size:11px;text-align:center;position:absolute;bottom:-24px}
		.login_page .link_reg {font-size:11px;padding:5px 0 0;display:block}
		.alert-login {margin:10px 10px 0}
	   
	/* error pages */
		.error_page {background:#eee}
		.error_page,.error_page body,.error_page .error_box {height:100%}
		.error_page h1 {font-family: 'Jockey One', sans-serif;font-size: 52px;line-height:1.1;text-transform: uppercase;color: #067ead;text-shadow: 2px 2px 0px rgba(255,255,255,.5), 4px 4px 0px rgba(0,0,0,.1);margin-bottom:10px;padding:40px 0 0 300px}
		.error_page p {color:inherit;font-size:16px;line-height:24px;font-weight:200;margin-left:300px}
		.error_page .back_link {margin-left:300px}
		.error_page .error_box {background: url(../img/error_big.png) no-repeat 0 0;padding:0;width:72%;margin: 0 auto}

    /* search page */
        .search_page .well {padding:10px 20px;line-height:26px}
        .search_page .well select {margin:0;display:inline-block;width:auto}
        .search_page .search_panel {border:1px solid #e0e0e0;-webkit-border-radius: 6px;-moz-border-radius: 6px;-ms-border-radius: 6px;border-radius: 6px;margin-bottom:20px}
        .search_page .search_item {border-bottom:1px solid #e0e0e0;padding:10px 20px 10px 30px;position:relative}
        .search_page .search_item:nth-child(even) {background:#f2f8fd}
        .search_page .search_item:last-child {border:none}
        .search_page .search_content {padding-left:110px}
        .search_page .search_content h4 {font-size:14px;margin-bottom:6px}
        .search_page .search_content h4 {vertical-align:text-top}
        .search_page .search_content strong {color:#666}
        .search_page .search_content small {color:#666;font-size:11px}
        .search_page .searchNb {position:absolute;top:10px;left:10px;color:#ccc;font-size:11px}
        .search_page .result_view a {opacity:0.3}
        .search_page .result_view a.active {opacity:1}
        
        .search_page .box_view {border:none;margin-left:-1%}
        .search_page .box_view .search_item {margin-left:1%;border:1px solid #e0e0e0;height:200px;-webkit-border-radius: 6px;-moz-border-radius: 6px;-ms-border-radius: 6px;border-radius: 6px;margin:0 0 1% 1%;float:left;width:32.333%}
        .search_page .box_view h4 {height:20px;overflow:hidden}
        .search_page .box_view .item_description {max-height:92px;overflow:hidden}
        .search_page .box_view .label {position:absolute;top:-9px;right:-6px}
        
        .sidebar_filters {padding:30px 30px 20px}
        .sidebar_filters h3 {font-size:13px;color:#067EAD}
        .sidebar_filters .filter_items {padding:0 4px 10px;margin-bottom:10px;border-bottom:1px dashed #ccc}

	/* forms */
		.formRow:last-child input, .formRow:last-child textarea, .formRow:last-child select, .uneditable-input {margin-bottom: 0}
		.formRow small {display:block;font-size:11px;color:#999;line-height:14px}
		.formSep {margin-bottom:12px;padding-bottom:12px;border-bottom:1px dashed #dcdcdc}
		
		.stacked select, .stacked input[type="text"],.stacked textarea {display:block}
		
		.help-block {font-size:11px;line-height:14px}
		
		.form-horizontal .help-block {margin-top:2px}
		legend+.form-group {margin-top:20px}
		.form-horizontal .form-group {margin-bottom:14px}
		
		input.focused {border-color: rgba(82,168,236,.8);-webkit-box-shadow: inset 0 1px 3px rgba(0,0,0,.1), 0 0 8px rgba(82,168,236,.6);-moz-box-shadow: inset 0 1px 3px rgba(0,0,0,.1), 0 0 8px rgba(82,168,236,.6);box-shadow: inset 0 1px 3px rgba(0,0,0,.1), 0 0 8px rgba(82,168,236,.6);outline: 0;z-index:2;position:relative}
		
		.f_warning > label,.f_warning .help-block,.f_warning .help-inline{color:#c09853}
		.f_warning input,.f_warning select,.f_warning textarea{color:#c09853;border-color:#c09853}
		.f_warning input:focus,.f_warning select:focus,.f_warning textarea:focus{-webkit-box-shadow:0 0 6px #dbc59e;-moz-box-shadow:0 0 6px #dbc59e;box-shadow:0 0 6px #dbc59e;border-color:#a47e3c}
		.f_warning .input-prepend .add-on,.f_warning .input-append .add-on{color:#c09853;background-color:#fcf8e3;border-color:#c09853}
		
		.f_error input,.f_error select,.f_error textarea{color:#b94a48;border-color:#b94a48}
		.f_error input:focus,.f_error select:focus,.f_error textarea:focus{-webkit-box-shadow:0 0 6px #d59392;-moz-box-shadow:0 0 6px #d59392;box-shadow:0 0 6px #d59392;border-color:#953b39}
		.f_error .input-prepend .add-on,.f_error .input-append .add-on{color:#b94a48;background-color:#f2dede;border-color:#b94a48}
		.f_error label.error {display:block;font-size:11px;font-weight:700;color:#C62626}
		input[type="text"] + label.error,input[type="password"] + label.error, select + label.error, textarea + label.error {margin-top:6px}
		.f_error label.radio + label.error,.f_error label.checkbox + label.error {margin-top:5px}
		.input-prepend.f_error label.error,.input_append.f_error  label.error {margin-top:4px}
		.control-group.f_error label.error {margin-top:2px}
		.checkbox-inline + label.error, .radio-inline + label.error {margin-top:4px}

		.f_success > label,.f_success .help-block,.f_success .help-inline{color:#468847}
		.f_success input,.f_success select,.f_success textarea{color:#468847;border-color:#468847}
		.f_success input:focus,.f_success select:focus,.f_success textarea:focus{-webkit-box-shadow:0 0 6px #7aba7b;-moz-box-shadow:0 0 6px #7aba7b;box-shadow:0 0 6px #7aba7b;border-color:#356635}
		.f_success .input-prepend .add-on,.f_success .input-append .add-on{color:#468847;background-color:#dff0d8;border-color:#468847}
		
		.ui-tooltip label {margin-bottom:0}
		.f_legend {font-size:16px;line-height:16px;padding-bottom:10px;margin-bottom:22px;border-bottom:1px solid #dcdcdc}
		.radio.inline, .checkbox.inline {margin-left:0 !important;margin-right:10px}
		
        .f_req {font-size:13px;color:#ff0000;font-family: helvetica, arial, sans-serif}
		.form-horizontal .controls.text_line {line-height:25px}
		
		.row input[class*="span"],.row textarea[class*="span"],.row select[class*="span"] {display:inline-block}
		.row input[class*="span"] + input[class*="span"], .row textarea[class*="span"] + textarea[class*="span"], .row select[class*="span"] + select[class*="span"] {display:block}
		
        textarea {resize: none}
        .ui-widget + .help-block {margin-top:-4px}

		input[type="radio"], input[type="checkbox"] {margin-top:2px;}

    /* BaseFile upload */
        .btn.btn-file{overflow:hidden;position:relative;vertical-align:middle}
        .btn.btn-file > input[type=file]{width:100%;position:absolute;left:0;top:0;opacity:0;cursor:pointer}
        .fileupload{margin-bottom:9px}
        .fileupload .uneditable-input{display:inline-block;margin-bottom:0;vertical-align:middle;height:28px !important}
        .fileupload .thumbnail{overflow:hidden;display:inline-block;margin-bottom:5px;vertical-align:middle;text-align:center}
        .fileupload .thumbnail > img{display:block;vertical-align:middle;max-height:100%}
        .fileupload .btn{vertical-align:middle}
        .fileupload-exists .fileupload-new,.fileupload-new .fileupload-exists{display:none}
        .fileupload-inline .fileupload-controls{display:inline}
        .fileupload-new .input-append .btn-file{-webkit-border-radius:0 3px 3px 0;-moz-border-radius:0 3px 3px 0;border-radius:0 3px 3px 0}
        .fileupload .fileupload-preview {vertical-align:middle}
        .fileupload .close.fileupload-exists {vertical-align:middle}
        
	/* input spinner */
		.ui-spinner button {height:14px;position:absolute;background:none;border:none;width:18px;padding:0;background: url(../img/spinner_arrows.gif) no-repeat 0 -100px;cursor:pointer}
		.ui-spinner .ui-spinner-up {top:0;right:0;background-position:0 0}
		.ui-spinner .ui-spinner-down {top:14px;right:0;background-position:0 -14px}
		.ui-spinner .ui-spinner-up:hover {background-position:-18px 0}
		.ui-spinner .ui-spinner-down:hover {background-position:-18px -14px}
		.ui-spinner input,.ui-spinner input:focus {display:block !important;margin:0 !important;padding:0;min-height:28px !important;height:28px !important;-webkit-box-shadow:none;-moz-box-shadow:none;-ms-box-shadow:none;box-shadow:none}
		.ui-spinner ul {margin:0}
		.ui-spinner li {line-height:28px}
		.ui-spinner{-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);-moz-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);position:relative;margin-bottom:10px;font-size:13px;height:28px;line-height:28px;color:#555;background-color:#fff;border:1px solid #ccc;-webkit-border-radius:3px;-moz-border-radius:3px;border-radius:3px;overflow:hidden;padding:0 24px 0 8px}
		.ui-spinner .ui-spinner-box {background: none;border: none !important}	
		
	/* animated progresbars */
		.pbar .ui-progressbar-value {display:block !important}
		.pbar {overflow: hidden}
		.percent,.elapsed {position:relative;text-align: right; font-size: 12px}
	
	/* timepicker */
		.bootstrap-timepicker-widget table {max-width:240px}
	
	/* ul list styles */
		.list_a,.list_b,.list_c,.list_d {margin-left:0;list-style:none;padding-left:16px}
		.list_a li {background: url(../img/bullet_blue.png) no-repeat 3px 4px;padding-left:25px}
		.list_b li {background: url(../img/bullet_green.png) no-repeat 3px 4px;padding-left:25px}
		.list_c li {background: url(../img/bullet_orange.png) no-repeat 3px 4px;padding-left:25px}
		.list_d li {background: url(../img/bullet_red.png) no-repeat 3px 4px;padding-left:25px}		
		
	/* prettyprint <pre> block */
		.lit{color:#195f91}
		.fun{color:#dc322f}
		.str,.atv{color:#D14}
		.kwd,.linenums .tag{color:#1e347b}
		.typ,.atn,.dec,.var{color:teal}
		.pln{color:#48484c}
		.prettyprint{background-color:#f7f7f9;border:1px solid #e1e1e8;padding:8px}
		.prettyprint.linenums{-webkit-box-shadow:inset 40px 0 0 #fbfbfc, inset 41px 0 0 #ececf0;-moz-box-shadow:inset 40px 0 0 #fbfbfc, inset 41px 0 0 #ececf0;box-shadow:inset 40px 0 0 #fbfbfc, inset 41px 0 0 #ececf0}
		ol.linenums{margin:0 0 0 31px;padding:0}
		ol.linenums li{padding-left:12px;color:#bebec5;line-height:18px;text-shadow:0 1px 0 #fff}
		.com,.pun,.opn,.clo{color:#93a1a1}		
		
	/* rowlink plugin */
		[data-rowlink] td, td[data-rowlink],[data-msg_rowlink] td, td[data-msg_rowlink] {cursor: pointer}	
		[data-rowlink] td.nohref, td.nohref[data-rowlink], [data-msg_rowlink] td.nohref, td.nohref[data-msg_rowlink] {cursor:default}
	
	/* actions links */
		.act{background:inherit;border:none;display:inline;color:#555;font-weight:700;-webkit-transition:text-shadow .1s linear;-moz-transition:text-shadow .1s linear;-ms-transition:text-shadow .1s linear;-o-transition:text-shadow .1s linear;transition:text-shadow .1s linear;padding:0}
		a.act:hover{color:#333;text-decoration:none;text-shadow:1px 1px 3px rgba(85,85,85,0.5)}
		.act-primary{color:#006dcc}
		a.act-primary:hover{color:#04c;text-shadow:1px 1px 3px rgba(0,109,204,0.5)}
		.act-info{color:#49afcd}
		a.act-info:hover{color:#2f96b4;text-shadow:1px 1px 3px rgba(75,175,206,0.5)}
		.act-success{color:#70A415}
		a.act-success:hover{color:#468847;text-shadow:1px 1px 3px rgba(81,164,81,0.5)}
		.act-warning{color:#F5AA1A}
		a.act-warning:hover{color:#f89406;text-shadow:1px 1px 3px rgba(192,152,84,0.5)}
		.act-danger{color:#C62626}
		a.act-danger:hover{color:#bd362f;text-shadow:1px 1px 3px rgba(185,72,70,0.5)}
		.act.disabled,.act[disabled]{color:#AAA;cursor:not-allowed}
		a.act.disabled:hover,.act[disabled]:hover{color:#AAA;text-shadow:none}
		.form-actions .act{line-height:30px}
		
	/* tabs */
		.tabbable-bordered{-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px}
		.tabbable-bordered > .nav-tabs {margin: 0}
		.tabbable-bordered .tab-content {border-color: #ddd;border-style:solid;border-width:0;overflow:auto;padding:18px 0 0}
		.tabbable-bordered .tab-pane {padding:0}
		.tabbable-bordered > .tab-content{-webkit-border-radius:0 0 4px 4px;-moz-border-radius:0 0 4px 4px;border-radius:0 0 4px 4px;border-width:0 1px 1px;padding:20px 20px 10px}
	
	/* dashboard icon navigation*/
		.dshb_icoNav {margin:0;text-align:center;padding:0}
		.dshb_icoNav li {-webkit-box-shadow: inset 0px 1px 0px 0px #fff;box-shadow: inset 0px 1px 0px 0px #fff;text-align:center;list-style:none;display:inline-block;margin:0 5px 10px;background: #f9f9f9;background: -moz-linear-gradient(top, #f9f9f9 0%, #efefef 100%);background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f9f9f9), color-stop(100%,#efefef));background:-webkit-linear-gradient(top, #f9f9f9 0%,#efefef 100%);background: -o-linear-gradient(top, #f9f9f9 0%,#efefef 100%);background: -ms-linear-gradient(top, #f9f9f9 0%,#efefef 100%);background: linear-gradient(top, #f9f9f9 0%,#efefef 100%);filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f9f9f9', endColorstr='#efefef',GradientType=0 );border:1px solid #e2e2e2;-webkit-border-radius: 6px;-moz-border-radius: 6px;-ms-border-radius: 6px;border-radius: 6px}
		.dshb_icoNav li:hover {border-color:#d2d2d2;-moz-box-shadow: 0 0 6px #ccc;-webkit-box-shadow: 0 0 6px #ccc;box-shadow: 0 0 6px #ccc}
		.dshb_icoNav li a {position:relative;display:block;padding:42px 8px 8px;width:110px;height:72px;font-size:12px;color:#222;background-repeat:no-repeat;background-position: center 10px}
		.dshb_icoNav li a:hover {text-decoration:none}
		.dshb_icoNav .label {position:absolute;right:-5px;top:-5px;text-shadow: none;}
		.iconNav_left {text-align:left}
		.iconNav_right {text-align:right}
	
	/* overview boxes (+small charts) */
		.ov_boxes {list-style: none;display: inline-block;margin:0 0 -20px;text-align:center;padding:0}
		.ov_boxes li {-webkit-box-shadow: inset 0px 1px 0px 0px #fff;box-shadow: inset 0px 1px 0px 0px #fff;line-height:18px;background: #f9f9f9;background: -moz-linear-gradient(top, #f9f9f9 0%, #ededed 100%);background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f9f9f9), color-stop(100%,#ededed));background: -webkit-linear-gradient(top, #f9f9f9 0%,#ededed 100%);background: -o-linear-gradient(top, #f9f9f9 0%,#ededed 100%);background: -ms-linear-gradient(top, #f9f9f9 0%,#ededed 100%);background: linear-gradient(top, #f9f9f9 0%,#ededed 100%);filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f9f9f9', endColorstr='#ededed',GradientType=0 );border: 1px solid #e2e2e2;display: inline-block;margin:0 10px 10px;-webkit-border-radius: 4px;-moz-border-radius: 4px;-ms-border-radius: 4px;border-radius: 4px;padding: 0 10px}
		.ov_boxes .p_canvas{margin-right:12px;float:left;border-right:1px solid #dcdcdc;-webkit-box-shadow:1px 0 0 0 #fff;-moz-box-shadow:1px 0 0 0 #fff;box-shadow:1px 0 0 0 #fff;padding:10px 14px 6px 4px}
		.ov_boxes .ov_text{text-align:left;width:180px;font-size:12px;float:left;padding:9px 10px 7px 0}
		.ov_boxes .ov_text strong{font-size:16px;display:block}
		.p_canvas {width:70px}

	/* flot charts */
		div.legend table td {padding: 3px 2px}
		#flotTip {background: rgba(0,0,0,0.8);font-size: 11px; line-height: 26px;padding: 0 6px;color:#fff;font-weight: 700;border-radius: 4px;}
		.fl_multihighlight {color: #fff;background: rgba(0,0,0,0.8);padding: 4px 8px;font-size: 11px;position: fixed;z-index: 1000;border-radius: 4px;}

	/* qtip2 tooltip */
		.qtip label {margin:0;font-size: 11px;font-weight: 700}

    /* widgets */
        .w-box {-webkit-box-shadow:  0 1px 2px 0 #efefef;box-shadow:  0 1px 2px 0 #efefef}
        .w-box + .w-box {margin-top:20px}
        .w-box-header {height:32px;line-height:31px;border:1px solid #ddd;padding:0 10px;background: #fbfbfb;
            background: -moz-linear-gradient(top,  #fbfbfb 0%, #f1f1f1 100%);
            background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#fbfbfb), color-stop(100%,#f1f1f1));
            background: -webkit-linear-gradient(top,  #fbfbfb 0%,#f1f1f1 100%);
            background: -o-linear-gradient(top,  #fbfbfb 0%,#f1f1f1 100%);
            background: -ms-linear-gradient(top,  #fbfbfb 0%,#f1f1f1 100%);
            background: linear-gradient(top,  #fbfbfb 0%,#f1f1f1 100%);
            filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fbfbfb', endColorstr='#f1f1f1',GradientType=0 );
            font-weight:700;color:#666;font-size:11px
        }
        .w-box-header .btn-group {line-height:1}
        .w-box-header .dropdown-menu {font-size:12px}
        .w-box-header input {padding:2px 6px;margin:3px 0 0;height:24px;font-size:12px}
        .w-box-content {border:1px solid #ddd;border-top:none}
        .w-box-content.cnt_a {padding:10px}
        .w-box-content table,.w-box-content pre {margin-bottom:0}
        .w-box-content pre {-webkit-border-radius: 0 0 0 0;-moz-border-radius: 0 0 0 0;-ms-border-radius: 0 0 0 0;border-radius: 0 0 0 0;border:none}
        .w-box-content .no-th tr:first-child td {border-top:none}
        .w-box-content .gebo-upload {margin:0}
        .w-box-content .gebo-upload .plupload_filelist_header {border-top:none}
        .w-box-content .gebo-upload .plupload_filelist_footer {border-bottom:none}
        .w-box-content .nav-tabs {position:relative;top:-25px;margin-bottom:-16px;border-bottom:none}
        .w-box-content .nav-tabs {float:right;clear:both}
        .w-box-content .nav-tabs > li > a {padding:4px 6px;font-size:11px}
        .w-box-content .nav-tabs > li.active > a {background:#fff !important}
        .w-box-content .nav-tabs > li > a:hover {border-color:#ddd #ddd transparent;background: transparent}
        .w-box-content .tab-content {clear:both}
        .w-box-content .tab-pane {padding:0 10px 10px}
        .w-box-footer {border:1px solid #ddd;padding:8px 10px;background: #fdfdfd;border-top:none}
        .w-box-footer .pagination {margin:0; font-size:11px;height:22px}
        .w-box-footer .pagination a {line-height:20px}

		.page-toolbar {background:#f5f8fa;border:1px solid #ddd;padding:4px;margin-bottom:15px;-webkit-border-radius: 4px;-moz-border-radius: 4px;-ms-border-radius: 4px;border-radius: 4px}

    /* sortable/searchable list */
		.user_list {list-style: none;margin:0;padding:0}
		.user_list li {padding:0 8px 8px;margin-bottom:8px;border-bottom: 1px dashed #dcdcdc;line-height:normal }
		.user_list small {font-size: 11px;color:#9b9b9b}
	
	/* BaseFile manager */
        #kcfinder_iframe {height:500px;margin-bottom:30px;border:1px solid #adaba9}

	/* icon list */
		.icon_list_awesome,.icon_list_a,.icon_list_b,.icon_list_c,.icon_list_d {list-style: none;margin:0 0 30px;padding:0}
		.icon_list_awesome li,.icon_list_a li,.icon_list_b li,.icon_list_d li {padding:4px 0;width:30px;text-align:center;border:1px solid #dcdcdc;float:left;margin:4px;-webkit-border-radius: 4px;-moz-border-radius: 4px;-ms-border-radius: 4px;border-radius: 4px}
		.icon_list_c li {padding:4px 0;width:40px;text-align:center;border:1px solid #dcdcdc;float:left;margin:4px;-webkit-border-radius: 4px;-moz-border-radius: 4px;-ms-border-radius: 4px;border-radius: 4px}
		.icon_copy_awesome i,.icon_list_awesome i {font-size:14px}
	
    /* aditional icons */
        .icon-adt_trash,.icon-adt_atach,.icon-adt_enter {background: url(../img/adt-icons.png) no-repeat 0 0;width:16px;height:16px;display:inline-block;line-height:16px;vertical-align:text-top}
        .icon-adt_atach {background-position:0 0}
        .icon-adt_trash {background-position:0 -24px}
        .icon-adt_enter {background-position:0 -47px;width:12px;height:12px}
    
	/* google maps */    
		.company_add_form legend {margin-bottom:10px;font-size:15px}
		#g_map img { max-width: none; }
		#g_map label {display:inline;margin:0;color:#000;font-size: 11px;line-height:normal}
		
	/* gallery grid */
		.wmk_grid > ul {list-style: none;margin:0;position:relative}
		.wmk_grid > ul > li {background-color: #fff;display: none;}
		.wmk_grid > ul > li>a,.wmk_grid ul > li>a img {display: block}
		.wmk_grid > ul > li>a img {width:100%}
		.wmk_grid > ul > li p {color: #666;font-size: 12px;margin: 8px 4px 2px;overflow:hidden}
		.wmk_grid > ul > li p a {display:inline-block;margin-left:10px;float:right;opacity:.6}
		.wmk_grid > ul > li p a:hover {opacity:1}
		.wmk_grid > ul > li p span {overflow:hidden;white-space:nowrap;text-overflow:ellipsis;display:block}
		.wmk_grid .thumbnail:hover {border-color:#0088CC}
		.wmk_grid .thumbnail>a {position:relative}
		.wmk_grid .thumbnail>a .vid_ico {position:absolute;top:10px;right:10px;display:block;width:16px;height:16px}
		.wmk_grid ul .self_vid .vid_ico {background: url(../img/vid_local.png) no-repeat 0 0}
		.wmk_grid .yt_vid .vid_ico {background: url(../img/vid_yt.png) no-repeat 0 0}
		.wmk_grid .vimeo_vid .vid_ico {background: url(../img/vid_vimeo.png) no-repeat 0 0}
        
		/* small gallery grid */
			#small_grid > ul > li {width:120px}
			#small_grid > ul > li p {line-height:1.3}
			#small_grid > ul > li p span {font-size:11px}
		/* large gallery grid */
			#large_grid > ul > li,#mixed_grid > ul > li {width:220px}
			#large_grid > ul > li p span {width:70%}
	
		.video-container {position: relative;padding-bottom: 55.75%;height: 0;overflow: hidden;background: rgba(0,0,0,.3)}
		.video-container iframe,.video-container object,.video-container embed {position: absolute;top: 0;left: 0;width: 100%;height: 100%}
		#cboxContent .video-js {width:640px !important;height:360px !important}
		
	/* datatables */
		div.dataTables_length {text-align:left}
		div.dataTables_length select{width:75px}
		div.dataTables_filter {text-align:right}
		div.dataTables_filter label {float:none}
		div.dataTables_info{padding-top:8px}
		div.dataTables_paginate{float:right;margin:0}
		.dataTables_wrapper{position:relative;overflow:hidden}
		.dataTables_wrapper table.table {clear:both;margin-bottom:6px!important;max-width:inherit}
		.dataTables_wrapper table.table thead .sorting,table.table thead .sorting_asc,table.table thead .sorting_desc,table.table thead .sorting_asc_disabled,table.table thead .sorting_desc_disabled{cursor:hand;cursor:pointer}
		.dataTables_wrapper table.table thead .sorting{background: #e9f3f8 url(../lib/datatables/images/sort_both.png) no-repeat center right}
		.dataTables_wrapper table.table thead .sorting_asc{background: #e9f3f8 url(../lib/datatables/images/sort_asc.png) no-repeat center right}
		.dataTables_wrapper table.table thead .sorting_desc{background: #e9f3f8 url(../lib/datatables/images/sort_desc.png) no-repeat center right}
		.dataTables_wrapper table.table thead .sorting_asc_disabled{background: #e9f3f8 url(../lib/datatables/images/sort_asc_disabled.png) no-repeat center right}
		.dataTables_wrapper table.table thead .sorting_desc_disabled{background: #e9f3f8 url(../lib/datatables/images/sort_desc_disabled.png) no-repeat center right}
		.dataTables_wrapper table.dataTable th:active{outline:none}
		.dataTables_wrapper .row {margin:0 -10px 10px}
		.dataTables_wrapper select{min-height:inherit}
		.dataTables_wrapper table + .row{margin-bottom:0;margin-top:10px}
        .dataTables_scrollBody{-webkit-overflow-scrolling:touch}
		.dataTables_wrapper .top,.dataTables_wrapper .bottom{background-color:#f5f5f5;border:1px solid #CCC;padding:15px}
		.top .dataTables_info{float:none}
		.dataTables_empty{text-align:center!important;font-size:15px;background:#fff!important;padding:20px 0!important}
		.dataTables_scroll{clear:both;margin-bottom:10px}
		.dataTables_scrollHeadInner table.table-bordered{-webkit-border-radius:4px 4px 0 0;border-radius:4px 4px 0 0;border-bottom:none}
		.dataTables_scrollHeadInner table.table-bordered,.dataTables_scrollHeadInner table.table-bordered thead{border-bottom:none}
		.dataTables_scrollBody table{border-top:none}
		.dataTables_processing{position:absolute;top:50%;margin-top:-24px;z-index:100;left:50%;width:250px;margin-left:-125px;border:1px solid #ddd;text-align:center;color:#000;background:#fff;font-size:15px!important;-webkit-box-shadow:0 0 4px rgba(0,0,0,0.2);-moz-box-shadow:0 0 4px rgba(0,0,0,0.2);-ms-box-shadow:0 0 4px rgba(0,0,0,0.2);box-shadow:0 0 4px rgba(0,0,0,0.2);padding:10px 0}
		.dataTables_wrapper .center{text-align:center}
		.dataTables_wrapper .details,.dataTables_wrapper .details:hover{background:#ddd!important}
		.dataTables_wrapper .details table td{background:#fff!important}
		.dataTables_scrollHeadInner table.table,.dataTables_wrapper .details table.table{margin:0!important}
        .dataTables_wrapper .dt_actions {float:left;margin-right:20px}
		.dataTables_empty {cursor:default}
		.dTableR {width:100% !important}
        .col_vis_menu {float:right}
        .dataTables_paginate {height:auto}
        .DTTT_collection_background {display:none}
		div.dataTables_length select{width:60px;padding:3px;border:none;font-size:13px}
		div.dataTables_filter input {min-height:26px;height:26px;padding:2px 8px;min-height:20px;line-height:20px;font-size:13px;border:none;width:170px}
		div.dataTables_length select,
		div.dataTables_filter input {border:1px solid #bbb}
		.dataTables_paginate .pagination {margin:0}

	/* floating header list */
		.-list-container {height:400px;padding:0;margin:0}
		.-list-container dd,.-list-container li,.-list-container .-list-fakeheader {list-style:none;margin:0;padding:0 10px;line-height:34px;height:34px;border-bottom:1px solid #efefef;margin:0;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}
		.-list-container dt,.-list-container .list_heading,.-list-container .-list-fakeheader {line-height:34px;height:34px;padding:0 10px;border-bottom:none;background: #f9f9f9;background: -moz-linear-gradient(top,  #f9f9f9 0%, #ececec 100%);background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f9f9f9), color-stop(100%,#ececec));background: -webkit-linear-gradient(top,  #f9f9f9 0%,#ececec 100%);background: linear-gradient(top,  #f9f9f9 0%,#ececec 100%);border-bottom: 1px solid #d6d6d6}
		.-list-container .list_heading h3 {font-size:13px;line-height:36px}
		.-list-container {border:1px solid #ccc;-webkit-border-radius: 4px;-moz-border-radius: 4px;-ms-border-radius: 4px;border-radius: 4px;margin:0;overflow-x:hidden}
		.ui-list {overflow:hidden}
		.ui-list ul {margin:0}
		#list-buttons {margin:0 0 6px}
        #list-buttons .btn {margin:0 4px 4px 0}
        .list-outer {position:relative;padding:30px 0}
        .slide-nav {cursor:pointer;position:absolute;left:50%;margin-left:-28px;height:26px;border-style:solid;border-color:#ccc;background:#f9f9f9;padding:5px 20px}
        .slide-up {top:5px;border-width:1px 1px 0;-webkit-border-radius: 4px 4px 0px 0px;border-radius: 4px 4px 0px 0px}
        .slide-down {bottom:5px;border-width:0px 1px 1px;-webkit-border-radius: 0px 0px 4px 4px;border-radius: 0px 0px 4px 4px}
        .slide-nav i {opacity:.6}
		.slide-nav:hover {background:#f0f0f0}
        .slide-nav:hover i {opacity:1}
		
	/*  responsive table */
		.mediaTableWrapper{position:relative}
		.mediaTableWrapperWithMenu{padding-top:48px}
		.mediaTableMenu li,.tableMenu li {padding:0 10px}
		.mediaTableMenu{position:absolute;top:0;left:0}
		.activeMediaTable th.optional,.activeMediaTable td.optional{display:none}
		@media (min-width: 1280px) {
			.activeMediaTable th.optional,.activeMediaTable td.optional{display:table-cell;_display:block}
			*+html .activeMediaTable th.optional,*+html .activeMediaTable td.optional{display:block}
		}
		@media (min-width: 980px) {
			.sidebar_hidden .activeMediaTable th.optional,.sidebar_hidden  .activeMediaTable td.optional{display:table-cell;_display:block}
			*+html .sidebar_hidden .activeMediaTable th.optional,*+html .sidebar_hidden .activeMediaTable td.optional{display:block}
		}
		@media (min-width: 480px) and (max-width: 767px) {
			.activeMediaTable th.optional,.activeMediaTable td.optional{display:table-cell;_display:block}
			*+html .activeMediaTable th.optional,*+html .activeMediaTable td.optional{display:block}
		}

	/* tables */
		.table_checkbox {width:13px}
		.table_vam th, .table_vam td {vertical-align: middle}
		.tab-content table.table {margin-bottom: 0 !important}
		
	/* mailbox */
		.mbox .heading {padding-bottom:0}
		.mbox .heading .nav-tabs {margin-bottom:0;border-bottom:none}
		
		.mbox .dataTables_wrapper .row {-webkit-box-shadow: inset 0px 1px 0px 0px #fefefe;box-shadow: inset 0px 1px 0px 0px #fefefe;margin:0;padding-bottom:10px;padding:8px 5px;border-width:1px 0;border-style:solid;border-color:#ddd;background:#f3f3f3}
		.mbox .dataTables_wrapper .row label {margin-bottom:0;line-height:28px}
		.mbox .dataTables_wrapper .row div.dataTables_info {padding-top:0;line-height:26px;color:#404040}
		.dataTables_wrapper table.mbox_table {margin:0 !important}
		.mbox_table tbody {color:#404040}
		.mbox_table .select_msg {margin-top:-2px;position:relative}
		.mbox_table .rowChecked td {background:#fffeed}
		.mbox .unread {font-weight:700}
		.js .mbox_star {display:none}
		.mbox_star {visibility:hidden;cursor:pointer}
		.mbox_star.splashy-star_full {visibility:visible}
		#mail_recipients {list-style:none;margin:0 0 6px}	
		
    /* chat */
        .chat_box select {margin:0}
		.chat_box .chat_content {border:1px solid #ddd;-webkit-border-radius:6px;border-radius:6px}
		.chat_box .chat_message {margin-bottom:10px}
		.chat_box .chat_editor_box {margin:10px;border:1px solid #ddd;-webkit-border-radius:6px;border-radius:6px;min-height:40px}
		.chat_box .chat_editor_heading {height:20px;line-height:20px;padding:2px 8px 0}
		.chat_box .chat_editor_box textarea {border:none;margin:0;-webkit-box-shadow:none;box-shadow:none;-webkit-border-radius: 0px 0px 6px 6px;border-radius: 0px 0px 6px 6px}
		.chat_box .chat_editor_box .send_btns {margin:5px}
		.chat_box .chat_heading {-webkit-box-shadow: inset 0px 1px 0px 0px #fefefe;box-shadow: inset 0px 1px 0px 0px #fefefe;font-size:12px;font-weight:700;color:#444;padding:6px 12px;font-size:13px;height:34px;line-height:22px;background:#f4f4f4;border-bottom:1px solid #ddd;-webkit-border-radius: 6px 6px 0px 0px;border-radius: 6px 6px 0px 0px} 
        .chat_box .chat_heading .chat_close {opacity:.8;cursor:pointer}
		.chat_box .msg_window {height:300px;overflow-y:auto;margin:5px 10px}
		.chat_box .chat_msg {margin:10px 5px}
		.chat_box .chat_msg .chat_msg_heading {font-size:11px;padding:0 5px}
		.chat_box .chat_msg .chat_msg_body {padding:4px 8px;border:1px solid #ddd;background:#fafafa;-webkit-border-radius: 6px;-moz-border-radius: 6px;-ms-border-radius: 6px;border-radius: 6px}
		.chat_box .chat_msg .chat_msg_date {float:right}
		.chat_box .chat_msg .chat_user_name {color:#CC333F;font-weight:700}
		.chat_box .chat_msg p {margin:0}
		.chat_box .chat_sidebar {border:1px solid #ddd;-webkit-border-radius: 6px;-moz-border-radius: 6px;-ms-border-radius: 6px;border-radius: 6px} 
		.chat_box .chat_sidebar .chat_user_list {margin:0;padding:0}
        .chat_box .chat_sidebar .chat_user_list li {list-style:none;border-bottom:1px solid #e6e6e6;overflow:hidden} 
        .chat_box .chat_sidebar .chat_user_list li:last-child {border:none}
		.chat_box .chat_sidebar .chat_user_list li input {float:left;margin:15px 0 0 6px}
		.chat_box .chat_sidebar .chat_user_list li a {display:block;margin-left:16px;padding:5px 20px 5px 12px;line-height:32px}
        .chat_box .chat_sidebar .chat_user_list li a:hover {text-decoration:none}
		.chat_box .chat_sidebar .chat_user_list li img {vertical-align:top;-webkit-border-radius: 4px;-moz-border-radius: 4px;-ms-border-radius: 4px;border-radius: 4px;margin-right:5px;border:1px solid #ddd}
		.chat_box .chat_sidebar .chat_user_list li.online a {background: url(../img/bullet_green.png) no-repeat 98% center} 
        .chat_box .chat_sidebar .chat_user_list li.offline a {background: url(../img/bullet_red.png) no-repeat 98% center;cursor:default} 
		.chat_box .chat_sidebar .chat_user_list li.active {background-color:#ffffef}
		.chat_box .chat_sidebar .chat_user_list li a span {color:#888;font-size:11px}

	/*  Calculator */
		#calc {padding:10px 0 0}
		#calc input.btn {width:44px;text-align:center}
		#calc .control-group {padding-left:12px}
		
	/* vcard */	
		.vcard .thumbnail {float:left}
		.vcard > ul {list-style:none;margin:10px 0 0 120px;overflow:hidden}
		.vcard > ul > li:first-child {border-top:1px dashed #dcdcdc}
		.vcard > ul > li {padding:8px;border-bottom:1px dashed #dcdcdc;overflow:hidden}
		.vcard .item-key {float:left;color:#888}
		.vcard .vcard-item {margin-left:120px}
		.vcard .v-heading {background:#F0F9FF;font-weight:700}
		.vcard .v-heading span {font-weight:100;font-size:11px;color:#666}
		.vcard .item-list-more,.vcard .thumbnail.item-list-more {display:none}

    /* document view */
        .doc_view {border:1px solid #ddd;-webkit-border-radius: 6px;-moz-border-radius: 6px;-ms-border-radius: 6px;border-radius: 6px;margin-bottom:10px}
        .doc_view .doc_view_header dl {margin-bottom:0;-webkit-background-clip:border;-moz-background-clip:border-box;background-clip:border-box;background-color:#f8f8f8;padding:10px 10px 6px;margin-top:0;border-bottom:1px solid #ddd;-webkit-border-radius: 6px 6px 0 0;-moz-border-radius: 6px 6px 0 0;-ms-border-radius: 6px 6px 0 0;border-radius: 6px 6px 0 0}
        .doc_view .doc_view_header dd {margin-bottom:4px}
        .doc_view .doc_view_header dd,.doc_view .doc_view_header dt {line-height:20px}
		.doc_view .doc_view_content {padding:20px}
		.doc_view .doc_view_footer {line-height:28px;-webkit-background-clip:border;-moz-background-clip:border-box;background-clip:border-box;background-color:#f8f8f8;padding:8px 20px;border-top:1px solid #ddd;-webkit-border-radius: 0 0 6px 6px;-moz-border-radius: 0 0 6px 6px;-ms-border-radius: 0 0 6px 6px;border-radius: 0 0 6px 6px}
	
	/* sortable elements */
        .ui-sortable-placeholder { border: 2px dashed #aaa;background:transparent;visibility: visible !important; height: 40px !important;margin-bottom:20px}
		.ui-sortable-helper {z-index:2100 !important}
		.ui-sortable-placeholder * { visibility: hidden; }
		.ui-sortable {min-height:40px !important}
		.ui-sortable .w-box-header {cursor:move}
		.sort-disabled .w-box-header {cursor:default}	
		.sort_ph {background:#f8f8f8}

    /* submenus */
        
		.caret-right {display: inline-block;position:absolute;top: 8px;right: 10px;width: 0;height: 0;vertical-align: top;border-left: 4px solid #000;border-top: 4px solid transparent;border-bottom: 4px solid transparent;content: "";opacity: 0.6;filter: alpha(opacity=60)}
		.dropdown-menu .sub-menu {left: 100%;position: absolute;top: 0;visibility: hidden;margin-top: -1px}
        .dropdown-menu li {position:relative}
	    .dropdown-menu > li > a:hover,
		.dropdown-menu > li > a:focus {color:#fff;}
		.dropdown-menu .sub-open {visibility: visible;display: block}
        .navbar .sub-menu:before {border-bottom: 7px solid transparent;border-left: none;border-right: 7px solid rgba(0, 0, 0, 0.2);border-top: 7px solid transparent;left: -7px;top: 5px}
        .navbar .sub-menu:after {
            border-top: 6px solid transparent;
            border-left: none;
            border-right: 6px solid #fff;
            border-bottom: 6px solid transparent;
            top: 6px;
            left: -6px;
        }
		
		.sub-dropdown > a:hover .caret-right, .sub-dropdown.active > a .caret-right, .sub-dropdown.active > a:hover .caret-right {border-left: 4px solid #fff;opacity:1;filter: alpha(opacity=100);}
		.navHover .dropdown-menu {display:block}
		.navHover > a:after {display:block;width:100%;content:'';height:2px;position:absolute;bottom:-3px}

    /* blog */
        .blog_content .toolbar { border-width: 1px 0; border-style: dashed; border-color: #ddd; margin-bottom: 10px; padding: 4px 0; }
        .blog_content .toolbar-icons a { display: block; padding: 2px 4px; background: #777; margin-right: 4px; float: left; }
        .blog_content .toolbar .toolbar_text { font-size: 12px; line-height: 24px; padding: 0 5px; }
        .blog_content { padding: 10px }
        .blog_content .blog_item + .blog_item { margin-top: 30px }
        .blog_content .blog_item + .pagination { margin-top: 40px }
        .blog_content h1 { font-size: 26px; font-family: "PT Sans",sans-serif; font-weight: 300; margin-bottom: 10px; }
        .blog_content .media + p { margin-top: 10px }
        .blog_content .toolbar { padding: 4px; margin-bottom: 20px; }

	/* to top */
		#toTop{display:none;text-decoration:none;position:fixed;bottom:10px;right:0;overflow:hidden;width:29px;height:32px;border:none;text-indent:100%;background:url(../img/ui.totop.png) no-repeat 0 0}
		#toTopHover{background:url(../img/ui.totop.png) no-repeat 0 -32px;width:29px;height:32px;display:block;overflow:hidden;float:left;opacity:0;-moz-opacity:0;filter:alpha(opacity=0)}
		#toTop:active,#toTop:focus{outline:none}

	/* backgrounds */
		.ptrn_a .main_content {background-image: url(../img/bg_a.png)}
		.ptrn_b .main_content {background-image: url(../img/bg_b.png)}
		.ptrn_c .main_content {background-image: url(../img/bg_c.png)}
		.ptrn_d .main_content {background-image: url(../img/bg_d.png)}
		.ptrn_e .main_content {background-image: url(../img/bg_e.png)}

	/* Style switcher */
		.ssw_trigger{position:fixed;top:62px;right:-1px;z-index:1001;text-decoration: none;font-size: 14px;color:#fff !important;padding: 2px 5px;background:#333;display: block;-webkit-border-top-left-radius: 8px;-webkit-border-bottom-left-radius: 8px;-moz-border-radius-topleft: 8px;-moz-border-radius-bottomleft: 8px;border-top-left-radius: 8px;border-bottom-left-radius: 8px;border:1px solid transparent}
		.ssw_trigger.active {border-color:#555}
		.style_switcher {font-size:12px;z-index:1000;position:fixed;top:54px;right:0;display:none;background:#000;background: rgba(0,0,0,.8);border:1px solid #111;width: 264px;height: auto;padding: 30px 30px 30px 50px;color:#f5f5f5}
		.style_switcher p {margin:0 0 6px;font-weight:700}
		.style_item {display:block;margin-right:6px;height:24px;width:24px;border:2px solid #fff;float:left;text-indent:-9999px}
		.style_switcher .ptrn_def {background: #fff }
		
		.style_switcher .ssw_ptrn_a {background: #fff url(../img/bg_a.png)}
		.style_switcher .ssw_ptrn_b {background: #fff url(../img/bg_b.png)}
		.style_switcher .ssw_ptrn_c {background: #fff url(../img/bg_c.png)}
		.style_switcher .ssw_ptrn_d {background: #fff url(../img/bg_d.png)}
		.style_switcher .ssw_ptrn_e {background: #fff url(../img/bg_e.png)}
		
		.style_switcher .dark_theme {background:#000}
		.style_switcher .blue_theme {background:#067ead}
		.style_switcher .brown_theme {background:#b47f44}
		.style_switcher .green_theme {background:#8da452}
		.style_switcher .eastern_blue_theme {background:#1294af}
		.style_switcher .tamarillo_theme {background:#af2c36}
		.style_active {border-color:#6fd05b}
		.style_switcher label {color:#fff}


	/* editable elements */
    .editable-input .datepicker table tr td, .editable-input .datepicker table tr th {background:transparent}
    .editable-input .tt-query {padding-left:12px}
	
	/* invoice */
		.invoice_heading {font-size:32px;font-family: "PT Sans",sans-serif;}
		td.invoice_tar,th.invoice_tar {text-align:right}
		
	/* media queries */

		@media (min-width: 980px) {
			.sidebar {position:fixed;top:40px;left:0;margin-left:0;bottom:0;background:#eee}
			.navbar-fixed-top .nav-collapse {height:40px !important}
			/* fixed layout */
			.gebo-fixed {max-width:940px;margin:0 auto !important}
			.gebo-fixed header .container-fluid {max-width:940px;margin:0 auto}
			.gebo-fixed #maincontainer {border-right:1px solid #ccc;border-left:1px solid #ccc}
			.gebo-fixed .sidebar {left:50%;margin-left:-469px}
			.gebo-fixed .sidebar_switch {left:50%;margin-left:-250px}
			.gebo-fixed.sidebar_hidden .sidebar_switch {margin-left:-465px;left:50%}
			.gebo-fixed .search_page .box_view .search_item {width:48%;height:210px;margin:0 0 2% 2%}
            .gebo-fixed .activeMediaTable th.optional,.gebo-fixed .activeMediaTable td.optional{display:none}
			
			.sidebar_right .sidebar {left:auto;right:-1px;background:#eee;border-left:1px solid #ccc;border-right:none}
			.sidebar_right .sidebar_switch {left:auto;right:6px}
			.sidebar_right .main_content {margin-right:240px;margin-left:0;border-left:none}
			.sidebar_right.sidebar_hidden #maincontainer {background: #fff}
			.sidebar_right.sidebar_hidden .main_content {margin:0}
			
			.gebo-fixed.sidebar_right .sidebar {left:auto;right:50%;margin-left:0;margin-right:-469px}
			.gebo-fixed.sidebar_right .sidebar_switch {left:auto;right:50%;margin-left:0;margin-right:-465px}
			.gebo-fixed.sidebar_hidden.sidebar_right .sidebar_switch {laeft:auto;margin-left:0;margin-right:-465px;right:50%}
			.selectnav {display:none}
		}
			
		@media (max-width: 1279px) {
			.search_page .box_view .search_item {width:49%}
		}
		
		@media (max-width: 979px) {
			#maincontainer {background: #fff}
			.row > [class*="span"],.navbar-fixed-top {margin:0 !important}
			.navbar-fixed-top .container {padding:0 !important}
			.navbar .brand {width:auto;line-height:18px;margin:9px 0 9px 34px}
			.navbar .user_menu .dropdown-menu {margin:10px 10px 0 0;left:0 !important;right:auto}
			.navbar-nav .open .dropdown-menu > li > a, .navbar-nav .open .dropdown-menu .dropdown-header {padding:5px 10px}
			.navbar .user_menu > li > a {padding:0 10px 0 0}
			.navbar-inner {position:relative;z-index:990;min-height:28px}
			.nav-collapse a {color:#fff !important}
			.nav-collapse .dropdown-menu li {padding:0}
			.nav-collapse .dropdown-menu li a {font-weight:400}
			.nav-collapse .sub-menu {display:block;left:0;right:auto;position:relative;visibility:visible}
			.nav-collapse .caret-right {display:none}
			.navbar-nav > li {display:inline-block;vertical-align:bottom;float: none}
			.btn-navbar {margin-top:2px !important;padding:2px 8px !important}
			.user_menu .dropdown {margin-left:10px}
			.main_content {margin:0 !important;border:none !important;padding:56px 14px 20px}
			.main_content .row + .row {margin-top:20px}
			.sidebar {height: 400px;position:absolute;top:39px;left:0;background:#f1f1f1;border-width:0 1px;border-style:solid;border-color:#ccc;margin:0;-webkit-box-shadow: 1px 1px 4px rgba(0,0,0,0.2);-moz-box-shadow: 1px 1px 4px rgba(0,0,0,0.2);box-shadow: 1px 1px 4px rgba(0,0,0,0.2);z-index:1000;-webkit-border-radius: 0px 5px 5px 0px;border-radius: 0px 5px 5px 0px}
			.sidebar:after {border-bottom: 6px solid #fff;border-left: 6px solid transparent;border-right: 6px solid transparent;content: "";display: inline-block;left: 15px;position: absolute;top: -6px}
            .sidebar_hidden .sidebar {border:none}
			.sidebar_switch {position:absolute;z-index:1040;left:5px !important;top:12px;padding:0 10px}
			.off_switch {background-position:center 1px}
			.on_switch {background-position:center -17px}
            .sidebar_inner {padding-bottom:0 !important}
            div.sticky-queue {z-index:10000;top:0}
            .btn_menu {display:block;clear:both;padding:8px 0 6px;border-top:1px solid rgba(255,255,255,.2);box-shadow: 0 -1px 0 rgba(0,0,0,.2);text-align:center;position:relative;top:4px;cursor:pointer}
            .btn_menu span {opacity:.8}
			.ssw_trigger {display:none}
			#mobile-nav {display:none}
			.selectnav {margin:8px 0 0 20px;margin-bottom:0}
		}
		
		@media (max-width: 767px) {
			.main_content {padding-top:20px}
			.navbar-fixed-top {position:static}
			.navbar .nav.pull-right {float:left !important;clear:both;margin:6px 10px 10px}
			.row > [class*="span"] + [class*="span"] {margin-top:20px}
			.row input[class*="span"],.row textarea[class*="span"],.row select[class*="span"] {width:80%}
			.input-prepend, .input-append {margin-bottom:10px}
			form .row > [class*="span"] + [class*="span"] {margin-top:0}
			.login_page .content_b {margin: 0 20px}
			.search_page .box_view .search_item {width:99%;margin-bottom:3%}
			.error_page .error_box {background:none;width:100%}
			.error_page .error_box h1 {padding:40px 20px 0}
			.error_page .error_box p {margin:0 20px 10px}
			.error_page .error_box .back_link {margin:0 20px}
            .style_switcher {display:none}
			.dshb_icoNav {text-align:center}
			.dshb_icoNav li {float:none;display:inline-block}
			#cboxContent .video-js {width:100% !important;height:100% !important}
			.vcard > .thumbnail {display:none}
			.vcard > ul {margin:0}
			.item-list .thumbnail {float:none;display:inline-block;margin:0 10px 10px 0}
			.selectnav {margin:5px 9px 0;display:block;clear:both}
            .dataTables_wrapper table + .row > div {text-align:left}
            .dataTables_wrapper table + .row > div + div,.dataTables_wrapper .dataTables_scroll + .row > div + div {padding-top:10px}
            .dataTables_wrapper table + .row .dataTables_info {padding-top:0}
            .dataTables_wrapper table + .row .dataTables_info {padding-top:0}
            .dataTables_wrapper table + .row .dataTables_paginate,.dataTables_wrapper .dataTables_scroll + .row .dataTables_paginate {float:none}
            div.dataTables_filter {text-align:left}
            div.DTTT.btn-group {margin-bottom:5px}
            .col_vis_menu {float:left}
            .dataTables_wrapper {overflow-x:auto;padding-bottom:10px}
            .blog_content .media > a { float:none;margin-bottom:10px;display:block;margin-left:0 }
			.navbar-nav .open .dropdown-menu {background:#fff;border:1px solid rgba(0, 0, 0, 0.15);position:absolute;-webkit-box-shadow:0 6px 12px rgba(0, 0, 0, 0.176);-moz-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.176);box-shadow: 0 6px 12px rgba(0, 0, 0, 0.176);float:left}
			.navbar-default .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {color:#fff}
		}
		
		@media (max-width: 479px) {
			.login_page .login_box {width:100%}
			.login_page .cnt_b {padding:20px 0;width:68%;margin:0 auto}
            .-list-container {height:300px}
			.ov_boxes li {padding:0 2px}
            .navbar .user_menu .dropdown-menu {left:0 !important;right:auto !important}
            .navbar .user_menu .dropdown-menu:after {left:10px !important;right:auto !important}
            .navbar .user_menu .dropdown-menu:before {left:9px !important;right:auto !important}
		}
		
		@media (min-width: 1681px) {
			body {max-width:1680px;margin:0 auto !important;}
			.modal-open	{padding-right: 0 !important;}
			header .container-fluid,header .container {max-width:1680px;margin:0 auto}
			#maincontainer {border-right:1px solid #ccc;border-left:1px solid #ccc}
			.main_content {border-left:none}
			.sidebar {left:50%;margin-left:-839px}
			.sidebar_switch {left:50%;margin-left:-620px}
			.sidebar_hidden .sidebar_switch {margin-left:-832px;left:50%}
			.sidebar_right .sidebar {right:50%;margin-left:0;margin-right:-839px}
			.sidebar_right .sidebar_switch {left:auto;right:50%;margin-left:0;margin-right:-836px}
		}