from dbutils.pooled_db import PooledDB
import sqlite3
from contextlib import contextmanager

# sqlite3 连接池配置
SQLITE_DB_PATH = './data/data.db'
# SQLITE_DB_PATH = '../data/data.db'
POOL_CONFIG = {
    'maxconnections': 10,
    'blocking': True,
    'check_same_thread': False
}


# sqlite3 连接池
class DatabasePool:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.pool = None
            cls._instance.init_pool()
        return cls._instance

    def init_pool(self):
        if self.pool is None:
            def connector():
                conn = sqlite3.connect(
                    database=SQLITE_DB_PATH,
                    timeout=10.0,
                    check_same_thread=False,
                    isolation_level=None,
                )
                conn.execute('PRAGMA journal_mode=WAL')
                conn.execute('PRAGMA synchronous=NORMAL')
                conn.execute('PRAGMA cache_size=10000')
                conn.execute('PRAGMA busy_timeout=3000')
                return conn

            self.pool = PooledDB(
                creator=connector,
                # database=SQLITE_DB_PATH,
                mincached=2,
                maxcached=5,
                maxconnections=20,
                blocking=True,
                # check_same_thread=False,
                ping=1
            )

    @contextmanager
    def get_connection(self, autocommit=True):
        if self.pool is None:
            self.init_pool()
        conn = self.pool.connection()
        try:
            yield conn
            if autocommit:
                conn.commit()
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()


# 连接池实例化
db_pool = DatabasePool()
db_pool.init_pool()


