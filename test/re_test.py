import re

a = """<a class="btn btn-primary" style="cursor: pointer" href="/proxy/Craft/Edit?id=3">
                                  <span class="glyphicon glyphicon-edit"></span>
                                  编辑
                              </a>"""

print(re.sub('href="/proxy/Craft/Edit\?id=(\w)"',
             lambda m: f'onclick="return window.parent.open("/proxy/Craft/Show?id={m.group(1)}", "_blank")"', a, re.S))
