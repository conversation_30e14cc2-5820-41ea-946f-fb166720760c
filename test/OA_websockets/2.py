# def str2base64(e):
#     # BASE64_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
#
#     def n_e(index):
#         return "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[index] if 0 <= index < 64 else '='
#
#         # if 0 <= index < 64:
#         #     return BASE64_CHARS[index]
#         # return '='
#
#     if not isinstance(e, str):
#         e = str(e)
#
#     for char in e:
#         if ord(char) > 255:
#             return None
#
#     result = []
#
#
#
#
#

# import re
#
# a = '<challenge xmlns="urn:ietf:params:xml:ns:xmpp-sasl">cj01MjIzMTBjYTA2NGYzYjhiMzFjNDYzOTg1NGVkNTVjMTY5YzA1ODU1LTM1NzctNGZhOS1hN2Q1LWRhZGJhY2U3OTBhMixzPWtDVEQ0cHZHV2xMaHNCTXc4SHRic2pPTnJlOXVCZXptLGk9NDA5Ng==</challenge>'
# b = re.findall('>(.*?)<', a)[0]
# print(b)

import execjs

def getUniqueId():
    js_code = '''
    getUniqueId = function (e) {
        var t = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (function (e) {
            var t = 16 * Math.random() | 0;
            return ("x" === e ? t : 3 & t | 8).toString(16)
        }));
        return "string" === typeof e || "number" === typeof e ? t + ":" + e : t + ""
    }'''

    js = execjs.compile(js_code)
    return js.call('getUniqueId', 'sendPresence')











