import os
import re
import websocket
import execjs
import base64


def str_base64(str_):
    bytes_data = str_.encode('utf-8')
    return base64.b64encode(bytes_data).decode('utf-8')


def base64_str(str_):
    decoded_bytes = base64.b64decode(str_)
    return decoded_bytes.decode('utf-8')


def get_result(str_):
    with open('2.js', 'r', encoding='utf-8') as f:
        js = execjs.compile(f.read())
        result = js.call('get_result', str_)
        return result


def getUniqueId():
    js_code = '''
    getUniqueId = function (e) {
        var t = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (function (e) {
            var t = 16 * Math.random() | 0;
            return ("x" === e ? t : 3 & t | 8).toString(16)
        }));
        return "string" === typeof e || "number" === typeof e ? t + ":" + e : t + ""
    }'''

    js = execjs.compile(js_code)
    return js.call('getUniqueId', 'sendPresence')


ws_url = 'ws://192.168.0.36:7070/ws/'

headers = {
    'Sec-WebSocket-Protocol': 'xmpp',
}
import threading

global_text = 0
base_str = ''

class MySocket:
    # global global_text
    def __init__(self):
        self.header = {
            'Sec-WebSocket-Protocol': 'xmpp',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) E-Mobile/7.2.6 Chrome/69.0.3497.128 Electron/4.2.12 Safari/537.36'
        }
        self.ws = None

    def connect(self):
        self.ws = websocket.WebSocketApp(ws_url,
                                         header=headers,
                                         on_message=self.on_message,
                                         on_open=self.on_open,
                                         on_error=self.on_error,
                                         on_close=self.on_close
                                         )

        threading.Thread(target=self.ws.run_forever, daemon=True).start()

    def on_message(self, ws_, message):
        global global_text, base_str
        print('接收到消息: ', message)
        if '<challenge xmlns="urn:ietf:params:xml:ns:xmpp-sasl">' in message:
            base_str = re.findall('>(.*?)<', message)[0]
        global_text += 1
        return message

    def on_open(self, ws_):
        print('打开连接：', '<open to="weaver" version="1.0" xmlns="urn:ietf:params:xml:ns:xmpp-framing"/>')
        self.ws.send('<open to="weaver" version="1.0" xmlns="urn:ietf:params:xml:ns:xmpp-framing"/>')

    def on_error(self, ws_, error):
        print('连接错误: ', error)

    def on_close(self, ws_, code, close_msg):
        print('关闭连接: ', code, close_msg)

    def send_msg(self, msg):
        print('发送： ', msg)
        if self.ws and self.ws.sock and self.ws.sock.connected:
            self.ws.send(msg)

    def close(self):
        self.ws.close()


oa_socket = MySocket()
oa_socket.connect()

while 1:
    if global_text == 2:
        global_text += 1
        oa_socket.send_msg(
            '<auth mechanism="SCRAM-SHA-1" xmlns="urn:ietf:params:xml:ns:xmpp-sasl">biwsbj05NDN8bCxyPTUyMjMxMGNhMDY0ZjNiOGIzMWM0NjM5ODU0ZWQ1NWMx</auth>')
    elif global_text == 4:
        global_text += 1
        # print(f'\n{base_str}\n')

        oa_socket.send_msg(
            f'<response xmlns="urn:ietf:params:xml:ns:xmpp-sasl">{get_result(base_str)}</response>')
    elif global_text == 6:
        global_text += 1
        oa_socket.on_open('1')
    elif global_text == 9:
        global_text += 1
        oa_socket.send_msg(
            '<iq id="_bind_auth_2" type="set" xmlns="jabber:client"><bind xmlns="urn:ietf:params:xml:ns:xmpp-bind"><resource>pc</resource></bind></iq>')
    # elif global_text == 11:
    #     global_text += 1
    #     oa_socket.send_msg(
    #         '<iq id="_session_auth_2" type="set" xmlns="jabber:client"><session xmlns="urn:ietf:params:xml:ns:xmpp-session"/></iq>')
    # elif global_text == 13:
    #     global_text += 1
    #     oa_socket.send_msg(
    #     f'<presence id="{getUniqueId()}" xmlns="jabber:client"><device>pc</device><priority>1</priority></presence>')
        # oa_socket.send_msg(
        #     '<iq id="bacb1ecf-30ed-4ee9-bfcf-893eafd23e7b" to="weaver" type="get" xmlns="http://weaver.com.cn/getConversation"><query>{&quot;pageSize&quot;:100,&quot;lasttime&quot;:&quot;&quot;,&quot;groupTop&quot;:1}</query></iq>'
        # )
    elif global_text == 11:
        global_text += 1
        oa_socket.send_msg(
            f'<iq id="bacb1ecf-30ed-4ee9-bfcf-893eafd23e7b" to="weaver" type="get" xmlns="http://weaver.com.cn/status"><query>[&quot;623&quot;,&quot;157&quot;,&quot;367&quot;,&quot;98&quot;,&quot;425&quot;,&quot;95&quot;]</query></iq>')

# try:
#     ws.send('<open to="weaver" version="1.0" xmlns="urn:ietf:params:xml:ns:xmpp-framing"/>')
#     while True:
#         data = ws.receive()
#         print('receive:', data)
# except KeyboardInterrupt:
#     ws.close()
