// const en = require('crypto')

function str_base64(e) {
    function r_(e) {
        if (e >= 0 && e < 64) return "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[e]
    }

    e_ = function (e) {
        var t = void 0;
        for (e = "" + e, t = 0; t < e.length; t++) if (e.charCodeAt(t) > 255) return null;
        var n = "";
        for (t = 0; t < e.length; t += 3) {
            var o = [void 0, void 0, void 0, void 0];
            o[0] = e.charCodeAt(t) >> 2,
                o[1] = (3 & e.charCodeAt(t)) << 4,
            e.length > t + 1 && (o[1] |= e.charCodeAt(t + 1) >> 4,
                o[2] = (15 & e.charCodeAt(t + 1)) << 2),
            e.length > t + 2 && (o[2] |= e.charCodeAt(t + 2) >> 6,
                o[3] = 63 & e.charCodeAt(t + 2));
            for (var a = 0; a < o.length; a++) "undefined" === typeof o[a] ? n += "=" : n += r_(o[a])
        }
        return n
    }
    return e_(e)
}


utf16to8 = function (e) {
    var t, n, r = "", o = e.length;
    for (t = 0; t < o; t++)
        (n = e.charCodeAt(t)) >= 0 &&
        n <= 127 ? r += e.charAt(t) : n > 2047 ? (r += String.fromCharCode(224 | n >> 12 & 15),
            r += String.fromCharCode(128 | n >> 6 & 63),
            r += String.fromCharCode(128 | n >> 0 & 63)) : (r += String.fromCharCode(192 | n >> 6 & 31),
            r += String.fromCharCode(128 | n >> 0 & 63));
    return r
}

function base64_str(e) {
    if ((e = (e = "" + e).replace(/[ \t\n\f\r]/g, "")).length % 4 === 0 && (e = e.replace(/==?$/, "")), e.length % 4 === 1 || /[^+/0-9A-Za-z]/.test(e)) return null;
    // console.log(e)
    for (var t, n, r = "", o = 0, a = 0, s = 0; s < e.length; s++) o <<= 6, o |= (t = e[s], n = void 0, (n = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(t)) < 0 ? void 0 : n), 24 === (a += 6) && (r += String.fromCharCode((16711680 & o) >> 16), r += String.fromCharCode((65280 & o) >> 8), r += String.fromCharCode(255 & o), o = a = 0);
    return 12 === a ? (o >>= 4, r += String.fromCharCode(o)) : 18 === a && (o >>= 2, r += String.fromCharCode((65280 & o) >> 8), r += String.fromCharCode(255 & o)), r
}


function r(e, t) {
    e[t >> 5] |= 128 << 24 - t % 32, e[15 + (t + 64 >> 9 << 4)] = t;
    var n, r, s, c, u, d, p, m, f = new Array(80), h = 1732584193, g = -271733879, b = -1732584194,
        v = 271733878, y = -1009589776;
    for (n = 0; n < e.length; n += 16) {
        for (c = h, u = g, d = b, p = v, m = y, r = 0; r < 80; r++) f[r] = r < 16 ? e[n + r] : l(f[r - 3] ^ f[r - 8] ^ f[r - 14] ^ f[r - 16], 1), s = i(i(l(h, 5), o(r, g, b, v)), i(i(y, f[r]), a(r))), y = v, v = b, b = l(g, 30), g = h, h = s;
        h = i(h, c), g = i(g, u), b = i(b, d), v = i(v, p), y = i(y, m)
    }
    return [h, g, b, v, y]
}

function o(e, t, n, r) {
    return e < 20 ? t & n | ~t & r : e < 40 ? t ^ n ^ r : e < 60 ? t & n | t & r | n & r : t ^ n ^ r
}

function a(e) {
    return e < 20 ? 1518500249 : e < 40 ? 1859775393 : e < 60 ? -1894007588 : -899497514
}

function s(e, t) {
    var n = c(e);
    n.length > 16 && (n = r(n, 8 * e.length));
    for (var o = new Array(16), a = new Array(16), s = 0; s < 16; s++) o[s] = 909522486 ^ n[s], a[s] = 1549556828 ^ n[s];
    var i = r(o.concat(c(t)), 512 + 8 * t.length);
    return r(a.concat(i), 672)
}

function i(e, t) {
    var n = (65535 & e) + (65535 & t);
    return (e >> 16) + (t >> 16) + (n >> 16) << 16 | 65535 & n
}

function l(e, t) {
    return e << t | e >>> 32 - t
}

function c(e) {
    for (var t = [], n = 0; n < 8 * e.length; n += 8) t[n >> 5] |= (255 & e.charCodeAt(n / 8)) << 24 - n % 32;
    return t
}

function u(e) {
    for (var t, n, r = "", o = 0; o < 4 * e.length; o += 3) for (t = (e[o >> 2] >> 8 * (3 - o % 4) & 255) << 16 | (e[o + 1 >> 2] >> 8 * (3 - (o + 1) % 4) & 255) << 8 | e[o + 2 >> 2] >> 8 * (3 - (o + 2) % 4) & 255, n = 0; n < 4; n++) 8 * o + 6 * n > 32 * e.length ? r += "=" : r += "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t >> 6 * (3 - n) & 63);
    return r
}

function d(e) {
    for (var t = "", n = 0; n < 32 * e.length; n += 8) t += String.fromCharCode(e[n >> 5] >>> 24 - n % 32 & 255);
    return t
}

var sha1 = {
    b64_hmac_sha1: function (e, t) {
        return u(s(e, t))
    }, b64_sha1: function (e) {
        return u(r(c(e), 8 * e.length))
    },
    binb2str: d,
    core_hmac_sha1: s,
    str_hmac_sha1: function (e, t) {
        return d(s(e, t))
    }, str_sha1: function (e) {
        return d(r(c(e), 8 * e.length))
    }
}


// const sha1 = require('./sha1.js')

var onChallenge = function (e, t) {
    for (var n = void 0, r = void 0, o = void 0, a = void 0, s = void 0, d = void 0, p = void 0, m = void 0, f = "c=biws,", h = e._sasl_data["client-first-message-bare"] + "," + t + ",", g = e._sasl_data.cnonce, b = /([a-z]+)=([^,]+)(,|$)/; t.match(b);) {
        var v = t.match(b);
        switch (t = t.replace(v[0], ""), v[1]) {
            case"r":
                n = v[2];
                break;
            case"s":
                r = v[2];
                break;
            case"i":
                o = v[2]
        }
    }
    debugger
    if (n.substr(0, g.length) !== g) return e._sasl_data = {}, e._sasl_failure_cb();
    h += f += "r=" + n, r = base64_str(r), r += "\0\0\0";
    var y = utf16to8(e.pass);
    debugger
    for (a = d = sha1.core_hmac_sha1(y, r), p = 1; p < o; p++) {
        for (s = sha1.core_hmac_sha1(y, sha1.binb2str(d)), m = 0; m < 5; m++) a[m] ^= s[m];
        d = s
    }
    a = sha1.binb2str(a);
    var w = sha1.core_hmac_sha1(a, "Client Key"), _ = sha1.str_hmac_sha1(a, "Server Key"),
        S = sha1.core_hmac_sha1(sha1.str_sha1(sha1.binb2str(w)), h);
    for (e._sasl_data["server-signature"] = sha1.b64_hmac_sha1(_, h), m = 0; m < 5; m++) w[m] ^= S[m];
    return f += ",p=" + str_base64(sha1.binb2str(w))
}


var str = "cj1jY2RhN2FmOWRmMTI2MWE2MWMwNDI0ODBjYTIzNzZkZDE4Y2IwODExLWZiMWYtNDkyZC04ODUzLThhMTM0MTRkNWIzOCxzPWtDVEQ0cHZHV2xMaHNCTXc4SHRic2pPTnJlOXVCZXptLGk9NDA5Ng=="

function get_result(base_str) {
    var org_data = base64_str(base_str)
    var cnonce = org_data.slice(2, 34);
    // console.log(org_data, '\n', cnonce)
    var mydata = {
        pass: "hlfccnx4gdh2f158ohejfg0k",
        _sasl_data: {
            "cnonce": cnonce,
            "client-first-message-bare": "n=943|l,r=" + cnonce,
        }
    }
    // console.log(str_base64(onChallenge(mydata, org_data)))
    return str_base64(onChallenge(mydata, org_data))
}


// console.log(get_result(str))
console.log(base64_str('biwsbj05NDN8bCxyPTUyMjMxMGNhMDY0ZjNiOGIzMWM0NjM5ODU0ZWQ1NWMx'))
// console.log(base64_str('0pBgGMAngMARL2ZyKXUPxEkU2nE='))

getUniqueId = function (e) {
    var t = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (function (e) {
        var t = 16 * Math.random() | 0;
        return ("x" === e ? t : 3 & t | 8).toString(16)
    }));
    return "string" === typeof e || "number" === typeof e ? t + ":" + e : t + ""
}


console.log(getUniqueId('sendPresence'))
    'Yz1iaXdzLHI9Y2NkYTdhZjlkZjEyNjFhNjFjMDQyNDgwY2EyMzc2ZGQwOWEyYjhjZi1kYzFiLTQ5YjctYWU1Ny03YjlkNTkwZTA2OTQscD1OeEo5TWI0SGJqdkFTVTl0Q20wRkswUnBpaW89'
    'Yz1iaXdzLHI9Y2NkYTdhZjlkZjEyNjFhNjFjMDQyNDgwY2EyMzc2ZGQwOWEyYjhjZi1kYzFiLTQ5YjctYWU1Ny03YjlkNTkwZTA2OTQscD1OeEo5TWI0SGJqdkFTVTl0Q20wRkswUnBpaW89'
    '<response xmlns="urn:ietf:params:xml:ns:xmpp-sasl">Yz1iaXdzLHI9Y2NkYTdhZjlkZjEyNjFhNjFjMDQyNDgwY2EyMzc2ZGQwOWEyYjhjZi1kYzFiLTQ5YjctYWU1Ny03YjlkNTkwZTA2OTQscD1OeEo5TWI0SGJqdkFTVTl0Q20wRkswUnBpaW89</response>'

    '<iq id="_bind_auth_2" type="set" xmlns="jabber:client"><bind xmlns="urn:ietf:params:xml:ns:xmpp-bind"><resource>pc</resource></bind></iq>'
    '<iq id="_bind_auth_2" type="set" xmlns="jabber:client"><bind xmlns="urn:ietf:params:xml:ns:xmpp-bind"><resource>pc</resource></bind></iq>'















getUniqueId = function (e) {
    var t = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (function (e) {
        var t = 16 * Math.random() | 0;
        return ("x" === e ? t : 3 & t | 8).toString(16)
    }));
    return "string" === typeof e || "number" === typeof e ? t + ":" + e : t + ""
}











