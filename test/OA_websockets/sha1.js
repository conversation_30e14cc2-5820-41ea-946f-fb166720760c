function r(e, t) {
    e[t >> 5] |= 128 << 24 - t % 32, e[15 + (t + 64 >> 9 << 4)] = t;
    var n, r, s, c, u, d, p, m, f = new Array(80), h = 1732584193, g = -271733879, b = -1732584194,
        v = 271733878, y = -1009589776;
    for (n = 0; n < e.length; n += 16) {
        for (c = h, u = g, d = b, p = v, m = y, r = 0; r < 80; r++) f[r] = r < 16 ? e[n + r] : l(f[r - 3] ^ f[r - 8] ^ f[r - 14] ^ f[r - 16], 1), s = i(i(l(h, 5), o(r, g, b, v)), i(i(y, f[r]), a(r))), y = v, v = b, b = l(g, 30), g = h, h = s;
        h = i(h, c), g = i(g, u), b = i(b, d), v = i(v, p), y = i(y, m)
    }
    return [h, g, b, v, y]
}

function o(e, t, n, r) {
    return e < 20 ? t & n | ~t & r : e < 40 ? t ^ n ^ r : e < 60 ? t & n | t & r | n & r : t ^ n ^ r
}

function a(e) {
    return e < 20 ? 1518500249 : e < 40 ? 1859775393 : e < 60 ? -1894007588 : -899497514
}

function s(e, t) {
    var n = c(e);
    n.length > 16 && (n = r(n, 8 * e.length));
    for (var o = new Array(16), a = new Array(16), s = 0; s < 16; s++) o[s] = 909522486 ^ n[s], a[s] = 1549556828 ^ n[s];
    var i = r(o.concat(c(t)), 512 + 8 * t.length);
    return r(a.concat(i), 672)
}

function i(e, t) {
    var n = (65535 & e) + (65535 & t);
    return (e >> 16) + (t >> 16) + (n >> 16) << 16 | 65535 & n
}

function l(e, t) {
    return e << t | e >>> 32 - t
}

function c(e) {
    for (var t = [], n = 0; n < 8 * e.length; n += 8) t[n >> 5] |= (255 & e.charCodeAt(n / 8)) << 24 - n % 32;
    return t
}

function u(e) {
    for (var t, n, r = "", o = 0; o < 4 * e.length; o += 3) for (t = (e[o >> 2] >> 8 * (3 - o % 4) & 255) << 16 | (e[o + 1 >> 2] >> 8 * (3 - (o + 1) % 4) & 255) << 8 | e[o + 2 >> 2] >> 8 * (3 - (o + 2) % 4) & 255, n = 0; n < 4; n++) 8 * o + 6 * n > 32 * e.length ? r += "=" : r += "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t >> 6 * (3 - n) & 63);
    return r
}

function d(e) {
    for (var t = "", n = 0; n < 32 * e.length; n += 8) t += String.fromCharCode(e[n >> 5] >>> 24 - n % 32 & 255);
    return t
}


    b64_hmac_sha1= function (e, t) {
        return u(s(e, t))
    }, b64_sha1= function (e) {
        return u(r(c(e), 8 * e.length))
    },
        binb2str= d,
        core_hmac_sha1= s,
        str_hmac_sha1= function (e, t) {
        return d(s(e, t))
    }, str_sha1= function (e) {
        return d(r(c(e), 8 * e.length))
    }

module.exports = {
    b64_hmac_sha1,
    b64_sha1,
    binb2str,
    core_hmac_sha1,
    str_hmac_sha1, str_sha1
}