var r = function (e, t) {
        var n = (65535 & e) + (65535 & t);
        return (e >> 16) + (t >> 16) + (n >> 16) << 16 | 65535 & n
    }, o = function (e) {
        if ("string" !== typeof e) throw new Error("str2binl was passed a non-string");
        for (var t = [], n = 0; n < 8 * e.length; n += 8) t[n >> 5] |= (255 & e.charCodeAt(n / 8)) << n % 32;
        return t
    }, a = function (e, t, n, o, a, s) {
        return r((i = r(r(t, e), r(o, s))) << (l = a) | i >>> 32 - l, n);
        var i, l
    }, s = function (e, t, n, r, o, s, i) {
        return a(t & n | ~t & r, e, t, o, s, i)
    },
    i = function (e, t, n, r, o, s, i) {
        return a(t & r | n & ~r, e, t, o, s, i)
    },
    l = function (e, t, n, r, o, s, i) {
        return a(t ^ n ^ r, e, t, o, s, i)
    },
    c = function (e, t, n, r, o, s, i) {
        return a(n ^ (t | ~r), e, t, o, s, i)
    },
    u = function (e, t) {
        e[t >> 5] |= 128 << t % 32, e[14 + (t + 64 >>> 9 << 4)] = t;
        for (var n = 1732584193, o = -271733879, a = -1732584194, u = 271733878,
                 d = void 0, p = void 0, m = void 0, f = void 0, h = 0; h < e.length; h += 16)
            d = n, p = o, m = a, f = u, n = s(n, o, a, u, e[h + 0], 7, -680876936),
                u = s(u, n, o, a, e[h + 1], 12, -389564586),
                a = s(a, u, n, o, e[h + 2], 17, 606105819),
                o = s(o, a, u, n, e[h + 3], 22, -1044525330),
                n = s(n, o, a, u, e[h + 4], 7, -176418897),
                u = s(u, n, o, a, e[h + 5], 12, 1200080426),
                a = s(a, u, n, o, e[h + 6], 17, -1473231341),
                o = s(o, a, u, n, e[h + 7], 22, -45705983),
                n = s(n, o, a, u, e[h + 8], 7, 1770035416),
                u = s(u, n, o, a, e[h + 9], 12, -1958414417),
                a = s(a, u, n, o, e[h + 10], 17, -42063),
                o = s(o, a, u, n, e[h + 11], 22, -1990404162),
                n = s(n, o, a, u, e[h + 12], 7, 1804603682),
                u = s(u, n, o, a, e[h + 13], 12, -40341101),
                a = s(a, u, n, o, e[h + 14], 17, -1502002290),
                o = s(o, a, u, n, e[h + 15], 22, 1236535329),
                n = i(n, o, a, u, e[h + 1], 5, -165796510),
                u = i(u, n, o, a, e[h + 6], 9, -1069501632),
                a = i(a, u, n, o, e[h + 11], 14, 643717713),
                o = i(o, a, u, n, e[h + 0], 20, -373897302),
                n = i(n, o, a, u, e[h + 5], 5, -701558691),
                u = i(u, n, o, a, e[h + 10], 9, 38016083),
                a = i(a, u, n, o, e[h + 15], 14, -660478335),
                o = i(o, a, u, n, e[h + 4], 20, -405537848),
                n = i(n, o, a, u, e[h + 9], 5, 568446438),
                u = i(u, n, o, a, e[h + 14], 9, -1019803690),
                a = i(a, u, n, o, e[h + 3], 14, -187363961),
                o = i(o, a, u, n, e[h + 8], 20, 1163531501),
                n = i(n, o, a, u, e[h + 13], 5, -1444681467),
                u = i(u, n, o, a, e[h + 2], 9, -51403784),
                a = i(a, u, n, o, e[h + 7], 14, 1735328473),
                o = i(o, a, u, n, e[h + 12], 20, -1926607734),
                n = l(n, o, a, u, e[h + 5], 4, -378558),
                u = l(u, n, o, a, e[h + 8], 11, -2022574463),
                a = l(a, u, n, o, e[h + 11], 16, 1839030562),
                o = l(o, a, u, n, e[h + 14], 23, -35309556),
                n = l(n, o, a, u, e[h + 1], 4, -1530992060),
                u = l(u, n, o, a, e[h + 4], 11, 1272893353),
                a = l(a, u, n, o, e[h + 7], 16, -155497632),
                o = l(o, a, u, n, e[h + 10], 23, -1094730640),
                n = l(n, o, a, u, e[h + 13], 4, 681279174),
                u = l(u, n, o, a, e[h + 0], 11, -358537222),
                a = l(a, u, n, o, e[h + 3], 16, -722521979),
                o = l(o, a, u, n, e[h + 6], 23, 76029189),
                n = l(n, o, a, u, e[h + 9], 4, -640364487),
                u = l(u, n, o, a, e[h + 12], 11, -421815835),
                a = l(a, u, n, o, e[h + 15], 16, 530742520),
                o = l(o, a, u, n, e[h + 2], 23, -995338651),
                n = c(n, o, a, u, e[h + 0], 6, -198630844),
                u = c(u, n, o, a, e[h + 7], 10, 1126891415),
                a = c(a, u, n, o, e[h + 14], 15, -1416354905),
                o = c(o, a, u, n, e[h + 5], 21, -57434055),
                n = c(n, o, a, u, e[h + 12], 6, 1700485571),
                u = c(u, n, o, a, e[h + 3], 10, -1894986606),
                a = c(a, u, n, o, e[h + 10], 15, -1051523),
                o = c(o, a, u, n, e[h + 1], 21, -2054922799),
                n = c(n, o, a, u, e[h + 8], 6, 1873313359),
                u = c(u, n, o, a, e[h + 15], 10, -30611744),
                a = c(a, u, n, o, e[h + 6], 15, -1560198380),
                o = c(o, a, u, n, e[h + 13], 21, 1309151649),
                n = c(n, o, a, u, e[h + 4], 6, -145523070),
                u = c(u, n, o, a, e[h + 11], 10, -1120210379),
                a = c(a, u, n, o, e[h + 2], 15, 718787259),
                o = c(o, a, u, n, e[h + 9], 21, -343485551),
                n = r(n, d), o = r(o, p), a = r(a, m), u = r(u, f);
        return [n, o, a, u]
    },

    hexdigest = function (e) {
        return function (e) {
            for (var t = "0123456789abcdef", n = "", r = 0; r < 4 * e.length; r++) n += t.charAt(e[r >> 2] >> r % 4 * 8 + 4 & 15) + t.charAt(e[r >> 2] >> r % 4 * 8 & 15);
            return n
        }(u(o(e), 8 * e.length))
    },
    hash = function (e) {
        return function (e) {
            for (var t = "", n = 0; n < 32 * e.length; n += 8) t += String.fromCharCode(e[n >> 5] >>> n % 32 & 255);
            return t
        }(u(o(e), 8 * e.length))

    };

module.exports = {
    hexdigest, hash
}