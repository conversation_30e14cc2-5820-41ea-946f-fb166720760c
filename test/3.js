

webpackJsonp([7], {
    494: function (e, t, o) {
        "use strict";
        Object.defineProperty(t, "__esModule", {value: !0}), t.openLink = t.getClient = t.isEMMobileClient = t.isEMPCClient = t.isEMClient = t.getQueryParams = t.getSearchParams = void 0;
        var n = o(5), i = function (e) {
            return e && e.__esModule ? e : {default: e}
        }(n), a = o(853), r = t.getSearchParams = function () {
            var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "", t = {};
            if (e) for (var o = e.slice(e.indexOf("?") + 1).split("&"), n = 0, i = o.length; n < i; n++) {
                var a = o[n].split("=");
                t[a[0]] = a[1]
            }
            return t
        };
        t.getQueryParams = function () {
            var e = (0, i.default)({}, r(window.location.search), r(window.location.hash));
            if (e.appid && e.service) {
                var t = window.decodeURIComponent(window.decodeURIComponent(e.service)), o = t.length,
                    n = t.indexOf("index.html?"), a = t.indexOf("#");
                if (-1 != n) {
                    var l = t.substring(n, -1 != a ? a : o);
                    e = (0, i.default)({}, e, r(l))
                }
            }
            return e
        }, t.isEMClient = function () {
            var e = window.navigator.userAgent;
            return e && e.indexOf("E-Mobile") > -1
        }, t.isEMPCClient = function () {
            var e = window.navigator.userAgent;
            return e && e.indexOf("E-Mobile") > -1 && e.indexOf("Electron") > -1
        }, t.isEMMobileClient = function () {
            var e = window.navigator.userAgent;
            return e && e.indexOf("E-Mobile") > -1 && -1 == e.indexOf("Electron")
        }, t.getClient = function () {
            var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, t = e.location,
                o = void 0 === t ? {} : t, n = o.search, i = void 0 === n ? "" : n, a = r(i);
            if (a.client) return a.client;
            var l = window.navigator.userAgent, s = l && l.indexOf("E-Mobile") > -1,
                u = s && l.indexOf("Electron") > -1;
            return s && !u ? "mobile" : "pc"
        }, t.openLink = function (e) {
            console.log(e), window.em && window.em.checkJsApi("openLink") ? window.em.openLink({
                url: (0, a.addContentPath)(e),
                openType: 2
            }) : window.openLink && "function" == typeof window.openLink ? window.openLink((0, a.addContentPath)(e)) : window.open((0, a.addContentPath)(e))
        }
    }, 527: function (e, t, o) {
        "use strict";
        Object.defineProperty(t, "__esModule", {value: !0});
        t.addContentPath = function (e) {
            var t = window.ecologyContentPath;
            return e && t && e.startsWith("/") && !e.startsWith(t) && (e = t + e), e
        }
    }, 782: function (e, t, o) {
        "use strict";

        function n(e) {
            return e && e.__esModule ? e : {default: e}
        }

        var i = o(8), a = n(i), r = o(1), l = n(r), s = o(4), u = n(s), d = o(2), c = n(d), g = o(3), p = n(g),
            f = o(0), m = n(f), h = o(17), y = n(h), v = o(10), b = o(13), w = o(811);
        o(812), o(844);
        var C = o(848), k = n(C), L = {
            Login: (0, w.asyncComponent)({
                resolve: function () {
                    return o.e(4).then(o.bind(null, 1314))
                }
            }), Setting: (0, w.asyncComponent)({
                resolve: function () {
                    return o.e(2).then(o.bind(null, 1315))
                }
            }), Preview: (0, w.asyncComponent)({
                resolve: function () {
                    return o.e(3).then(o.bind(null, 1316))
                }
            })
        }, S = function (e) {
            function t() {
                return (0, l.default)(this, t), (0, c.default)(this, (t.__proto__ || (0, a.default)(t)).apply(this, arguments))
            }

            return (0, p.default)(t, e), (0, u.default)(t, [{
                key: "render", value: function () {
                    return m.default.createElement(v.Provider, k.default, m.default.createElement(b.HashRouter, null, m.default.createElement(b.Route, {
                        name: "main",
                        path: "/",
                        render: function (e) {
                            return m.default.createElement(b.Switch, null, m.default.createElement(b.Route, {
                                name: "login",
                                path: "/login",
                                component: L.Login
                            }), m.default.createElement(b.Route, {
                                name: "setting",
                                path: "/setting",
                                component: L.Setting
                            }), m.default.createElement(b.Route, {
                                name: "preview",
                                path: "/preview",
                                component: L.Preview
                            }), m.default.createElement(b.Redirect, {
                                to: {
                                    pathname: "/login",
                                    search: e.location.search
                                }
                            }))
                        }
                    })))
                }
            }]), t
        }(m.default.Component);
        window.isLocalApp && "EM7" === window.localAppUa ? window.getLocalInfo().then(function () {
            return y.default.render(m.default.createElement(S, null), document.getElementById("root"))
        }) : y.default.render(m.default.createElement(S, null), document.getElementById("root"))
    }, 811: function (e, t, o) {
        "use strict";

        function n(e) {
            return e && "object" === typeof e && "default" in e ? e.default : e
        }

        function i(e) {
            var t, o, n = e.name, i = e.resolve, a = e.autoResolveES2015Default, c = void 0 === a || a,
                m = e.serverMode, h = void 0 === m ? "resolve" : m, v = e.LoadingComponent, b = e.ErrorComponent;
            if (-1 === y.indexOf(h)) throw new Error("Invalid serverMode provided to asyncComponent");
            var w = ["node", "browser"].indexOf(e.env) > -1 ? e.env : "undefined" === typeof window ? "node" : "browser",
                C = {
                    id: null,
                    module: null,
                    error: null,
                    resolver: null,
                    resolving: !1,
                    asyncComponents: null,
                    asyncComponentsAncestor: null
                }, k = function () {
                    return null == C.module && null == C.error && !C.resolving && "undefined" !== typeof window
                }, L = function (e) {
                    return c && null != e && ("function" === typeof e || "object" === ("undefined" === typeof e ? "undefined" : s(e))) && e.default ? e.default : e
                }, S = function () {
                    if (null == C.resolver) {
                        C.resolving = !0;
                        try {
                            C.resolver = Promise.resolve(i())
                        } catch (e) {
                            C.resolver = Promise.reject(e)
                        }
                    }
                    return C.resolver
                };
            return o = t = function (e) {
                function t() {
                    return u(this, t), f(this, (t.__proto__ || Object.getPrototypeOf(t)).apply(this, arguments))
                }

                return p(t, e), d(t, [{
                    key: "getChildContext", value: function () {
                        return {asyncComponentsAncestor: null == C.asyncComponents ? null : {isBoundary: "boundary" === h}}
                    }
                }, {
                    key: "componentWillMount", value: function () {
                        null != this.context.asyncComponents && (C.asyncComponents = this.context.asyncComponents, C.asyncComponentsAncestor = this.context.asyncComponentsAncestor, C.id || (C.id = this.context.asyncComponents.getNextId()))
                    }
                }, {
                    key: "bootstrap", value: function () {
                        var e = this, t = function () {
                            return e.resolveModule().then(function (e) {
                                return void 0 !== e && void 0
                            })
                        };
                        if ("browser" === w) {
                            var o = C.asyncComponents, n = o.shouldRehydrate, i = o.getError, a = i(C.id);
                            return a ? (C.error = a, !1) : !!n(C.id) && t()
                        }
                        var r = null != C.asyncComponentsAncestor && C.asyncComponentsAncestor.isBoundary;
                        return "defer" !== h && !r && t()
                    }
                }, {
                    key: "componentDidMount", value: function () {
                        k() && this.resolveModule()
                    }
                }, {
                    key: "resolveModule", value: function () {
                        var e = this;
                        return S().then(function (e) {
                            return null != C.asyncComponents && C.asyncComponents.resolved(C.id), C.module = e, C.error = null, C.resolving = !1, e
                        }).catch(function (e) {
                            var t = e.message, o = e.stack, n = {message: t, stack: o};
                            null != C.asyncComponents && C.asyncComponents.failed(C.id, n), C.error = n, C.resolving = !1, b || console.error(n)
                        }).then(function (t) {
                            if (!e.unmounted) return e.context.reactAsyncBootstrapperRunning || "browser" !== w || e.forceUpdate(), t
                        })
                    }
                }, {
                    key: "componentWillUnmount", value: function () {
                        this.unmounted = !0
                    }
                }, {
                    key: "render", value: function () {
                        var e = C.module, t = C.error;
                        if (t) return b ? r.createElement(b, g({}, this.props, {error: t})) : null;
                        var o = L(e);
                        return o ? r.createElement(o, this.props) : v ? r.createElement(v, this.props) : null
                    }
                }]), t
            }(r.Component), t.displayName = n || "AsyncComponent", t.contextTypes = {
                asyncComponentsAncestor: l.shape({isBoundary: l.bool}),
                asyncComponents: l.shape({
                    getNextId: l.func.isRequired,
                    resolved: l.func.isRequired,
                    shouldRehydrate: l.func.isRequired
                })
            }, t.childContextTypes = {asyncComponentsAncestor: l.shape({isBoundary: l.bool})}, o
        }

        Object.defineProperty(t, "__esModule", {value: !0});
        var a = o(0), r = n(a), l = n(o(15)),
            s = "function" === typeof Symbol && "symbol" === typeof Symbol.iterator ? function (e) {
                return typeof e
            } : function (e) {
                return e && "function" === typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
            }, u = function (e, t) {
                if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
            }, d = function () {
                function e(e, t) {
                    for (var o = 0; o < t.length; o++) {
                        var n = t[o];
                        n.enumerable = n.enumerable || !1, n.configurable = !0, "value" in n && (n.writable = !0), Object.defineProperty(e, n.key, n)
                    }
                }

                return function (t, o, n) {
                    return o && e(t.prototype, o), n && e(t, n), t
                }
            }(), c = function (e, t, o) {
                return t in e ? Object.defineProperty(e, t, {
                    value: o,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : e[t] = o, e
            }, g = Object.assign || function (e) {
                for (var t = 1; t < arguments.length; t++) {
                    var o = arguments[t];
                    for (var n in o) Object.prototype.hasOwnProperty.call(o, n) && (e[n] = o[n])
                }
                return e
            }, p = function (e, t) {
                if ("function" !== typeof t && null !== t) throw new TypeError("Super expression must either be null or a function, not " + typeof t);
                e.prototype = Object.create(t && t.prototype, {
                    constructor: {
                        value: e,
                        enumerable: !1,
                        writable: !0,
                        configurable: !0
                    }
                }), t && (Object.setPrototypeOf ? Object.setPrototypeOf(e, t) : e.__proto__ = t)
            }, f = function (e, t) {
                if (!e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                return !t || "object" !== typeof t && "function" !== typeof t ? e : t
            }, m = function () {
                var e = 0, t = {}, o = {};
                return {
                    getNextId: function () {
                        return e += 1
                    }, resolved: function (e) {
                        t[e] = !0
                    }, failed: function (e, t) {
                        o[e] = t
                    }, getState: function () {
                        return {
                            resolved: Object.keys(t).reduce(function (e, t) {
                                return Object.assign(e, c({}, t, !0))
                            }, {}), errors: o
                        }
                    }
                }
            }, h = function (e) {
                function t() {
                    return u(this, t), f(this, (t.__proto__ || Object.getPrototypeOf(t)).apply(this, arguments))
                }

                return p(t, e), d(t, [{
                    key: "componentWillMount", value: function () {
                        this.asyncContext = this.props.asyncContext || m(), this.rehydrateState = this.props.rehydrateState
                    }
                }, {
                    key: "getChildContext", value: function () {
                        var e = this;
                        return {
                            asyncComponents: {
                                getNextId: this.asyncContext.getNextId,
                                resolved: this.asyncContext.resolved,
                                failed: this.asyncContext.failed,
                                shouldRehydrate: function (t) {
                                    var o = e.rehydrateState.resolved[t];
                                    return delete e.rehydrateState.resolved[t], o
                                },
                                getError: function (t) {
                                    return e.rehydrateState.errors && e.rehydrateState.errors[t]
                                }
                            }
                        }
                    }
                }, {
                    key: "render", value: function () {
                        return r.Children.only(this.props.children)
                    }
                }]), t
            }(a.Component);
        h.propTypes = {
            children: l.node.isRequired,
            asyncContext: l.shape({
                getNextId: l.func.isRequired,
                resolved: l.func.isRequired,
                failed: l.func.isRequired,
                getState: l.func.isRequired
            }),
            rehydrateState: l.shape({resolved: l.object})
        }, h.defaultProps = {
            asyncContext: void 0,
            rehydrateState: {resolved: {}}
        }, h.childContextTypes = {
            asyncComponents: l.shape({
                getNextId: l.func.isRequired,
                resolved: l.func.isRequired,
                failed: l.func.isRequired,
                shouldRehydrate: l.func.isRequired,
                getError: l.func.isRequired
            }).isRequired
        };
        var y = ["resolve", "defer", "boundary"];
        t.AsyncComponentProvider = h, t.createAsyncContext = m, t.asyncComponent = i
    }, 812: function (e, t, o) {
        "use strict";

        function n(e) {
            return e && e.__esModule ? e : {default: e}
        }

        var i = o(28), a = n(i), r = o(9), l = n(r), s = o(46), u = n(s), d = o(0), c = n(d), g = o(88), p = n(g),
            f = o(12), m = function (e) {
                if (e && e.__esModule) return e;
                var t = {};
                if (null != e) for (var o in e) Object.prototype.hasOwnProperty.call(e, o) && (t[o] = e[o]);
                return t.default = e, t
            }(f), h = o(836), y = o(837), v = u.default.alert, b = l.default.getLabel, w = window.jQuery, C = m.toJS, k = 0,
            L = function (e) {
                var t = e.langid, o = e.callback, n = e.queryParams;
                window.currLanguageId = t;
                var i = e.loginid || "";
                "" == i && (i = w("#loginid").val());
                var r = [], s = [], u = {}, d = {}, c = !1;
                r = [{
                    label: "\u8d26\u53f7",
                    atype: "account",
                    id: "loginid",
                    name: "loginid",
                    type: "text"
                }, {
                    label: "\u5bc6\u7801",
                    atype: "password",
                    id: "userpassword",
                    name: "userpassword",
                    type: "password"
                }], n = n && C(n), n && n.appid && n.service && n.message && 0 == k && (k += 1, l.default.callApi({
                    url: "/api/integration/common1/getMessageByCode",
                    method: "POST",
                    params: {code: n.message, language: t}
                }).then(function (e) {
                    a.default.error(e.message, 3)
                }).catch(function (e) {
                    console && console.log(e)
                })), l.default.callApi({
                    url: "/api/hrm/login/getLoginForm",
                    method: "POST",
                    params: {loginid: i, langid: t}
                }).then(function (e) {
                    r = e.formItems, u = e.loginSetting, d = e.qrcode, c = u.hasMultiLang, s = [{html: '<input type="hidden" id="messages" name="messages" value="">'}], o && "function" == typeof o && o({
                        hasMultiLang: c,
                        loginFormItems: r,
                        loginFormHiddenItems: s,
                        loginSetting: u,
                        qrCode: d,
                        message: e.message
                    })
                }).catch(function (e) {
                    a.default.error(e), o && "function" == typeof o && o({
                        hasMultiLang: c,
                        loginFormItems: r,
                        loginFormHiddenItems: s,
                        loginSetting: u,
                        qrCode: d,
                        message: result.message
                    })
                })
            }, S = function (e) {
                var t = e.loginSetting.openRSA;
                t && "1" == t ? window.__RSAEcrypt__.refreshRsaCode(function () {
                    P(e)
                }) : P(e)
            }, P = function (e) {
                for (var t = e.loginSetting, o = t.userUsbType, n = (t.hasMultiLang, t.hasValidateCode), i = t.validateCodeKey, r = (t.numValidateWrong, t.validateType, t.showDynamicPwd, t.openRSA), s = e.langid, u = e.success, d = e.failure, g = e.logintype, p = e.languageIdentify, f = e.queryParams, m = "", k = 0; k < p.length; k++) p[k][s] && "" != p[k][s] && (m = p[k][s]);
                var L = w("#loginid").val(), S = w("#userpassword").val(), P = w("#dynamicPassword").val(), x = L, _ = S;
                r && "1" == r && (_ = window.__RSAEcrypt__.rsa_data_encrypt(S), x = window.__RSAEcrypt__.rsa_data_encrypt(L));
                var A = w("#tokenAuthKey") ? w("#tokenAuthKey").val() : "",
                    T = w("#validatecode") ? w("#validatecode").val() : "", M = w("#messages") ? w("#messages").val() : "";
                if (a.default.hide(), w("#userpassword") && w("#userpassword").length > 0 && ("" == L || "" == S)) return a.default.error(b("386478", "\u8bf7\u8f93\u5165\u7528\u6237\u540d\u3001\u5bc6\u7801"), 3), void (d && "function" == typeof d && d());
                if (w("#dynamicPassword") && w("#dynamicPassword").length > 0 && "" == P) return a.default.error(b("20289", "\u8bf7\u8f93\u5165\u52a8\u6001\u5bc6\u7801"), 3), void (d && "function" == typeof d && d());
                if (n && "1" == n && "" == T) return a.default.error(b("84270", "\u8bf7\u8f93\u5165\u9a8c\u8bc1\u7801"), 3), void (d && "function" == typeof d && d());
                if ("3" == o) {
                    if ("" == A) return a.default.warning(b("84271", "\u8bf7\u8f93\u5165\u52a8\u6001\u4ee4\u724c\u53e3\u4ee4")), void (d && "function" == typeof d && d());
                    if (!O(A) || 6 != A.length) return a.default.warning(b("386435", "\u52a8\u6001\u4ee4\u724c\u53e3\u4ee4\u5fc5\u987b\u4e3a6\u4f4d\u6570\u5b57\uff01")), void (d && "function" == typeof d && d())
                }
                f = C(f) || {};
                var E = f, z = E.appid, F = E.service;
                l.default.callApi({
                    url: "/api/hrm/login/checkLogin",
                    method: "POST",
                    params: {
                        islanguid: s,
                        loginid: x,
                        userpassword: _,
                        dynamicPassword: P,
                        tokenAuthKey: A,
                        validatecode: T,
                        validateCodeKey: i,
                        logintype: g,
                        messages: M,
                        appid: z,
                        service: F
                    }
                }).then(function (e) {
                    if ("access_token" in e && "string" === typeof e.access_token && "" !== e.access_token ? localStorage.setItem("access_token", e.access_token) : localStorage.access_token && localStorage.removeItem("access_token"), e.user_token && "" != e.user_token && (S = e.user_token), "true" == e.loginstatus) {
                        var t = function (e) {
                            if ("1" == e.isRemoveSession) return void u(L, S, e);
                            w("#userpassword").val();
                            u && "function" == typeof u && (a.default.hide(), 2 == g ? (0, h.checkWeakPass)(_, i, r, p) : l.default.callApi({
                                url: "/api/hrm/login/remindLogin",
                                method: "POST",
                                params: {logintype: g}
                            }).then(function (e) {
                                if ("1" == e.status) if ("true" == e.isUpPswd) d && "function" == typeof d && d({reflashValidateCode: !0}), v(b(15172, "\u7cfb\u7edf\u63d0\u793a"), b("81626", "\u9996\u6b21\u767b\u5f55\u9700\u4fee\u6539\u5bc6\u7801\uff01"), [{
                                    text: b(826, "\u786e\u5b9a"),
                                    onPress: function () {
                                        d && "function" == typeof d && d({reflashValidateCode: !0}), R("/spa/hrm/static4mobile/index.html#/changePassword?optype=2", !0)
                                    }
                                }, {
                                    text: b(201, "\u53d6\u6d88"), onPress: function () {
                                        d && "function" == typeof d && d({reflashValidateCode: !0}), l.default.callApi("/api/hrm/login/checkLogout", "POST", {}).then(function (e) {
                                        })
                                    }
                                }]); else if ("1" == e.passwdReminder) if ("1" == e.canpass) if ("1" == e.canremind) {
                                    var t = b("386479", "\u60a8\u7684\u5bc6\u7801\u8fd8\u6709{params}\u5929\u8fc7\u671f,\u662f\u5426\u73b0\u5728\u4fee\u6539?").replace("{params}", e.passwdelse);
                                    v(b(15172, "\u7cfb\u7edf\u63d0\u793a"), t, [{
                                        text: b(826, "\u786e\u5b9a"),
                                        onPress: function () {
                                            d && "function" == typeof d && d({reflashValidateCode: !0}), R("/spa/hrm/static4mobile/index.html#/changePassword", !0)
                                        }
                                    }, {
                                        text: b(201, "\u53d6\u6d88"), onPress: function () {
                                            (0, h.checkWeakPass)(_, i, r, p)
                                        }
                                    }])
                                } else (0, h.checkWeakPass)(_, i, r, p); else v(b(15172, "\u7cfb\u7edf\u63d0\u793a"), b("386480", "\u60a8\u7684\u5bc6\u7801\u5df2\u7ecf\u8fc7\u671f,\u8bf7\u7acb\u5373\u4fee\u6539\uff01"), [{
                                    text: b(826, "\u786e\u5b9a"),
                                    onPress: function () {
                                        d && "function" == typeof d && d({reflashValidateCode: !0}), R("/spa/hrm/static4mobile/index.html#/changePassword?optype=1", !0)
                                    }
                                }, {
                                    text: b(201, "\u53d6\u6d88"), onPress: function () {
                                        d && "function" == typeof d && d({reflashValidateCode: !0}), l.default.callApi("/api/hrm/login/checkLogout", "POST", {}).then(function (e) {
                                        })
                                    }
                                }]); else (0, h.checkWeakPass)(_, i, r, p)
                            }))
                        };
                        if ("-100" == e.qysflag && v(b(15172, "\u7cfb\u7edf\u63d0\u793a"), e.qysmg), e.authUrl && 0 != e.authUrl.length) {
                            q() && "face" == e.qysAuthType ? function (e) {
                                $("#qyscheckview").remove();
                                var t = document.createElement("div");
                                t.id = "qyscheckview", t.style.position = "fixed", t.style.width = "100%", t.style.height = "100%", t.style.top = 0, t.style.left = 0, t.style.zIndex = 9999, t.innerHTML = "<div style='width: 100%;height:30px;background: #2489f2;line-height: 30px;text-align: center;color:#fff;'>" + b("534593", "\u8eab\u4efd\u6821\u9a8c") + "</div><div style='width: 100%;height: calc(100% - 60px)'><iframe style='border:none;height:100%;width:100%' src='" + e + "'></iframe></div><div id='quitqyscheck' style='width: 100%;height:30px;background: #2489f2;line-height: 30px;text-align: center;cursor: pointer;color: #fff;'>" + b(32694, "\u53d6\u6d88") + "</div>", document.body.appendChild(t), $("#quitqyscheck").click(function () {
                                    $("#qyscheckview").remove(), d({reflashValidateCode: !0}), clearInterval(n)
                                })
                            }((0, y.addContentPath)("/spa/hrm/static4mobile/index.html#/qysLogin?langid=" + s + "&authUrl=" + e.authUrl)) : (window.em && window.em.checkJsApi("pageVisible") && window.em.pageVisible(function (e) {
                                e && d({reflashValidateCode: !0})
                            }), window.em && window.em.checkJsApi("openLink") && window.em.openLink({
                                url: e.authUrl,
                                openType: 2
                            }));
                            var o = {authId: e.authId, langid: s}, n = setInterval(function () {
                                l.default.callApi({
                                    url: "/api/hrm/login/checkqysLogin",
                                    method: "POST",
                                    params: o
                                }).then(function (o) {
                                    if ("1" == o.code) clearInterval(n), $("#qyscheckview").remove(), t(e); else if ("-1" == o.code) return void clearInterval(n)
                                })
                            }, 2e3)
                        } else t(e);
                        var i = function () {
                            R("/spa/hrm/static4mobile/index.html#/changePassword", !0)
                        }, r = function () {
                            d && "function" == typeof d && d({reflashValidateCode: !0})
                        }, p = function () {
                            $("#content_detail").remove(), $("#content_detail_bg").remove(), d({reflashValidateCode: !0}), clearInterval(n), I({
                                success: u,
                                loginid: L,
                                userpassword: S,
                                langid4em: m,
                                result: e
                            })
                        }
                    } else {
                        if (d && "function" == typeof d && d({reflashValidateCode: !0}), "66" == e.msgcode) return;
                        a.default.hide();
                        var f = e.msg || b("386481", "\u767b\u5f55\u5931\u8d25"), C = "";
                        if ("120" == e.msgcode) C = "," + b("507855", "\u8bf7\u53bbPC\u7aef") + b("388043", "\u7ed1\u5b9a\u4ee4\u724c"); else if ("122" == e.msgcode) C = "," + b("507855", "\u8bf7\u53bbPC\u7aef") + b("84269", "\u540c\u6b65\u4ee4\u724c"); else if ("19" == e.msgcode) return void (C = "," + b("507855", "\u8bf7\u53bbPC\u7aef") + b("18640", "\u63d0\u4ea4License"));
                        a.default.error(c.default.createElement("span", null, f + C), 5)
                    }
                })
            }, x = function (e) {
                var t = (e.langid, e.success, e.failure), o = e.loginSetting.openRSA, n = w("#loginid").val(),
                    i = w("#userpassword").val();
                w("#dynamicPassword").val();
                if (a.default.hide(), w("#userpassword") && w("#userpassword").length > 0 && ("" == n || "" == i)) return a.default.error(b("386478", "\u8bf7\u8f93\u5165\u7528\u6237\u540d\u3001\u5bc6\u7801"), 3), void (t && "function" == typeof t && t());
                o && "1" == o ? window.__RSAEcrypt__.refreshRsaCode(function () {
                    _(e)
                }) : _(e)
            }, _ = function (e) {
                var t = e.langid, o = e.success, n = (e.failure, e.loginSetting.openRSA), i = w("#loginid").val(),
                    r = w("#userpassword").val(), s = i, u = r;
                n && "1" == n && (s = window.__RSAEcrypt__.rsa_data_encrypt(i), u = window.__RSAEcrypt__.rsa_data_encrypt(r)), l.default.callApi({
                    url: "/api/hrm/login/getDynamicPassword",
                    method: "POST",
                    params: {langid: t, loginid: s, userpassword: u}
                }).then(function (e) {
                    if ("1" == e.status) o && "function" == typeof o && o(e); else {
                        a.default.hide();
                        var t = e.message;
                        a.default.error(c.default.createElement("span", null, t), 5)
                    }
                })
            }, I = function (e) {
                var t = e.success, o = e.failure, n = e.loginid, i = e.userpassword, r = e.langid4em, s = e.result;
                window.em && window.em.checkJsApi("ssoLogin") ? l.default.callApi({
                    url: "/api/ec/dev/app/getSSOCode",
                    method: "get",
                    params: {}
                }).then(function (e) {
                    window.em.ssoLogin({
                        ssokey: e.keycode, lang_type: r, account: "", into: 0, success: function (e) {
                            e.errCode && 0 != e.errCode && a.default.error(e.errMsg, 5), t(n, i, s)
                        }, fail: function (t) {
                            a.default.error("ssoRes.keycode:" + e.keycode + "emLogin error: " + t.errMsg, 5), o && "function" == typeof o && o({reflashValidateCode: !0})
                        }, complete: function (e) {
                        }
                    })
                }) : t(n, i, s)
            }, R = function (e, t) {
                if (t) return void A(e);
                if (window.em && window.em.checkJsApi("openLink")) {
                    var o = window.localStorage.emobile_ec_id;
                    o = "", window.em.openLink({sysId: o, url: (0, y.addContentPath)(e), openType: 2})
                } else window.open(e)
            }, q = function () {
                var e = window.navigator.userAgent;
                return !!(e && e.indexOf("Electron") > -1 || e && e.indexOf("nw/0.14.7") > -1)
            }, A = function (e) {
                w("#first_password_iframe").remove();
                var t = document.createElement("div");
                t.style.position = "fixed", t.style.width = "100%", t.style.height = "100%", t.style.top = 0, t.style.left = 0, t.style.zIndex = 998, t.innerHTML = '<iframe style="border:none;height:100%;width:100%" src="' + (0, y.addContentPath)(e) + '"></iframe>', document.body.appendChild(t)
            }, O = function (e) {
                var t;
                return t = /\d*/i, e.match(t) == e
            };
        window.doLoginAction = function (e, t) {
            var o = t.langid;
            "getLoginForm" == e ? l.default.getLocaleLabelByLang("common,hrm", o).then(function () {
                L(t)
            }) : "login" == e ? p.default.isDefined("rsa") ? p.default.ready("rsa", function () {
                S(t)
            }) : (0, p.default)(["/js/rsa/jsencrypt.js", "/js/rsa/rsa.js"], "rsa", {
                async: !1, success: function () {
                    S(t)
                }
            }) : "getDynamicPassword" == e ? p.default.isDefined("rsa") ? p.default.ready("rsa", function () {
                x(t)
            }) : (0, p.default)(["/js/rsa/jsencrypt.js", "/js/rsa/rsa.js"], "rsa", {
                async: !1, success: function () {
                    x(t)
                }
            }) : "forgetPassword" == e && R("/spa/hrm/static4mobile/index.html#/forgetPassword?languageId=" + o)
        }
    }, 836: function (e, t, o) {
        "use strict";

        function n(e) {
            return e && e.__esModule ? e : {default: e}
        }

        Object.defineProperty(t, "__esModule", {value: !0}), t.checkWeakPass = void 0;
        var i = o(46), a = n(i), r = o(8), l = n(r), s = o(1), u = n(s), d = o(4), c = n(d), g = o(2), p = n(g),
            f = o(3), m = n(f), h = o(9), y = n(h), v = o(0), b = n(v), w = o(17), C = n(w), k = y.default.getLabel,
            L = function () {
                var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                return y.default.callApi({url: "/api/hrm/password/isWeakPassword", method: "POST", params: e})
            }, S = (t.checkWeakPass = function (e, t, o, n) {
                L({password: e}).then(function (e) {
                    var i = e.weakPasswordDisable, a = e.isWeakPassword;
                    e.defaultPasswordEnable;
                    if ("1" === i && a) {
                        var r = document.createElement("div");
                        document.body.appendChild(r), C.default.render(b.default.createElement(S, {
                            weakPasswordDisable: i,
                            onOk: t,
                            onCancel: o
                        }), r)
                    } else n()
                }).catch(function () {
                    n()
                })
            }, function (e) {
                function t(e) {
                    (0, u.default)(this, t);
                    var o = (0, p.default)(this, (t.__proto__ || (0, l.default)(t)).call(this, e));
                    return o.handleOk = function () {
                        var e = o.props.onOk;
                        o.setState({visible: !1}), e && e()
                    }, o.handleCancel = function () {
                        var e = o.props.onCancel;
                        o.setState({visible: !1}), e && e()
                    }, o.state = {visible: !1}, o
                }

                return (0, m.default)(t, e), (0, c.default)(t, [{
                    key: "componentDidMount", value: function () {
                        this.setState({visible: !0})
                    }
                }, {
                    key: "render", value: function () {
                        var e = this.state.visible, t = this.props.weakPasswordDisable;
                        if (!e) return null;
                        var o = [{text: k(33703, "\u786e\u5b9a"), onPress: this.handleOk}];
                        return "0" === t && o.push({
                            text: k(32694, "\u53d6\u6d88"),
                            onPress: this.handleCancel
                        }), b.default.createElement(a.default, {
                            closable: !0,
                            transparent: !0,
                            visible: e,
                            onClose: this.handleCancel,
                            title: k(131329, "\u4fe1\u606f\u786e\u8ba4"),
                            footer: o
                        }, b.default.createElement("p", null, k("516219", "\u8be5\u5bc6\u7801\u5b89\u5168\u6027\u8f83\u4f4e\uff0c\u7ba1\u7406\u5458\u5df2\u5f00\u542f\u5f31\u5bc6\u7801\u7981\u6b62\u4fdd\u5b58\u8bbe\u7f6e\uff0c\u8bf7\u4fee\u6539\u5bc6\u7801\u3002")))
                    }
                }]), t
            }(v.Component))
    }, 837: function (e, t, o) {
        "use strict";

        function n(e) {
            return e && e.__esModule ? e : {default: e}
        }

        Object.defineProperty(t, "__esModule", {value: !0}), t.addContentPath = t.formatUrl = t.configNavigation = void 0;
        var i = o(27), a = n(i), r = o(840), l = n(r);
        window.viewSubCompany = function (e) {
            window.em
        }, window.viewDepartment = function (e) {
            window.em
        };
        var s = function (e) {
            var t = window.em;
            if (t && t.checkJsApi("configNavigation")) {
                var o = "string" == typeof e ? {title: e} : e;
                t.configNavigation(o)
            }
        }, u = function (e) {
            return e = (0, l.default)(e).reduce(function (t, o, n) {
                return "" + t + (0, a.default)(e)[n] + "=" + o + "&"
            }, "?"), e.substring(0, e.length - 1)
        }, d = function (e) {
            var t = window.ecologyContentPath || "";
            return e && t && e.startsWith("/") && !e.startsWith(t) && (e = t + e), e
        };
        t.configNavigation = s, t.formatUrl = u, t.addContentPath = d
    }, 840: function (e, t, o) {
        e.exports = {default: o(841), __esModule: !0}
    }, 841: function (e, t, o) {
        o(842), e.exports = o(34).Object.values
    }, 842: function (e, t, o) {
        var n = o(57), i = o(843)(!1);
        n(n.S, "Object", {
            values: function (e) {
                return i(e)
            }
        })
    }, 843: function (e, t, o) {
        var n = o(76), i = o(127), a = o(95), r = o(149).f;
        e.exports = function (e) {
            return function (t) {
                for (var o, l = a(t), s = i(l), u = s.length, d = 0, c = []; u > d;) o = s[d++], n && !r.call(l, o) || c.push(e ? [o, l[o]] : l[o]);
                return c
            }
        }
    }, 844: function (e, t, o) {
        "use strict";
        o(845), o(846), o(847)
    }, 845: function (e, t) {
    }, 846: function (e, t) {
    }, 847: function (e, t) {
    }, 848: function (e, t, o) {
        "use strict";
        Object.defineProperty(t, "__esModule", {value: !0});
        var n = o(849), i = function (e) {
            return e && e.__esModule ? e : {default: e}
        }(n), a = new i.default;
        t.default = {portalLoginStore: a}
    }, 849: function (e, t, o) {
        "use strict";

        function n(e) {
            return e && e.__esModule ? e : {default: e}
        }

        function i(e, t, o, n) {
            o && (0, O.default)(e, t, {
                enumerable: o.enumerable,
                configurable: o.configurable,
                writable: o.writable,
                value: o.initializer ? o.initializer.call(n) : void 0
            })
        }

        function a(e, t, o, n, i) {
            var a = {};
            return Object.keys(n).forEach(function (e) {
                a[e] = n[e]
            }), a.enumerable = !!a.enumerable, a.configurable = !!a.configurable, ("value" in a || a.initializer) && (a.writable = !0), a = o.slice().reverse().reduce(function (o, n) {
                return n(e, t, o) || o
            }, a), i && void 0 !== a.initializer && (a.value = a.initializer ? a.initializer.call(i) : void 0, a.initializer = void 0), void 0 === a.initializer && (Object.defineProperty(e, t, a), a = null), a
        }

        Object.defineProperty(t, "__esModule", {value: !0}), t.default = void 0;
        var r, l, s, u, d, c, g, p, f, m, h, y, v, b, w, C, k, L, S, P, x, _, I, R, q, A = o(21), O = n(A), T = o(47),
            M = n(T), E = o(77), z = n(E), F = o(28), j = n(F), D = o(78), V = n(D), J = o(1), U = n(J), Q = o(4),
            N = n(Q), B = o(9), W = n(B), H = o(12), G = o(494), $ = o(527), K = W.default.getLabel,
            X = (window.jQuery, r = function () {
                function e() {
                    (0, U.default)(this, e), i(this, "loading", l, this), i(this, "queryParams", s, this), i(this, "logining", u, this), i(this, "language", d, this), i(this, "languages", c, this), i(this, "languageIdentify", g, this), i(this, "languagesVisible", p, this), i(this, "data", f, this), i(this, "labels", m, this), i(this, "autologin", h, this), i(this, "remember", y, this), i(this, "remembered", v, this), i(this, "loginForm", b, this), i(this, "dynamicPasswordTime", w, this), i(this, "seriesnum_", C, this), i(this, "validateCode", k, this), i(this, "loginType", L, this), i(this, "qrcodeValidTime", S, this), i(this, "qrcodeReqTime", P, this), i(this, "qrcodeIsInvalid", x, this), i(this, "agreeMentState", _, this), i(this, "oneClickLoading", I, this), i(this, "oneClickLoadingFlag", R, this), i(this, "qrcodeUsedTime", q, this)
                }

                return (0, N.default)(e, [{
                    key: "getLanguages", value: function () {
                        var e = this;
                        W.default.callApi({
                            url: "/api/system/language/base/getActiveLanguage",
                            method: "GET"
                        }).then(function (t) {
                            var o = t.systemDefaultLang, n = void 0 === o ? {} : o, i = t.activeLanguageInfo,
                                a = void 0 === i ? [] : i, r = t.languageIdentify, l = void 0 === r ? [] : r, s = {};
                            for (var u in n) s = {id: u, language: n[u]};
                            e.language = s, e.languages = a, e.languageIdentify = l;
                            var d = s, c = d.id;
                            e.getLabels(c), e.getRemember(), e.getRemembered(c)
                        })
                    }
                }, {
                    key: "getLabels", value: function (e) {
                        var t = this;
                        W.default.callApi({
                            url: "/api/portal/mloginpage/getMLoginPageLabels",
                            method: "GET",
                            params: {language: e}
                        }).then(function (e) {
                            var o = e.data, n = void 0 === o ? {} : o;
                            t.labels = n
                        })
                    }
                }, {
                    key: "onLanguageChange", value: function (e) {
                        this.getLabels(e), this.getLoginForm({langid: e});
                        for (var t = this.languages, o = 0, n = t.length; o < n; o++) {
                            var i = t[o];
                            if (i.id == e) {
                                this.language = i;
                                break
                            }
                        }
                        this.onLanguagesVisibleChange(!1)
                    }
                }, {
                    key: "onLanguagesVisibleChange", value: function (e) {
                        this.languagesVisible = e
                    }
                }, {
                    key: "onAutoLoginChange", value: function (e) {
                        this.autologin = e, e && this.onRememberChange(1)
                    }
                }, {
                    key: "onRememberChange", value: function (e) {
                        this.remember = e, 1 != e && this.onAutoLoginChange(!1)
                    }
                }, {
                    key: "onRemember", value: function (e, t) {
                        var o = this.data || {}, n = o.formconfig, i = void 0 === n ? {} : n, a = i.showRemeber;
                        if (void 0 === a || a) {
                            var r = this.remember, l = {};
                            1 == r ? l = {
                                account: e,
                                password: t
                            } : 2 == r ? l = {account: e} : 3 == r && (l = {}), window.em && window.em.checkJsApi("setItem") ? (window.em.setItem({
                                key: "login-autologin",
                                isPublic: !0,
                                value: (0, V.default)(this.autologin)
                            }), window.em.setItem({
                                key: "login-remember",
                                isPublic: !0,
                                value: (0, V.default)(r)
                            }), window.em.setItem({
                                key: "login-remembered",
                                isPublic: !0,
                                value: (0, V.default)(l)
                            })) : (window.localStorage.setItem("login-autologin", (0, V.default)(this.autologin)), window.localStorage.setItem("login-remember", (0, V.default)(r)), window.localStorage.setItem("login-remembered", (0, V.default)(l)))
                        }
                    }
                }, {
                    key: "onRemoveRemember", value: function () {
                        window.em && window.em.checkJsApi("setItem") ? (window.em.removeItem({
                            key: "login-autologin",
                            isPublic: !0
                        }), window.em.removeItem({
                            key: "login-remember",
                            isPublic: !0
                        }), window.em.removeItem({
                            key: "login-remembered",
                            isPublic: !0
                        })) : (window.localStorage.removeItem("login-autologin"), window.localStorage.removeItem("login-remember"), window.localStorage.removeItem("login-remembered"))
                    }
                }, {
                    key: "getDataByUsing", value: function () {
                        var e = this;
                        this.loading = !0, W.default.callApi({
                            url: "/api/portal/mloginpage/getMLoginPageByUsing",
                            method: "GET"
                        }).then(function (t) {
                            var o = t.data, n = void 0 === o ? {} : o;
                            e.data = n;
                            var i = n.formconfig, a = void 0 === i ? {} : i, r = n.qrcodeconfig,
                                l = void 0 === r ? {} : r, s = n.agreeMentConfig, u = void 0 === s ? {} : s,
                                d = a.showRemeber, c = void 0 === d || d, g = l.showQrcode, p = l.qrcodeValidTime,
                                f = void 0 === p ? 30 : p, m = l.qrcodeReqTime, h = void 0 === m ? 1 : m,
                                y = l.defaultQrcode;
                            c || e.onRemoveRemember(), e.qrcodeValidTime = f, e.qrcodeReqTime = h, g && y && (e.loginType = "qrcode"), e.loading = !1, u && "1" == u.state && e.getAgreeState()
                        })
                    }
                }, {
                    key: "getDataById", value: function (e) {
                        var t = this;
                        this.loading = !0, W.default.callApi({
                            url: "/api/portal/mloginpage/getMLoginPage",
                            method: "GET",
                            params: {id: e}
                        }).then(function (e) {
                            var o = e.data, n = void 0 === o ? {} : o;
                            t.data = n;
                            var i = n.formconfig, a = void 0 === i ? {} : i, r = n.qrcodeconfig,
                                l = void 0 === r ? {} : r, s = n.agreeMentConfig, u = a.showRemeber,
                                d = void 0 === u || u, c = l.showQrcode, g = l.qrcodeValidTime,
                                p = void 0 === g ? 30 : g, f = l.qrcodeReqTime, m = void 0 === f ? 1 : f,
                                h = l.defaultQrcode;
                            d || t.onRemoveRemember(), t.qrcodeValidTime = p, t.qrcodeReqTime = m, c && h && (t.loginType = "qrcode"), s && "1" == s.state && t.getAgreeState(), t.loading = !1
                        })
                    }
                }, {
                    key: "onDataChange", value: function (e) {
                        this.data = e
                    }
                }, {
                    key: "getLoginForm", value: function () {
                        var e = this, t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                            o = arguments[1], n = t.loginid, i = void 0 === n ? "" : n, a = t.langid,
                            r = void 0 === a ? this.language.id : a;
                        window.doLoginAction && window.doLoginAction("getLoginForm", {
                            queryParams: this.queryParams,
                            logintype: "1",
                            loginid: i,
                            langid: r,
                            callback: function (t) {
                                var n = t.loginFormItems, i = void 0 === n ? [] : n, a = t.loginFormHiddenItems,
                                    r = void 0 === a ? [] : a, l = t.loginSetting, s = void 0 === l ? {} : l,
                                    u = t.qrCode, d = void 0 === u ? {} : u;
                                e.loginForm = {
                                    loginFormItems: i,
                                    loginFormHiddenItems: r,
                                    loginSetting: s,
                                    qrCode: d
                                }, o && "function" == typeof o && o(t)
                            }
                        })
                    }
                }, {
                    key: "onLoginTypeChange", value: function (e) {
                        this.loginType = e
                    }
                }, {
                    key: "onValidateCodeChange", value: function () {
                        var e = this;
                        setTimeout(function () {
                            return e.validateCode = "/weaver/weaver.file.MakeValidateCode?seriesnum_=" + ++e.seriesnum_
                        }, 50)
                    }
                }, {
                    key: "getDynamicPassword", value: function () {
                        var e = this, t = this.language.id, o = this.loginForm.loginSetting;
                        window.doLoginAction && window.doLoginAction("getDynamicPassword", {
                            queryParams: this.queryParams,
                            logintype: "1",
                            langid: t,
                            loginSetting: o,
                            success: function (t) {
                                var o = t.validitySec, n = void 0 === o ? 60 : o;
                                e.dynamicPasswordTime = n;
                                var i = setInterval(function () {
                                    var t = e.dynamicPasswordTime;
                                    1 == t && window.clearInterval(i), e.dynamicPasswordTime = t - 1
                                }, 1e3)
                            }
                        })
                    }
                }, {
                    key: "onForgot", value: function () {
                        var e = this.language.id;
                        window.doLoginAction && window.doLoginAction("forgetPassword", {langid: e})
                    }
                }, {
                    key: "onLogin", value: function () {
                        var e = this, t = this.queryParams, o = this.language.id, n = this.loginForm.loginSetting;
                        this.logining = !0, window.doLoginAction && window.doLoginAction("login", {
                            queryParams: t,
                            logintype: "1",
                            langid: o,
                            loginSetting: n,
                            languageIdentify: this.languageIdentify,
                            isRememberAccount: 1 == this.remember || 2 == this.remember,
                            isRememberPassword: 1 == this.remember,
                            success: function (n, i, a) {
                                e.onRemember(n || "", i || ""), e.logining = !1;
                                var r = {isQCLogin: !1, langid: o, languageIdentify: e.languageIdentify};
                                e.onLoginJump(r, t, a)
                            },
                            failure: function () {
                                var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                                e.logining = !1, t.reflashValidateCode && e.onValidateCodeChange(), e.getLoginForm({}, function (t) {
                                    var o = t.loginSetting, n = void 0 === o ? {} : o, i = t.message, a = n.userUsbType;
                                    (0, G.isEMMobileClient)() || "6" != a || (i && j.default.warning(i), e.onLoginTypeChange("qrcode"))
                                })
                            }
                        })
                    }
                }, {
                    key: "onQCLogin", value: function () {
                        var e = this;
                        if (!this.qrcodeIsInvalid) {
                            var t = this.queryParams, o = this.language.id, n = this.loginForm.qrCode,
                                i = void 0 === n ? {} : n, a = new Date;
                            W.default.callApi({
                                url: "/api/hrm/login/qrcode/getQCLoginStatus",
                                method: "POST",
                                params: {langid: o, loginkey: i.loginkey, isie: !1}
                            }).then(function (n) {
                                if ("1" == n.status) {
                                    var i = {isQCLogin: !0, langid: o, languageIdentify: e.languageIdentify};
                                    e.onLoginJump(i, t, n)
                                } else if ("-1" == n.status) n.msg && j.default.warning(n.msg), e.onQCLoginEnd(); else {
                                    var r = new Date, l = 1 == e.qrcodeReqTime ? 2 : e.qrcodeReqTime, s = r - a;
                                    s >= 1e3 * l ? e.onPollingStart() : (e.pollingTimeout && window.clearTimeout(e.pollingTimeout), e.pollingTimeout = setTimeout(function () {
                                        e.onPollingStart()
                                    }, 1e3 * l - s))
                                }
                            })
                        }
                    }
                }, {
                    key: "onLoginJump", value: function () {
                        var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, t = this,
                            o = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                            n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
                        if ("1" == n.casflag && n.casurl) return void (window.location.href = n.casurl);
                        var i = function (e, t) {
                            var o = function (e) {
                                e && e.gopage ? window.location.href = (0, $.addContentPath)(window.decodeURIComponent(e.gopage)) : window.em && window.em.checkJsApi("pushToMain") ? window.em.pushToMain() : window.location.href = (0, $.addContentPath)("/spa/em/mobile.html")
                            };
                            if (e.isQCLogin) if (window.em && window.em.checkJsApi("ssoLogin")) {
                                for (var n = "zh", i = e.langid, a = e.languageIdentify, r = 0, l = a.length; r < l; r++) a[r][i] && (n = a[r][i]);
                                W.default.callApi({
                                    url: "/api/ec/dev/app/getSSOCode",
                                    method: "GET"
                                }).then(function (e) {
                                    window.em.ssoLogin({
                                        ssokey: e.keycode,
                                        lang_type: n,
                                        into: 0,
                                        success: function (e) {
                                            0 == e.errCode ? o(t) : j.default.error(e.errMsg, 5)
                                        }
                                    })
                                })
                            } else o(t); else o(t)
                        };
                        W.default.callApi({
                            url: "/api/integration/common1/getDelegateSSO",
                            method: "POST",
                            params: o
                        }).then(function (n) {
                            var a = n.isDelegate, r = n.em_isuse, l = n.delegateLogin_URL, s = n.delegateCheckLogin_URL;
                            a && r ? l && s ? W.default.callApi({
                                url: window.decodeURIComponent(l),
                                method: "GET"
                            }).then(function (n) {
                                if (n && 1 == n.isLogined) {
                                    if (n.serviceURL) return void (window.location.href = (0, $.addContentPath)(n.serviceURL));
                                    i(e, o)
                                } else j.default.error(K("521345,498", "Delegate\u8ba4\u8bc1\u5931\u8d25"), 5), t.logining = !1, W.default.callApi({
                                    url: "/api/integration/common1/getDelegateSSO",
                                    method: "POST",
                                    params: {command: "clearDelegateID"}
                                })
                            }) : (j.default.error(K("521345,498", "Delegate\u8ba4\u8bc1\u5931\u8d25"), 5), t.logining = !1, W.default.callApi({
                                url: "/api/integration/common1/getDelegateSSO",
                                method: "POST",
                                params: {command: "clearDelegateID"}
                            })) : i(e, o)
                        }).catch(function () {
                            return i(e, o)
                        })
                    }
                }, {
                    key: "onQCLoginStart", value: function () {
                        var e = this;
                        this.qrcodeIsInvalid = !1;
                        var t = this.qrcodeValidTime;
                        1 == this.qrcodeReqTime || this.qrcodeReqTime;
                        this.qrcodeCdInterval = window.setInterval(function () {
                            e.qrcodeUsedTime >= t && e.onQCLoginEnd(), e.qrcodeUsedTime++
                        }, 1e3), this.onPollingStart()
                    }
                }, {
                    key: "onPollingStart", value: function () {
                        var e = this.qrcodeValidTime;
                        this.qrcodeUsedTime >= e && this.onQCLoginEnd(), this.onQCLogin()
                    }
                }, {
                    key: "onQCLoginEnd", value: function () {
                        this.qrcodeIsInvalid = !0, this.qrcodeUsedTime = 0, window.clearInterval(this.qrcodeCdInterval), window.clearInterval(this.qrcodeLoginInterval)
                    }
                }, {
                    key: "setRemember", value: function (e) {
                        e && (window.em && window.em.checkJsApi("setItem") ? window.em.setItem({
                            key: "login-remember",
                            isPublic: !0,
                            value: (0, V.default)(e)
                        }) : window.localStorage.setItem("login-remember", (0, V.default)(e)))
                    }
                }, {
                    key: "getRemember", value: function () {
                        var e = this;
                        if (window.em && window.em.checkJsApi("getItem")) window.em.getItem({
                            key: "login-remember",
                            isPublic: !0,
                            success: function (t) {
                                var o = t.value;
                                e.remember = o ? JSON.parse(o) : 1
                            },
                            fail: function () {
                                e.remember = 1
                            }
                        }); else {
                            var t = window.localStorage.getItem("login-remember");
                            this.remember = t ? JSON.parse(t) : 1
                        }
                    }
                }, {
                    key: "setRemembered", value: function (e) {
                        e && (window.em && window.em.checkJsApi("setItem") ? window.em.setItem({
                            key: "login-remembered",
                            isPublic: !0,
                            value: (0, V.default)(e)
                        }) : window.localStorage.setItem("login-remembered", (0, V.default)(e)))
                    }
                }, {
                    key: "getRemembered", value: function (e) {
                        var t = this;
                        if (window.em && window.em.checkJsApi("getItem")) window.em.getItem({
                            key: "login-remembered",
                            isPublic: !0,
                            success: function (o) {
                                var n = o.value;
                                t.remembered = n ? JSON.parse(n) : {}, window.em.getItem({
                                    key: "login-autologin",
                                    success: function (o) {
                                        var n = o.value;
                                        t.autologin = !!n && JSON.parse(n);
                                        var i = t.remembered.account, a = void 0 === i ? "" : i;
                                        t.getLoginForm({loginid: a, langid: e}, function () {
                                            return t.autoLogin()
                                        })
                                    },
                                    fail: function () {
                                        var o = t.remembered.account, n = void 0 === o ? "" : o;
                                        t.getLoginForm({loginid: n, langid: e})
                                    }
                                })
                            },
                            fail: function () {
                                t.remembered = {};
                                var o = t.remembered.account, n = void 0 === o ? "" : o;
                                t.getLoginForm({loginid: n, langid: e})
                            }
                        }); else {
                            var o = window.localStorage.getItem("login-remembered");
                            this.remembered = o ? JSON.parse(o) : {};
                            var n = window.localStorage.getItem("login-autologin");
                            this.autologin = !!n && JSON.parse(n);
                            var i = this.remembered.account, a = void 0 === i ? "" : i;
                            this.getLoginForm({loginid: a, langid: e}, function () {
                                return t.autoLogin()
                            })
                        }
                    }
                }, {
                    key: "autoLogin", value: function () {
                        var e = -1 != window.location.href.indexOf("em_logout=1"),
                            t = -1 != window.location.href.indexOf("from=backstage"), o = this.data || {},
                            n = o.formconfig, i = void 0 === n ? {} : n, a = i.showAutoLogin, r = this.remembered || {},
                            l = r.account, s = r.password;
                        !(0, G.isEMMobileClient)() && !e && !t && "form" == this.loginType && a && this.autologin && l && s && this.onLogin()
                    }
                }, {
                    key: "onServerChange", value: function () {
                        window.em && window.em.checkJsApi("switchServer") ? window.em.switchServer() : z.default.info(this.labels.label7, 1)
                    }
                }, {
                    key: "openNewLink", value: function () {
                        var e = this.data.agreeMentConfig, t = void 0 === e ? {} : e;
                        (0, G.openLink)(t.agreeMentLink)
                    }
                }, {
                    key: "onNewLogin", value: function () {
                        var e = this.data.agreeMentConfig, t = void 0 === e ? {} : e;
                        if (t && "1" == t.state) if ("0" == this.agreeMentState) {
                            var o = this.labels.label17 + t.agreeMentContent;
                            z.default.info(o, 2, null, !1)
                        } else this.onLogin(); else this.onLogin()
                    }
                }, {
                    key: "onStateChange", value: function (e) {
                        e ? (this.agreeMentState = e, this.setAgreeState(this.agreeMentState)) : "1" == this.agreeMentState ? (this.agreeMentState = "0", this.setAgreeState("0")) : (this.agreeMentState = "1", this.setAgreeState("1"))
                    }
                }, {
                    key: "setAgreeState", value: function (e) {
                        e && (window.em && window.em.checkJsApi("setItem") ? window.em.setItem({
                            key: "agreement-state",
                            isPublic: !0,
                            value: (0, V.default)(e)
                        }) : window.localStorage.setItem("agreement-state", (0, V.default)(e)))
                    }
                }, {
                    key: "getAgreeState", value: function () {
                        var e = this;
                        if (window.em && window.em.checkJsApi("getItem")) window.em.getItem({
                            key: "agreement-state",
                            isPublic: !0,
                            success: function (t) {
                                var o = JSON.parse(t.value);
                                e.agreeMentState = "1" == o ? o : "0"
                            },
                            fail: function () {
                                e.agreeMentState = "0"
                            }
                        }); else {
                            var t = JSON.parse(window.localStorage.getItem("agreement-state"));
                            this.agreeMentState = "1" == t ? t : "0"
                        }
                    }
                }, {
                    key: "getDataByOneClick", value: function () {
                        var e = this;
                        this.oneClickLoading = !1, this.oneClickLoadingFlag = !1;
                        for (var t = "zh", o = this.language.id, n = this.languageIdentify, i = 0, a = n.length; i < a; i++) n[i][o] && (t = n[i][o]);
                        window.em && window.em.invoke && window.em.invoke("supportCmccQuickLogin", {
                            lang_type: t,
                            into: 1,
                            success: function (t) {
                                e.oneClickLoadingFlag = !1, 0 === t.errCode && (1 === t.support ? e.oneClickLoading = !0 : e.oneClickLoading = !1)
                            },
                            fail: function () {
                                e.oneClickLoadingFlag = !1, e.oneClickLoading = !1
                            }
                        })
                    }
                }, {
                    key: "onClickOnLogin", value: function () {
                        var e = this;
                        this.oneClickLoadingFlag = !1;
                        for (var t = "zh", o = this.language.id, n = this.languageIdentify, i = 0, a = n.length; i < a; i++) n[i][o] && (t = n[i][o]);
                        window.em && window.em.invoke && window.em.invoke("cmccQuickLogin", {
                            lang_type: t,
                            into: 1,
                            success: function (t) {
                                e.oneClickLoadingFlag = !1
                            },
                            fail: function () {
                                e.oneClickLoadingFlag = !1
                            }
                        })
                    }
                }, {
                    key: "style", get: function () {
                        var e = this.data, t = e.backgroundconfig, o = void 0 === t ? {} : t, n = e.logoconfig,
                            i = void 0 === n ? {} : n, a = e.formconfig, r = void 0 === a ? {} : a,
                            l = e.loginbuttonconfig, s = void 0 === l ? {} : l, u = e.textbuttonconfig,
                            d = void 0 === u ? {} : u, c = e.copyrightconfig, g = void 0 === c ? {} : c,
                            p = e.qrcodeconfig, f = void 0 === p ? {} : p, m = [];
                        if (void 0 == o.backgroundImage || " " == o.backgroundImage) m.push(".portal-login {background-color:" + o.backgroundColor + ";}"); else {
                            var h = new XMLHttpRequest;
                            h.open("Get", (0, $.addContentPath)(o.backgroundImage), !1), h.send(), 404 === h.status ? m.push(".portal-login {background-color:" + o.backgroundColor + ";}") : m.push(".portal-login {background-image: url('" + (0, $.addContentPath)(o.backgroundImage) + "');background-repeat: no-repeat;background-size: 100% 100%;}")
                        }
                        return m.push(".portal-login-logo-text {color:" + i.fontColor + ";}"), m.push(".portal-login-logo-text {font-size:" + i.fontSize + "px;}"), m.push(".portal-login-logo-text {font-family:" + i.fontFamily + ";}"), m.push(".portal-login-form-label {color:" + r.iconColor + ";}"), m.push(".portal-login-form-control .am-list-item .am-input-control input::-moz-placeholder {color:" + r.fontColor + ";}"), m.push(".portal-login-form-control .am-list-item .am-input-control input::-ms-input-placeholder {color:" + r.fontColor + ";}"), m.push(".portal-login-form-control .am-list-item .am-input-control input::-webkit-input-placeholder {color:" + r.fontColor + ";}"), m.push(".portal-login-form-control .am-list-item .am-input-control input {color:" + r.fontHoveColor + ";}"), m.push(".portal-login-form-control .am-list-item .am-input-control input {font-size:" + r.fontSize + "px;}"), m.push(".portal-login-form-control .am-list-item .am-input-control input {font-family:" + r.fontFamily + ";}"), m.push(".portal-login-type-text {color:" + d.fontColor + ";}"), m.push(".portal-login-type-text {font-size:" + d.fontSize + "px;}"), m.push(".portal-login-type-text {font-family:" + d.fontFamily + ";}"), m.push(".portal-login-language-text {color:" + d.fontColor + ";}"), m.push(".portal-login-language-text {font-size:" + d.fontSize + "px;}"), m.push(".portal-login-language-text {font-family:" + d.fontFamily + ";}"), m.push(".portal-login-other-text {color:" + d.fontColor + ";}"), m.push(".portal-login-other-text {font-size:" + d.fontSize + "px;}"), m.push(".portal-login-other-text {font-family:" + d.fontFamily + ";}"), m.push(".portal-login-dynamic-text {color:" + d.fontColor + ";}"), m.push(".portal-login-dynamic-text {font-size:" + d.fontSize + "px;}"), m.push(".portal-login-dynamic-text {font-family:" + d.fontFamily + ";}"), m.push(".portal-login-remember-text {color:" + d.fontColor + ";}"), m.push(".portal-login-remember-text {font-size:" + d.fontSize + "px;}"), m.push(".portal-login-remember-text {font-family:" + d.fontFamily + ";}"), m.push(".portal-login-forgot-text {color:" + d.fontColor + ";}"), m.push(".portal-login-forgot-text {font-size:" + d.fontSize + "px;}"), m.push(".portal-login-forgot-text {font-family:" + d.fontFamily + ";}"), m.push(".portal-login-server-text {color:" + d.fontColor + ";}"), m.push(".portal-login-server-text {font-size:" + d.fontSize + "px;}"), m.push(".portal-login-server-text {font-family:" + d.fontFamily + ";}"), void 0 == s.imgbtn ? (m.push(".portal-login-button .am-button {visibility:visible}"), m.push(".portal-login-button .portal-login-imgbutton .am-button {visibility:hidden;height:0px;}"), m.push(".portal-login-button .am-button {color:" + s.fontColor + ";}"), m.push(".portal-login-button .am-button {font-size:" + s.fontSize + "px;}"), m.push(".portal-login-button .am-button {font-family:" + s.fontFamily + ";}"), m.push(".portal-login-button .am-button {background-color:" + s.backgroundColor + ";}"), void 0 != s.opacity ? 0 == s.opacity ? m.push(".portal-login-button .am-button {opacity:0};}") : m.push(".portal-login-button .am-button {opacity:" + .01 * s.opacity + ";}") : m.push(".portal-login-button .am-button {opacity:1};}"), m.push(".portal-login-button .am-button {border-radius:" + (s.border || "0") + "px;}")) : 0 == s.imgbtn ? (m.push(".portal-login-button .am-button {visibility:visible}"), m.push(".portal-login-button .portal-login-imgbutton .am-button {visibility:hidden;height:0px;}"), m.push(".portal-login-button .am-button {color:" + s.fontColor + ";}"), m.push(".portal-login-button .am-button {font-size:" + s.fontSize + "px;}"), m.push(".portal-login-button .am-button {font-family:" + s.fontFamily + ";}"), m.push(".portal-login-button .am-button {background-color:" + s.backgroundColor + ";}"), void 0 != s.opacity ? 0 == s.opacity ? m.push(".portal-login-button .am-button {opacity:0};}") : m.push(".portal-login-button .am-button {opacity:" + .01 * s.opacity + ";}") : m.push(".portal-login-button .am-button {opacity:1};}"), m.push(".portal-login-button .am-button {border-radius:" + (s.border || "0") + "px;}")) : (m.push(".portal-login-button .am-button {visibility:hidden;height:0px;}"), m.push(".portal-login-button .portal-login-imgbutton .am-button {visibility:visible}"), m.push(".portal-login-button .portal-login-imgbutton .am-button {background-image:url(" + (0, $.addContentPath)(s.content) + ");}"), m.push(".portal-login-button .portal-login-imgbutton .am-button {background-size:100% 100%;}"), m.push(".portal-login-button .portal-login-imgbutton .am-button {background-repeat:no-repeat;}"), m.push(".portal-login-button .portal-login-imgbutton .am-button-primary {background-color:rgba(255,255,255,1);}"), m.push(".portal-login-button .portal-login-imgbutton .am-button {color:" + s.imgfontColor + ";}"), m.push(".portal-login-button .portal-login-imgbutton .am-button {font-size:" + s.imgfontSize + "px;}"), m.push(".portal-login-button .portal-login-imgbutton .am-button {font-family:" + s.imgfontFamily + ";}"), void 0 != s.imgopacity ? 0 == s.imgopacity ? m.push(".portal-login-button .portal-login-imgbutton .am-button {opacity:0};}") : m.push(".portal-login-button .portal-login-imgbutton .am-button {opacity:" + .01 * s.imgopacity + ";}") : m.push(".portal-login-button .portal-login-imgbutton .am-button {opacity:1};}"), m.push(".portal-login-button .portal-login-imgbutton>.am-button {height:40px;line-height:40px;border:none;cursor:pointer;}"), void 0 == s.isShowtext ? m.push(".portal-login-button .portal-login-imgbutton .am-button .imgbutton-text {visibility:hidden;height:0px;}") : 1 == s.isShowtext ? m.push(".portal-login-button .portal-login-imgbutton .am-button .imgbutton-text {visibility:visible}") : m.push(".portal-login-button .portal-login-imgbutton .am-button .imgbutton-text {visibility:hidden;height:0px;}")), m.push(".portal-login-copyright-text {color:" + g.fontColor + ";}"), m.push(".portal-login-copyright-text {font-size:" + g.fontSize + "px;}"), m.push(".portal-login-copyright-text {font-family:" + g.fontFamily + ";}"), m.push(".portal-login-qrcode-text {color:" + f.fontColor + ";}"), m.push(".portal-login-qrcode-text {font-size:" + f.fontSize + "px;}"), m.push(".portal-login-qrcode-text {font-family:" + f.fontFamily + ";}"), m.join("")
                    }
                }]), e
            }(), l = a(r.prototype, "loading", [H.observable], {
                enumerable: !0, initializer: function () {
                    return !1
                }
            }), s = a(r.prototype, "queryParams", [H.observable], {
                enumerable: !0, initializer: function () {
                    return (0, G.getQueryParams)()
                }
            }), u = a(r.prototype, "logining", [H.observable], {
                enumerable: !0, initializer: function () {
                    return !1
                }
            }), d = a(r.prototype, "language", [H.observable], {
                enumerable: !0, initializer: function () {
                    return {}
                }
            }), c = a(r.prototype, "languages", [H.observable], {
                enumerable: !0, initializer: function () {
                    return []
                }
            }), g = a(r.prototype, "languageIdentify", [H.observable], {
                enumerable: !0, initializer: function () {
                    return []
                }
            }), p = a(r.prototype, "languagesVisible", [H.observable], {
                enumerable: !0, initializer: function () {
                    return !1
                }
            }), f = a(r.prototype, "data", [H.observable], {
                enumerable: !0, initializer: function () {
                    return {}
                }
            }), m = a(r.prototype, "labels", [H.observable], {
                enumerable: !0, initializer: function () {
                    return {}
                }
            }), h = a(r.prototype, "autologin", [H.observable], {
                enumerable: !0, initializer: function () {
                    return !1
                }
            }), y = a(r.prototype, "remember", [H.observable], {
                enumerable: !0, initializer: function () {
                    return 1
                }
            }), v = a(r.prototype, "remembered", [H.observable], {
                enumerable: !0, initializer: function () {
                    return {}
                }
            }), b = a(r.prototype, "loginForm", [H.observable], {
                enumerable: !0, initializer: function () {
                    return {}
                }
            }), w = a(r.prototype, "dynamicPasswordTime", [H.observable], {
                enumerable: !0, initializer: function () {
                    return 0
                }
            }), C = a(r.prototype, "seriesnum_", [H.observable], {
                enumerable: !0, initializer: function () {
                    return 0
                }
            }), k = a(r.prototype, "validateCode", [H.observable], {
                enumerable: !0, initializer: function () {
                    return "/weaver/weaver.file.MakeValidateCode?seriesnum_=0"
                }
            }), L = a(r.prototype, "loginType", [H.observable], {
                enumerable: !0, initializer: function () {
                    return "form"
                }
            }), S = a(r.prototype, "qrcodeValidTime", [H.observable], {
                enumerable: !0, initializer: function () {
                    return 30
                }
            }), P = a(r.prototype, "qrcodeReqTime", [H.observable], {
                enumerable: !0, initializer: function () {
                    return 1
                }
            }), x = a(r.prototype, "qrcodeIsInvalid", [H.observable], {
                enumerable: !0, initializer: function () {
                    return !1
                }
            }), _ = a(r.prototype, "agreeMentState", [H.observable], {
                enumerable: !0, initializer: function () {
                    return "0"
                }
            }), I = a(r.prototype, "oneClickLoading", [H.observable], {
                enumerable: !0, initializer: function () {
                    return !1
                }
            }), R = a(r.prototype, "oneClickLoadingFlag", [H.observable], {
                enumerable: !0, initializer: function () {
                    return !1
                }
            }), q = a(r.prototype, "qrcodeUsedTime", [H.observable], {
                enumerable: !0, initializer: function () {
                    return 0
                }
            }), a(r.prototype, "style", [H.computed], (0, M.default)(r.prototype, "style"), r.prototype), a(r.prototype, "getLanguages", [H.action], (0, M.default)(r.prototype, "getLanguages"), r.prototype), a(r.prototype, "getLabels", [H.action], (0, M.default)(r.prototype, "getLabels"), r.prototype), a(r.prototype, "onLanguageChange", [H.action], (0, M.default)(r.prototype, "onLanguageChange"), r.prototype), a(r.prototype, "onLanguagesVisibleChange", [H.action], (0, M.default)(r.prototype, "onLanguagesVisibleChange"), r.prototype), a(r.prototype, "onAutoLoginChange", [H.action], (0, M.default)(r.prototype, "onAutoLoginChange"), r.prototype), a(r.prototype, "onRememberChange", [H.action], (0, M.default)(r.prototype, "onRememberChange"), r.prototype), a(r.prototype, "onRemember", [H.action], (0, M.default)(r.prototype, "onRemember"), r.prototype), a(r.prototype, "onRemoveRemember", [H.action], (0, M.default)(r.prototype, "onRemoveRemember"), r.prototype), a(r.prototype, "getDataByUsing", [H.action], (0, M.default)(r.prototype, "getDataByUsing"), r.prototype), a(r.prototype, "getDataById", [H.action], (0, M.default)(r.prototype, "getDataById"), r.prototype), a(r.prototype, "onDataChange", [H.action], (0, M.default)(r.prototype, "onDataChange"), r.prototype), a(r.prototype, "getLoginForm", [H.action], (0, M.default)(r.prototype, "getLoginForm"), r.prototype), a(r.prototype, "onLoginTypeChange", [H.action], (0, M.default)(r.prototype, "onLoginTypeChange"), r.prototype), a(r.prototype, "onValidateCodeChange", [H.action], (0, M.default)(r.prototype, "onValidateCodeChange"), r.prototype), a(r.prototype, "getDynamicPassword", [H.action], (0, M.default)(r.prototype, "getDynamicPassword"), r.prototype), a(r.prototype, "onForgot", [H.action], (0, M.default)(r.prototype, "onForgot"), r.prototype), a(r.prototype, "onLogin", [H.action], (0, M.default)(r.prototype, "onLogin"), r.prototype), a(r.prototype, "onQCLogin", [H.action], (0, M.default)(r.prototype, "onQCLogin"), r.prototype), a(r.prototype, "onLoginJump", [H.action], (0, M.default)(r.prototype, "onLoginJump"), r.prototype), a(r.prototype, "onQCLoginStart", [H.action], (0, M.default)(r.prototype, "onQCLoginStart"), r.prototype), a(r.prototype, "onPollingStart", [H.action], (0, M.default)(r.prototype, "onPollingStart"), r.prototype), a(r.prototype, "onQCLoginEnd", [H.action], (0, M.default)(r.prototype, "onQCLoginEnd"), r.prototype), a(r.prototype, "setRemember", [H.action], (0, M.default)(r.prototype, "setRemember"), r.prototype), a(r.prototype, "getRemember", [H.action], (0, M.default)(r.prototype, "getRemember"), r.prototype), a(r.prototype, "setRemembered", [H.action], (0, M.default)(r.prototype, "setRemembered"), r.prototype), a(r.prototype, "getRemembered", [H.action], (0, M.default)(r.prototype, "getRemembered"), r.prototype), a(r.prototype, "autoLogin", [H.action], (0, M.default)(r.prototype, "autoLogin"), r.prototype), a(r.prototype, "onServerChange", [H.action], (0, M.default)(r.prototype, "onServerChange"), r.prototype), a(r.prototype, "openNewLink", [H.action], (0, M.default)(r.prototype, "openNewLink"), r.prototype), a(r.prototype, "onNewLogin", [H.action], (0, M.default)(r.prototype, "onNewLogin"), r.prototype), a(r.prototype, "onStateChange", [H.action], (0, M.default)(r.prototype, "onStateChange"), r.prototype), a(r.prototype, "setAgreeState", [H.action], (0, M.default)(r.prototype, "setAgreeState"), r.prototype), a(r.prototype, "getAgreeState", [H.action], (0, M.default)(r.prototype, "getAgreeState"), r.prototype), a(r.prototype, "getDataByOneClick", [H.action], (0, M.default)(r.prototype, "getDataByOneClick"), r.prototype), a(r.prototype, "onClickOnLogin", [H.action], (0, M.default)(r.prototype, "onClickOnLogin"), r.prototype), r);
        t.default = X
    }, 853: function (e, t, o) {
        "use strict";
        Object.defineProperty(t, "__esModule", {value: !0});
        t.addContentPath = function (e) {
            var t = window.ecologyContentPath;
            return e && t && e.startsWith("/") && !e.startsWith(t) && (e = t + e), e == t && (e = ""), e
        }
    }
}, [782]);
//# sourceMappingURL=main.js.map?v=77a6e9df