# import requests
#
# session = requests.session()
#
# headers = {
#     "Accept": "*/*",
#     "Accept-Encoding": "gzip, deflate",
#     "Accept-Language": "zh-CN,zh;q=0.9",
#     "Cache-Control": "no-cache",
#     "Connection": "keep-alive",
#     "Content-Length": "133",
#     "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
#     "Host": "************:8888",
#     "Origin": "http://************:8888",
#     "Pragma": "no-cache",
#     "Referer": "http://************:8888/wui/index.html",
#     "User-Agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36",
#     "X-Requested-With": "XMLHttpRequest",
#     'Cookie': "ecology_JSessionid=aaaoqRTzRqzLXd5WMhozz; languageidweaver=7; loginuuids=943; "
#               "extloginid=90b4d97f674643c2b77e7e97fdd73628; loginidweaver=943; CASTGC=TGT-135-LMaKuze"
#               "j4IXqiQVzx6DsZwoRhcZ97YXc0OUHSI9BFjv931f2as-c01; __randcode__=9ce92a5e-53d0-4b89-abfa-ed0626839bca"
# }
#
# cookie = {
#
#     'Cookie': "ecology_JSessionid=aaaoqRTzRqzLXd5WMhozz; languageidweaver=7; loginuuids=943; "
#               "extloginid=90b4d97f674643c2b77e7e97fdd73628; loginidweaver=943; CASTGC=TGT-135-LMaKuze"
#               "j4IXqiQVzx6DsZwoRhcZ97YXc0OUHSI9BFjv931f2as-c01; __randcode__=9ce92a5e-53d0-4b89-abfa-ed0626839bca"
# }
#
# data = {
#     'tabkey': 'default_3',
#     'showAllLevel': 1,
#     'virtualtype': '',
#     'resourcename': '肖伟',
#     'manager': '',
#     'subcompany': '',
#     'department': '',
#     'telephone': '',
#     'mobile': '',
#     'mobilecall': '',
#     'jobtitle': '',
# }
#
# url = 'http://************:8888/api/hrm/search/getHrmSearchResult'
#
# session.headers = headers
#
# res = session.post(url=url, data=data)
# sessionkey = res.json()['sessionkey']
#
# url = 'http://************:8888/api/ec/dev/table/datas'
#
# data = {
#     "dataKey": sessionkey,
#     "current": "1",
#     "sortParams": []
# }
# res = session.post(url, data)
# data = res.json()
# for i in data['datas']:
#     state = '掉线' if i['idspan'] == '' else '在线'
#     print(f"{i['lastname']}  {i['jobtitlespan']}  {i['departmentidspan']} {state}")
import base64
import binascii
import time

import execjs
# print(execjs.get().name)
import requests
from Crypto.Cipher import PKCS1_OAEP
from Crypto.PublicKey import RSA

def get_time_temp():
    return int(time.time() * 1000)


def rsa_encrypt(public_key, text):
    rsa_key = RSA.importKey(public_key)
    rsa_cipher = PKCS1_OAEP.new(rsa_key)
    encrypt_text = rsa_cipher.encrypt(text.encode())
    return binascii.hexlify(encrypt_text).decode()

from urllib.parse import quote


class OALogin:
    def __init__(self):
        self.pk = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAp2aay1967x1mLWb7Z9gUXfPnEFttUcNeCnmMAPx9WotkiWFBr34QQSVhnyLlR2J0pHHKv0UleKbsqYoJD/T8tiPk8Cm17uMXpP0iDPeppsBWssTDIgZxDqxTstsl4wsuyT/UB/djYEqabuObpl9tGKG3qAh4IFn6ZxxQTmlsKHhQG5Nxhdi7ChwhwcrSl04H9Q2kapQngdG8wOmjpG5KZ8ctHBmsolFxWd1/MFe8IAG4+3YkI4SwFLWG7a3DywS2OL4YYfL4+vJPXjmIyTGnzrlPjxil8SlVQuawZAJtHBooHJytKdvO93yt8w3VX9oXhn2fsIBWc46dkYulZgSoqQIDAQAB
-----END PUBLIC KEY-----"""
        self.session = requests.session()

        self.session.headers = {
            "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Host": "************:8888",
            "Origin": "http://************:8888",
            "Pragma": "no-cache",
            "Referer": "http://************:8888/wui/index.html?",
            "User-Agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36",
            "X-Requested-With": "XMLHttpRequest"
        }
        self.get_rsa_url = f'http://************:8888/rsa/weaver.rsa.GetRsaInfo?ts={get_time_temp()}'
        self.login_url = 'http://************:8888/api/hrm/login/checkLogin'

    def hex2b64(self, hex_str):
        # print(hex_str)
        byte_date = bytes.fromhex(hex_str)
        b64_data = base64.b64encode(byte_date)
        # print(b64_data)
        return b64_data.decode('utf-8')

    def get_login_code(self):
        res = self.session.get(self.get_rsa_url).json()
        # print(self.session.cookies)
        return res['rsa_code'], res['rsa_flag']

    def text2_rsa(self, text):
        code, flag = self.get_login_code()
        print(code, flag)
        # encrypt_text = text + code
        # name_en = rsa_encrypt(self.pk, encrypt_text)
        # rsa_text = self.hex2b64(name_en) + flag
        # print(f'{text} RSA加密结果： {rsa_text}')
        print('开始执行加密...')
        with open('1.js', 'r', encoding='utf-8') as f:
            ctx = execjs.compile(f.read())
            res = ctx.call('get_result', code, text)
            print(f"{text}加密结果 {res}")
            return quote(res)

    def login(self, user_name, user_pwd):
        data = {
            "islanguid": 7,
            "loginid": self.text2_rsa(user_name),
            "userpassword": self.text2_rsa(user_pwd),
            "dynamicPassword": "",
            "tokenAuthKey": "",
            "validatecode": "",
            "validateCodeKey": "",
            "logintype": 1,
            "messages": "",
            "isie": "false",
            "appid": "",
            "service": "",
            "isRememberPassword": "false",
        }
        # print(self.session.headers)

        headers = {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Length": "939",
            "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
            "Host": "************:8888",
            "Origin": "http://************:8888",
            "Pragma": "no-cache",
            "Referer": "http://************:8888/wui/index.html",
            "User-Agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36",
            "X-Requested-With": "XMLHttpRequest",
        }
        self.session.headers = headers
        print(self.session.cookies)
        res = self.session.post(url=self.login_url, data=data, )
        print(res.json())

a = OALogin()
a.login('HMJX30592', 'gsh1493829867')
# a = 'NkrzZvslTbwpwxHalR8b01OjFj9c355CZrNJmPG0OpG3LGEKAVdbQws1+KqujS6bVCpCJySMkJwoLHqe6RzQMPac4vPFcQMTYyirZWN5cMOSRFzsJCqPA8mpMHSAqmgnHDbZrIg3SnJkRDA7Nx1xCd5V1Fxj2imH31KQmcUwid4=``RSA``'
# print(quote(a))
