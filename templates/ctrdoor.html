<div class="layui-fluid">
    <div class="layui-row">
        {#                左侧导航栏#}
        <div class="layui-col-md1">
            {#                        style="width: 100%; color: black; background-color: white; #}
            <ul class="layui-nav layui-nav-tree layui-bg-gray left-nav" lay-filter="left-nav"
                style="width: 100%; height: 100%">
                {#                            <li class="layui-nav-item m-left-nav layui-this " id="base-TS">#}
                {#                                <a>基本态势</a>#}
                {#                            </li>#}
                <li class="layui-nav-item door-m-left-nav" id="d">
                    <a>保密点位申请</a>
                </li>
                <li class="layui-nav-item door-m-left-nav " id="a">
                    <a>保密点位列表</a>
                </li>
                <li class="layui-nav-item door-m-left-nav" id="b">
                    <a>新增保密点位</a>
                </li>
                <li class="layui-nav-item door-m-left-nav" id="add-user">
                    <a>人员录入</a>
                </li>

            </ul>
        </div>
        {#右侧数据栏目#}
        <div class="layui-col-md11">
            <div class="layui-tab scrollable-tab" lay-filter="door-l-tab" lay-allowClose="true">
                <ul class="layui-tab-title door-tab-title-li">
                    <li class="layui-this " id="door-base-TS-li" lay-allowClose="false">人员信息</li>
                </ul>
                <div class="layui-tab-content door-data-tab" id="door-l-tab-page" style="">
                    <div class="layui-tab-item layui-show" style="" id="door-base-TS-li-content">
                        <div class="layui-fluid tab-child">
                            <div class="layui-row" id="door-base-TS-content">
                                <div class="layui-col-md2 tab-input" style="">
                                    <label class="layui-form-label input-label search-label">名称搜索:</label>
                                    <input type="text" name="TS-search-name" id="door-TS-search-name"
                                           class="layui-input search-input"
                                           placeholder="名称搜索" style="">
                                </div>
                                <div class="layui-col-md2 tab-input" style="">
                                    <label class="layui-form-label input-label search-label">IP搜索:</label>
                                    <input type="text" name="door-TS-search-ip" id="door-TS-search-ip"
                                           class="layui-input search-input"
                                           placeholder="IP搜索" style="">
                                </div>
                                <div class="layui-col-md2" style="">
                                    <div class="layui-form">
                                        <div class="layui-form-item tab-layui-form-item" style="">
                                            <label class="layui-form-label input-label search-label">部门搜索:</label>
                                            <div class="layui-input-inline">
                                                <select name="door-TS-search-department"
                                                        id="door-TS-search-department"
                                                        class=""
                                                        style="padding-right: 10px">
                                                    <option value=""></option>
                                                    {% for i in department %}
                                                        <option value="{{ i }}">{{ i }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md2" style="">
                                    <div class="layui-form">
                                        <div class="layui-form-item tab-layui-form-item" style="">
                                            <label class="layui-form-label input-label search-label">点位搜索:</label>
                                            <div class="layui-input-inline">
                                                <select name="door-TS-search-position" id="door-TS-search-position"
                                                        class=""
                                                        style="padding-right: 10px">
                                                    <option value=""></option>
                                                    {% for i in position %}
                                                        {% for key, value in i.items() %}
                                                            <option value="{{ key }}">{{ value }}</option>
                                                        {% endfor %}
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-col-md2" style="">
                                    <div class="layui-form">
                                        <div class="layui-form-item tab-layui-form-item" style="">
                                            <label class="layui-form-label input-label search-label">状态搜索:</label>
                                            <div class="layui-input-inline">
                                                <select name="door-TS-search-state" id="door-TS-search-state"
                                                        class="">
                                                    <option value=""></option>
                                                    {% for i in state %}
                                                        <option value="{{ i }}">{{ i }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md2 search-tool-btn door-search-tool-btn" style="">
                                    <button type="button" class="layui-btn door-TS-search-btn"
                                            id="TS-search-btn">搜索
                                    </button>
                                    <button type="button" class="layui-btn door-TS-refresh-btn"
                                            id="door-table-refresh-btn">刷新
                                    </button>
                                </div>
                            </div>
                            <hr>
                            <table id="door" style="width: 100%;height: 100%"></table>
                            <script>

                            </script>
                            <script id="op-door-info-btn" type="text/html">
                                <a class="layui-btn layui-btn-xs " lay-event="edit">编辑</a>
                                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del-door-info">删除</a>
                            </script>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{#编辑人员#}
<div class="layui-tab-item" style="display: none; visibility: hidden" id="edit-user-li-content">
    <form class="layui-form" id="editUserForm">
        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-md5 layui-col-xl-offset1 layui-inline" style="">
                    <label class=" layui-form-label">编号</label>
                    <div class="layui-input-inline">
                        <input type="text" name="id" id="edit-user-id" required placeholder="请输入编号（唯一标识）"
                               class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-md5 layui-col-xl-offset1 layui-inline" style="">
                    <label class=" layui-form-label">姓名</label>
                    <div class="layui-input-inline">
                        <input type="text" name="name" id="edit_user_name" required placeholder="请输入姓名"
                               class="layui-input">
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-md5 layui-col-xl-offset1 layui-inline" style="">
                    <label class=" layui-form-label">上传人脸</label>
                    <div class="layui-input-inline">
                        <button type="button" class="layui-btn layui-btn-normal " id="edit-user-pic-choose">
                            <i class="layui-icon layui-icon-upload"></i> 选择图片
                        </button>
                        <input type="file" id="edit-fileInput" style="display: none">
                        <div id="edit-upload-img">
                            <div class="layui-upload-list">
                                <img class="layui-upload-img" id="edit-previewImg"
                                     style="max-width: 200px; margin-top: 10px; display: none">
                                <p id="edit-uploadText"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-md5 layui-col-xl-offset1 layui-inline" style="">
                    <label class=" layui-form-label">时间范围</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="edit-date-time-choose" autocomplete="off">
                        {#                               placeholder="  -  ">#}
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-md1 layui-col-xl-offset2" style="">
                    <button class="layui-btn submit-add-user-btn" type="button" id="sub-edit-user">立即提交</button>
                </div>
                <div class="layui-col-md1" style="">
                    <button class="layui-btn layui-btn-primary" id="edit-form-reset" type="reset">重置</button>
                </div>
            </div>
        </div>
    </form>
    <script>
        var selectFile = null

        {#用户编辑#}
        $('#edit-user-pic-choose').click(function () {
            {#debugger#}
            $('#edit-fileInput').click()
        })

        {#用户编辑图片上传#}
        $('#edit-fileInput').change(function (e) {
            debugger
            var file = e.target.files[0]
            if (file) {
                var validTypes = ['image/jpeg']
                if (validTypes.indexOf(file.type) === -1) {
                    layer.msg('请上传JPG格式图片')
                    return;
                }
                var uploadImgDiv = $('#edit-upload-img')
                {#debugger#}
                if (uploadImgDiv.children().length === 0) {
                    uploadImgDiv.append('<div class="layui-upload-list">\n' +
                        '                                <img class="layui-upload-img" id="edit-previewImg"\n' +
                        '                                     style="max-width: 200px; margin-top: 10px; display: none">\n' +
                        '                                <p id="edit-uploadText"></p>\n' +
                        '                            </div>')
                }

                var reader = new FileReader()
                reader.onload = function (e) {
                    $('#edit-previewImg').attr('src', e.target.result).show()
                }
                reader.readAsDataURL(file)

                selectFile = file
                layer.msg('图片已添加')
            }
        })

        $('#sub-edit-user').click(function () {
            var forValue = $('#editUserForm').serializeArray()
            {#debugger#}
            if (selectFile == null) {
                var formData = {};
                formData.time = $('#edit-date-time-choose').val()
                $.each(forValue, function (i, field) {
                    {#debugger#}
                    if (field.value !== '') {
                        let key = field.name
                        formData[key] = field.value
                    }
                });
                debugger
                $.ajax({
                    url: '/door/editUser/',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function (res) {
                    }
                })
                return
            }
            formData = new FormData();
            {#forValue = $('#editUserForm').serializeArray()#}
            console.log(forValue)
            $.each(forValue, function (i, field) {
                formData.append(field.name, field.value)
            });

            formData.append('user_img', selectFile)
            var loadIndex = layer.load(2);
            debugger
            $.ajax({
                url: '/door/addUser',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (res) {

                    layer.close(loadIndex)
                    layer.msg('添加成功')
                    $("#form-reset").click()
                    $('.layui-upload-list').remove()
                    {#laydate.render({#}
                    {#    elem: '#date-time-choose',#}
                    {#    type: 'datetime',#}
                    {#    range: true,#}
                    {#    format: 'yyyy-MM-dd HH:mm:ss  -  yyyy-MM-dd HH:mm:ss',#}
                    {#    value: new Date(),#}
                    {#    trigger: 'click'#}
                    {#\})#}
                }
            })
        })


    </script>
</div>

{#新增人员#}
<div class="layui-tab-item" style="display: none" id="add-user-li-content">
    <form class="layui-form" id="addUserForm">

        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-md5 layui-col-xl-offset1 layui-inline" style="">
                    <label class=" layui-form-label">编号</label>
                    <div class="layui-input-inline">
                        <input type="text" name="id" id="add-user-id" required placeholder="请输入编号（唯一标识）"
                               class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-md5 layui-col-xl-offset1 layui-inline" style="">
                    <label class=" layui-form-label">姓名</label>
                    <div class="layui-input-inline">
                        <input type="text" name="name" id="add_user_name" required placeholder="请输入姓名"
                               class="layui-input">
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-md5 layui-col-xl-offset1 layui-inline" style="">
                    <label class=" layui-form-label">上传人脸</label>
                    <div class="layui-input-inline">
                        <button type="button" class="layui-btn layui-btn-normal " id="user-pic-choose">
                            <i class="layui-icon layui-icon-upload"></i> 选择图片
                        </button>
                        <input type="file" id="fileInput" style="display: none">
                        <div id="upload-img">
                            <div class="layui-upload-list">
                                <img class="layui-upload-img" id="previewImg"
                                     style="max-width: 200px; margin-top: 10px; display: none">
                                <p id="uploadText"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-md5 layui-col-xl-offset1 layui-inline" style="">
                    <label class=" layui-form-label">时间范围</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="date-time-choose" autocomplete="off">
                        {#                               placeholder="  -  ">#}
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-row">

                <div class="layui-col-md1 layui-col-xl-offset2" style="">
                    <button class="layui-btn submit-add-user-btn" type="button" id="sub-add-user">立即提交</button>
                </div>
                <div class="layui-col-md1" style="">
                    <button class="layui-btn layui-btn-primary" id="form-reset" type="reset">重置</button>
                </div>
            </div>
        </div>
    </form>
    <script>
        // 门禁日期控件初始化
        layui.use(['laydate', 'form'], function () {
            var laydate = layui.laydate;
            var now = new Date()
            var currentDateTime = now.getFullYear() + '-' +
                (now.getMonth() + 1).toString().padStart(2, '0') + '-' +
                (now.getDate()).toString().padStart(2, '0') + ' ' +
                (now.getHours()).toString().padStart(2, '0') + ':' +
                (now.getMonth()).toString().padStart(2, '0') + ':' +
                (now.getSeconds()).toString().padStart(2, '0');

            {#console.log(new Date())#}
            laydate.render({
                elem: '#date-time-choose',
                type: 'datetime',
                range: true,
                format: 'yyyy-MM-dd HH:mm:ss',
                value: currentDateTime + ' - ' + currentDateTime,
                trigger: 'click',
                done: function (value, date) {
                    $('#date-time-choose').val(value)
                }
            })
        })

        // 门禁人员添加提交及编辑上传控件
        layui.use(['form', 'upload', 'layer', 'laydate', 'element'], function () {
            var from = layui.form;
            var upload = layui.upload;
            var layer = layui.layer;
            var laydate = layui.laydate;

            var selectFile = null

            {#用户添加#}
            $('#user-pic-choose').click(function () {
                debugger
                $('#fileInput').click()
            })


            {#用户添加图片上传#}
            $('#fileInput').change(function (e) {
                debugger
                var file = e.target.files[0]
                if (file) {
                    var validTypes = ['image/jpeg']
                    if (validTypes.indexOf(file.type) === -1) {
                        layer.msg('请上传JPG格式图片')
                        return;
                    }
                    var uploadImgDiv = $('#upload-img')
                    {#debugger#}
                    if (uploadImgDiv.children().length === 0) {
                        uploadImgDiv.append('<div class="layui-upload-list">\n' +
                            '                                <img class="layui-upload-img" id="previewImg"\n' +
                            '                                     style="max-width: 200px; margin-top: 10px; display: none">\n' +
                            '                                <p id="uploadText"></p>\n' +
                            '                            </div>')
                    }

                    var reader = new FileReader()
                    reader.onload = function (e) {
                        $('#previewImg').attr('src', e.target.result).show()
                    }
                    reader.readAsDataURL(file)

                    selectFile = file
                    layer.msg('图片已添加')
                }
            })


            $('#sub-add-user').click(function () {
                var formData = new FormData();

                var forValue = $('#addUserForm').serializeArray()
                console.log(forValue)
                $.each(forValue, function (i, field) {
                    formData.append(field.name, field.value)
                });

                formData.append('user_img', selectFile)
                var loadIndex = layer.load(2);

                $.ajax({
                    url: '/door/addUser',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (res) {

                        layer.close(loadIndex)
                        layer.msg('添加成功')
                        $("#form-reset").click()
                        $('.layui-upload-list').remove()
                        {#laydate.render({#}
                        {#    elem: '#date-time-choose',#}
                        {#    type: 'datetime',#}
                        {#    range: true,#}
                        {#    format: 'yyyy-MM-dd HH:mm:ss  -  yyyy-MM-dd HH:mm:ss',#}
                        {#    value: new Date(),#}
                        {#    trigger: 'click'#}
                        {#\})#}
                    }
                })
            })
        })

        // 门禁编辑和删除点击事件处理
        layui.use(['table', 'element', 'form', 'laydate'], function () {
            var table = layui.table
            var element = layui.element;
            var form = layui.form
            var laydate = layui.laydate;

            table.on('checkbox(door)', function (obj) {
                var check_num = table.checkStatus('door').data.length
                if (!window.globalVal.deleteBtn) {
                    window.globalVal.deleteBtn = true
                    $('.door-search-tool-btn').append('<button type="button" class="layui-btn layui-btn-danger" id="deleteUser-btn">删除选中</button>')
                } else if (check_num === 0) {
                    $('#deleteUser-btn').remove()
                    $('#deleteUser-btn').remove()
                    window.globalVal.deleteBtn = false
                }
                if (obj.type === 'all') {
                    console.log('删除全部')
                } else {

                }
                // console.log(obj)
            })

            // 监听表格工具栏
            table.on('tool(door)', function (obj) {
                var event = obj.event;
                console.log(event, obj.data)
                {#debugger#}
                if (event === "edit") {
                    var id = obj.data.id
                    var name = obj.data.name
                    var start_time = obj.data.start_time
                    var end_time = obj.data.end_time
                    var img_url = obj.data.img_url
                    if ($('.door-tab-title-li li').text().includes('人员信息编辑')) {
                        element.tabDelete('door-l-tab', 'edit-user-li')
                    }
                    element.tabAdd('door-l-tab', {
                        title: '人员信息编辑',
                        content: $('#edit-user-li-content').html(),
                        id: 'edit-user-li'
                    });


                    {#debugger#}
                    $('#edit-user-id').val(id)
                    $('#edit_user_name').val(name)
                    {#var reader = new FileReader()#}
                    {#reader.onload = function (e) {#}
                    $('#edit-previewImg').attr('src', img_url).show()
                    {#$('#edit-date-time-choose').val()#}
                    laydate.render({
                        elem: '#edit-date-time-choose',
                        type: 'datetime',
                        range: true,
                        format: 'yyyy-MM-dd HH:mm:ss',
                        value: start_time + ' - ' + end_time,
                        trigger: 'click',
                        done: function (value, date) {
                            $('#edit-date-time-choose').val(value)
                        }
                    })


                    element.tabChange('door-l-tab', 'edit-user-li');
                    layui.element.render('tab')
                    form.render()
                    // 刷新表单、tab
                    {#layui.element.render('tab')#}
                    {#form.render()#}
                    {#\}#}
                    {#reader.readAsDataURL(file)#}
                    {##}
                    {#selectFile = file#}

                    {#console.log(click_id)#}
                    // 刷新表单、tab
                    {#layui.element.render('tab')#}
                    {#form.render()#}

                    {#element.tabDelete('l-tab', 'edit-user')#}
                    {#element.tabAdd('l-tab', {#}
                    {#    title: '人员编辑',#}
                    {#    content: $("#add-user-li-content").html(),#}
                    {#    id: 'add-user-li-content'#}
                    {#\});#}
                    {#$('#add_name_name').val(name)#}
                    {#$('#add_user_id').val(id)#}
                    {#$('#add_edit_ip').val(ip)#}
                    {#$('#add_edit_port').val(port)#}
                    {#$('#add_edit_department').val(department)#}
                    {#$('#add_edit_position').val(position)#}
                    {#$('#add-edit-create-time').val(creat_time)#}
                    {#// debugger#}
                    {#element.tabChange('l-tab', 'edit-pos');#}
                    {#layui.element.render('tab')#}
                    {#form.render()#}
                } else {
                    layer.open({
                        title: false,
                        content: '确认删除该数据吗？',
                        btn: ['是', '否'],
                        yes: function (index, layero) {
                            layer.close(index)
                            {#debugger#}
                            $.ajax({
                                url: '/door/delUser',
                                type: 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    id: obj.data.id
                                }),

                                success: function (res) {
                                    // console.log('响应结果', res)
                                    table.reload('door', {
                                        url: '/door/getInfo?page=1&limit=10'
                                    })
                                }
                            })
                        },
                        btn2: function () {
                        }
                    })
                }
            })
        })

        // 门禁多选删除按钮点击事件监听
        $(document).on('click', '#deleteUser-btn', function () {
            layui.use('table', function () {
                var table = layui.table
                {#debugger#}
                var info_list = table.checkStatus('door')
                // console.log(checkStatus.data)
                var id_list = []
                for (let i of info_list.data) {
                    id_list.push(i.id)
                }
                // console.log(creat_time_list)
                layer.open({
                    title: false,
                    content: '确认删除该数据吗？',
                    btn: ['是', '否'],
                    yes: function (index, layero) {
                        layer.close(index)
                        $.ajax({
                            url: '/door/delUsers',
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({
                                id_list: id_list
                            }),
                            success: function (res) {
                                // 此处彻底删除按钮，否则页面切换会出现多按钮BUG
                                $('#delete-btn').remove()
                                $('#delete-btn').remove()
                                window.globalVal.deleteBtn = false
                                table.reload('door', {
                                    url: '/door/getInfo?page=1&limit=10'
                                })
                            }
                        })
                    },
                    btn2: function () {
                    }
                })
            })
        })

        // 门禁数据表格刷新
        $(document).on('click', '#door-table-refresh-btn', function () {
            layui.use('table', function () {
                var table = layui.table;
                table.reload('door', {
                    url: '/door/getInfo?page=1&limit=10',
                    page: {
                        curr: 1
                    }
                })
            })
        });
    </script>
</div>




