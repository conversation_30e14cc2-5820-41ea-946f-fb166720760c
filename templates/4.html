<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>空调用电监测系统</title>
    <!-- 使用备用CDN -->
    <link rel="stylesheet" href="/static/css/layui.css">
    <style>
        .timeline-container {
            margin: 20px;
            padding: 20px;
            background-color: #f2f2f2;
            border-radius: 5px;
        }
        .data-container {
            margin: 20px;
            padding: 20px;
            background-color: #fff;
            border-radius: 5px;
            min-height: 300px;
        }
        .timeline-item {
            cursor: pointer;
        }
        .timeline-item:hover {
            background-color: #e6f7ff;
        }
        .selected {
            background-color: #e6f7ff;
            font-weight: bold;
        }
        .chart-container {
            height: 300px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
<div class="layui-layout layui-layout-admin">
    <!-- 头部 -->
    <div class="layui-header">
        <div class="layui-logo">空调用电监测系统</div>
    </div>
    
    <!-- 主体内容 -->
    <div class="layui-body">
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="timeline-container">
                    <fieldset class="layui-elem-field">
                        <legend>监测时间线</legend>
                        <div class="layui-field-box">
                            <ul class="layui-timeline" id="timeLine">
                                <li class="layui-timeline-item">
                                    <i class="layui-icon layui-timeline-axis"></i>
                                    <div class="layui-timeline-content layui-text">
                                        <div class="layui-timeline-title">加载中...</div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </fieldset>
                </div>
                
                <div class="data-container">
                    <fieldset class="layui-elem-field">
                        <legend>用电数据详情</legend>
                        <div class="layui-field-box">
                            <div id="dataDetail">
                                <blockquote class="layui-elem-quote">请点击时间线上的时间段查看详细数据</blockquote>
                            </div>
                            <div class="chart-container" id="powerChart"></div>
                        </div>
                    </fieldset>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部 -->
    <div class="layui-footer">
        © 2023 空调用电监测系统
    </div>
</div>

<!-- 使用备用CDN -->
<script src="/static/js/layui.js"></script>
<script src="http://************:8888/cloudstore/resource/pc/echarts4/echarts.min.js"></script>
<script>
// 资源加载失败处理
{#function loadLocalLayui() {#}
{#    document.write('<link rel="stylesheet" href="/static/layui/css/layui.css">');#}
{#    document.write('<script src="/static/layui/layui.js"><\/script>');#}
{#\}#}

layui.config({
    base: '/static/layui/modules/' // 配置模块基础目录
}).use(['jquery', 'layer', 'element', 'echarts'], function(){
    try {
        var $ = layui.jquery;
        var layer = layui.layer;
        var element = layui.element;
        var echarts = layui.echarts;
        
        // 模拟时间线数据
        var mockTimePoints = [
            {
                id: "1",
                startTime: "2023-06-01 08:00",
                endTime: "2023-06-01 12:00",
                deviceCount: 5
            },
            {
                id: "2",
                startTime: "2023-06-01 13:00",
                endTime: "2023-06-01 17:00",
                deviceCount: 5
            }
        ];
        
        // 模拟获取时间线数据
        function mockGetTimePoints() {
            return {
                code: 0,
                msg: "success",
                data: mockTimePoints
            };
        }
        
        // 从后端获取时间线数据
        function loadTimeline() {
            layer.load(2, {time: 10*1000}); // 设置10秒超时
            
            // 模拟异步请求
            setTimeout(function() {
                try {
                    var res = mockGetTimePoints();
                    layer.closeAll('loading');
                    if(res.code === 0) {
                        renderTimeline(res.data);
                    } else {
                        layer.msg('获取时间线数据失败: ' + res.msg, {icon: 2});
                        $('#timeLine').html('<li class="layui-timeline-item"><i class="layui-icon layui-timeline-axis"></i><div class="layui-timeline-content layui-text"><div class="layui-timeline-title">数据加载失败</div></div></li>');
                    }
                } catch (e) {
                    layer.closeAll('loading');
                    console.error("渲染时间线出错:", e);
                    $('#timeLine').html('<li class="layui-timeline-item"><i class="layui-icon layui-timeline-axis"></i><div class="layui-timeline-content layui-text"><div class="layui-timeline-title">渲染数据出错</div></div></li>');
                }
            }, 1500); // 1.5秒延迟
        }
        
        // 渲染时间线
        function renderTimeline(data) {
            try {
                var html = '';
                if(data && data.length > 0) {
                    data.forEach(function(item, index) {
                        html += '<li class="layui-timeline-item timeline-item" data-id="' + item.id + '" data-start="' + item.startTime + '" data-end="' + item.endTime + '">';
                        html += '    <i class="layui-icon layui-timeline-axis"></i>';
                        html += '    <div class="layui-timeline-content layui-text">';
                        html += '        <div class="layui-timeline-title">';
                        html += '            <span>' + item.startTime + ' 至 ' + item.endTime + '</span>';
                        html += '            <span class="layui-badge layui-bg-gray" style="margin-left: 10px;">' + item.deviceCount + '台设备</span>';
                        html += '        </div>';
                        html += '    </div>';
                        html += '</li>';
                    });
                } else {
                    html = '<li class="layui-timeline-item">';
                    html += '    <i class="layui-icon layui-timeline-axis"></i>';
                    html += '    <div class="layui-timeline-content layui-text">';
                    html += '        <div class="layui-timeline-title">暂无监测数据</div>';
                    html += '    </div>';
                    html += '</li>';
                }
                
                $('#timeLine').html(html);
                
                // 绑定点击事件
                $('.timeline-item').off('click').on('click', function() {
                    $('.timeline-item').removeClass('selected');
                    $(this).addClass('selected');
                    
                    var id = $(this).data('id');
                    var startTime = $(this).data('start');
                    var endTime = $(this).data('end');
                    
                    loadDeviceData(id, startTime, endTime);
                });
                
                // 默认选中第一个
                if(data && data.length > 0) {
                    $('.timeline-item').first().trigger('click');
                }
            } catch (e) {
                console.error("渲染时间线时出错:", e);
                layer.msg('渲染时间线时出错', {icon: 2});
            }
        }
        
        // 初始化加载时间线
        loadTimeline();
        
    } catch (e) {
        console.error("页面初始化错误:", e);
        layer.msg('页面初始化失败，请刷新重试', {icon: 2});
        $('#timeLine').html('<li class="layui-timeline-item"><i class="layui-icon layui-timeline-axis"></i><div class="layui-timeline-content layui-text"><div class="layui-timeline-title">初始化失败</div></div></li>');
    }
});
</script>
</body>
</html>