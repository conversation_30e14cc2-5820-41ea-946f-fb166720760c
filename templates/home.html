<!DOCTYPE html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>智能洞库厂内试验</title>
    <!-- 请勿在项目正式环境中引用该 layui.css 地址 -->
    {#  <link href="../util/layui/css/layui.css" rel="stylesheet">#}
    <link href="/static/css/layui.css" rel="stylesheet" media="all">
    <script src="/static/js/layui.js"></script>
    <script src="/static/js/jquery.js"></script>
{#    <script type="module" src="/static/js/panzoom.min.js"></script>#}
    <script src="/static/js/home.js"></script>


    <link href="/static/css/lay.css" rel="stylesheet" media="all">
</head>


<body>
{#顶部导航栏#}
{% include 'top_nav.html' %}

{#表单#}
<div class="layui-tab layui-tab-brief" lay-filter="top-tab">
    <ul class="layui-tab-title tab_ch_flag" style="text-align: center" id="{{ tab_ch }}">
        <li class="layui-this" lay-id="icon-TH-l">空调监控</li>
        <li lay-id="icon-door-l">门禁检测</li>
        <li class="inventory top-tab-btn" lay-id="icon-warehouse-l">
            库存管理
            {#            <div class="">#}
            {##}
            {#            </div>#}
        </li>
        <li lay-id="icon-crane-l">起重机管理</li>
    </ul>
    <div class="layui-tab-content">
        {#空调监控页面#}
        <div class="layui-tab-item layui-show ">
            {% include 'airconditioner_right_page.html' %}
        </div>
        <div class="layui-tab-item">
            {% include 'ctrdoor.html' %}
        </div>
        <div class="layui-tab-item iframe_item">
            {#            http://**************:50/proxy/Common/LogOn?message=%E8%AF%B7%E7%99%BB%E5%BD%95#}
            {#            <iframe id="iframe" src="http://**************:50/proxy/"#}
            {#                    height="800px" width="100%"#}
            {#                    sandbox="allow-same-origin allow-scripts allow-forms"#}
            {#                    allowfullscreen#}
            {#            ></iframe>#}
        </div>
        <div class="layui-tab-item">
            {% include 'crane_table.html' %}
        </div>
        {#        <div class="layui-tab-item">内容5</div>#}
        <!-- 请勿在项目正式环境中引用该 layui.js 地址 -->
    </div>

</div>

{#点位列表#}
{% include 'device_table.html' %}
{#新增点位#}
{% include 'add_device.html' %}
{#编辑点位#}
{% include 'edit_device.html' %}
{#历史数据#}
{% include 'history.html' %}

</body>




