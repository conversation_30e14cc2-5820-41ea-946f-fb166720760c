{#编辑点位#}
<div class="layui-tab-item" style="display: none" id="edit-pos-li-content">
    <form class="layui-form" action="/web/editDevice" method="post">

            <div class="layui-form-item">
                <div class="layui-row">
                    <div class="layui-col-md5 layui-col-xl-offset1 layui-inline" style="">
                        <label class=" layui-form-label">名称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="name" id="add_edit_name" required placeholder="请输入名称"
                                   class="layui-input">
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-row">
                    <div class="layui-col-md5 layui-col-xl-offset1 layui-inline" style="">
                        <label class="layui-form-label">部门</label>
                        <div class="layui-input-inline">
                            <select name="department" class="layui-select" id="add_edit_department" required>
                                <option value=""></option>
                                {% for i in department %}
                                    <option value="{{ i }}">{{ i }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-row">
                    <div class="layui-col-md5 layui-col-xl-offset1 layui-inline" style="">
                        <label class=" layui-form-label">点位</label>
                        <div class="layui-input-inline">
                            <input type="text" name="position" id="add_edit_position" required placeholder="请输入点位"
                                   class="layui-input">
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-row">
                    <div class="layui-col-md5 layui-col-xl-offset1 layui-inline" style="">
                        <label class=" layui-form-label">IP</label>
                        <div class="layui-input-inline">
                            <input type="text" name="ip" id="add_edit_ip" required placeholder="请输入IP"
                                   class="layui-input">
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-row">
                    <div class="layui-col-md5 layui-col-xl-offset1 layui-inline" style="">
                        <label class=" layui-form-label">端口</label>
                        <div class="layui-input-inline">
                            <input type="text" name="port" required
                                   placeholder="请输入端口 一般为502"
                                   id="add_edit_port"
                                   class="layui-input">
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-row">
                    <div class="layui-col-md5 layui-col-xl-offset1 layui-inline" style="">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-inline">
                            <select name="state" class="" id="add_edit_state" required>
                                <option value=""></option>
                                {% for i in state %}
                                    <option value="{{ i }}">{{ i }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <input type="hidden" name="create-time" id="add-edit-create-time">
            </div>
            <div class="layui-form-item">
                <div class="layui-row">
                    <div class="layui-col-md1 layui-col-xl-offset2" style="">
                        <button class="layui-btn submit-edit-btn" type="button">立即提交</button>
                    </div>
                    <div class="layui-col-md1" style="">
                        <button class="layui-btn layui-btn-primary" type="reset">重置</button>
                    </div>
                </div>
            </div>

    </form>
</div>