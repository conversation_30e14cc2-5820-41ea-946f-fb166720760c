{#新增点位#}
<div class="layui-tab-item" style="display: none" id="add-pos-li-content">

    <form class="layui-form" action="/web/adddevice">

        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-md5 layui-col-xl-offset1" style="">
                    <label class=" layui-form-label">名称</label>
                    <div class="layui-input-inline">
                        <input type="text" name="name" id="add-device-name" required placeholder="请输入名称"
                               class="layui-input">
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-md5 layui-col-xl-offset1" style="">
                    <label class="layui-form-label">部门</label>
                    <div class=" layui-input-inline ">

                        <select name="department" class="" id="add-device-department" required>
                            <option value=""></option>
                            {% for i in department %}
                                <option value="{{ i }}">{{ i }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-md5 layui-col-xl-offset1" style="">
                    <label class=" layui-form-label">点位</label>
                    <div class="layui-input-inline">
                        <input type="text" name="position" id="add-device-position" required placeholder="请输入点位"
                               class="layui-input">
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-md5 layui-col-xl-offset1" style="">
                    <label class=" layui-form-label">IP</label>
                    <div class="layui-input-inline">
                        <input type="text" name="ip" required id="add-device-ip" placeholder="请输入IP"
                               class="layui-input">
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-md5 layui-col-xl-offset1" style="">
                    <label class=" layui-form-label">端口</label>
                    <div class="layui-input-inline">
                        <input type="text" name="port" required
                               placeholder="请输入端口 一般为502"
                               id="add-device-port"
                               class="layui-input">
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-md5 layui-col-xl-offset1" style="">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-inline">
                        <select name="state" class="" id="add-device-state" required>
                            <option value=""></option>
                            {% for i in state %}
                                <option value="{{ i }}">{{ i }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-row">
                <div class="layui-col-md1 layui-col-xl-offset2" style="">
                    <button class="layui-btn add-device-btn" type="button">立即提交</button>

                </div>
                <div class="layui-col-md1" style="">
                    <button class="layui-btn layui-btn-primary" type="reset">重置</button>
                </div>
            </div>
        </div>
    </form>
</div>
