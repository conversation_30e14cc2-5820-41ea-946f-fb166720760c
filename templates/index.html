<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>海南分厂报备系统后台管理</title>
    <!-- 先引入所有第三方库 -->
{#    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>#}
    <script src="http://************:8888/cloudstore/resource/pc/jquery/jquery.min.js?v=20200605"></script>
    <!-- 更换为SheetJS的官方CDN -->
    <script src="https://cdn.sheetjs.com/xlsx-0.20.1/package/dist/xlsx.full.min.js"></script>
    <script src="../static/js/echarts.js"></script>

    <!-- 然后是样式文件 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../static/css/style.css" rel="stylesheet">

    <!-- 最后引入你的应用代码 -->
    <script src="../static/js/app.js"></script>
    <!--  <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.2/dist/echarts.min.js"></script> -->

    <!-- 添加 ExcelJS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/4.3.0/exceljs.min.js"></script>
</head>

<body>
<!-- 侧边栏 -->
<div class="sidebar" id="sidebar">
    <h2 id="sidebar-title">
        <img alt="LOGO" src="../static/img/logo.png">
        后台管理
    </h2>
    <ul>
        <li><a data-page="dashboard" href="#dashboard">
            <i class="fas fa-tachometer-alt"></i>
            <span>仪表盘</span>
        </a></li>
        <li><a data-page="users" href="#users">
            <i class="fas fa-users"></i>
            <span>用户管理</span>
        </a></li>
        <li><a data-page="data" href="#data">
            <i class="fas fa-table"></i>
            <span>数据管理</span>
        </a></li>
        <li><a data-page="settings" href="#settings">
            <i class="fas fa-cog"></i>
            <span>设置</span>
        </a></li>
        <li><a data-page="help" href="#help">
            <i class="fas fa-question-circle"></i>
            <span>帮助</span>
        </a></li>
    </ul>
</div>

<!-- 顶部导航栏 -->
<div class="topbar" id="topbar">
    <div class="menu-toggle" id="menu-toggle">&#9776;</div>
    <div class="search-container" style="position: relative;">
        <input class="search-box" placeholder="信息搜索..." type="text">
    </div>
    <div class="user-info">
        <img alt="用户头像" src="../static/img/logo-collapsed.png">
        <span>管理员</span>
    </div>
</div>

<!-- 内容区域 -->
<div class="content" id="content">
    <div class="card" id="dashboard-page">
        <div class="charts-container">
            <div class="chart-card">
                <h3>用户增长趋势</h3>
                <div class="chart" id="userGrowthChart" style="width: 100%; height: 400px;"></div>
            </div>
            <div class="chart-card">
                <h3>报备类型分布</h3>
                <div class="chart" id="reportTypeChart" style="width: 100%; height: 400px;"></div>
            </div>
        </div>
    </div>
    <div class="card" id="users-page">
        <div class="table-container">
            <table class="data-table" id="users-table">
                <thead>
                <tr>
                    <th>用户名</th>
                    <th>OPENID</th>
                    <th>推送UID</th>
                    <th>注册时间</th>
                    <th>留餐推送</th>
                    <th>用车推送</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>

                </tbody>
            </table>
        </div>
    </div>
    <div class="card" id="orders-page" style="display: none;">
        <div class="filter-bar tabs">
            <button class="active" data-type="meal"><i class="fas fa-utensils"></i>留餐报备</button>
            <button data-type="vehicle"><i class="fas fa-car"></i>用车报备</button>
        </div>
    </div>
    <div class="card" id="settings-page" style="display: none;">
        <p>这里是设置页面，您可以配置系统参数。</p>
    </div>
    <div class="card" id="help-page" style="display: none;">
        <p>这里是帮助页面，您可以查看使用文档。</p>
    </div>
    <div class="card" id="data-page" style="display: none;">
        <div class="filter-bar tabs">
            <button class="active" data-type="meal"><i class="fas fa-utensils"></i>留餐数据</button>
            <button data-type="vehicle"><i class="fas fa-car"></i>用车数据</button>

        </div>

        <!-- 留餐数据表格 -->
        <div class="table-container meal-table">
            <table class="data-table meal-table" id="meal-data-table">
                <thead>
                <tr>
                    <th>申请人</th>
                    <th>留餐人数</th>
                    <th>留餐时间</th>
                    <th>提交时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <!-- 数据动态加载 -->
                </tbody>
            </table>
        </div>

        <!-- 用车数据表格 -->
        <div class="table-container vehicle-table" style="display:none">
            <table class="data-table car-table" id="vehicle-data-table">
                <thead>
                <tr>
                    <th>申请人</th>
                    <th>用车人数</th>
                    <th>用车地点</th>
                    <th>用车事由</th>
                    <th>用车时间</th>
                    <th>提交时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <!-- 数据动态加载 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 在body结束前添加编辑模态框 -->
<div class="modal" id="editModal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>编辑用户</h3>
        <form id="userForm">
            <div class="form-group">
                <label>用户名</label>
                <input id="editUsername" required type="text">
            </div>
            <div class="form-group">
                <label>角色</label>
                <select id="editRole">
                    <option value="管理员">管理员</option>
                    <option value="用户">用户</option>
                </select>
            </div>
            <div class="form-group">
                <label>用户组</label>
                <select id="editGroup">
                    <option value="admin">管理组</option>
                    <option value="vip">VIP用户</option>
                    <option value="normal">普通用户</option>
                </select>
            </div>
            <div class="form-group">
                <label>状态</label>
                <select id="editStatus">
                    <option value="active">已激活</option>
                    <option value="inactive">未激活</option>
                </select>
            </div>
            <button class="btn save-btn" type="submit">保存更改</button>
        </form>
    </div>
</div>

</body>

</html>