<!DOCTYPE html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>智能洞库厂内试验</title>
    <!-- 请勿在项目正式环境中引用该 layui.css 地址 -->
    {#  <link href="../util/layui/css/layui.css" rel="stylesheet">#}
    <link href="/static/css/layui.css" rel="stylesheet" media="all">
    <script src="/static/js/layui.js"></script>
    <script src="/static/js/jquery.js"></script>
    {#    <script type="module" src="/static/js/crane.js"></script>#}
    {#    <script src="/static/js/home.js"></script>#}

    <style>
        .container {
            position: relative;
            width: 800px;
            height: 500px;
            margin: 30px auto;
        }

        .background-img {
            width: 150%;
            height: 150%;
            background-image: url("/static/img/layout.png");
            background-size: cover;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
        }

        .icon-container {
            position: absolute;
            cursor: pointer;
            transition: all 0.3s;
        }

        .icon-container:hover {
            transform: scale(1.2);
        }

        .icon-container:hover .hover-box {
            display: block;
        }

        .hover-box {
            display: none;
            position: absolute;
            left: 50%;
            bottom: 100%;
            transform: translate(-50%);
            width: 200px;
            padding: 15px;
            background-color: rgba(0, 0, 0, 0.7);
            color: #fff;
            border-radius: 4px;
            margin-bottom: 10px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

        }

        .hover-box:after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translate(-50%);
            border-width: 8px;
            border-style: solid;
            border-color: rgba(0, 0, 0, 0.7) transparent transparent transparent;
        }

        .hover-box h3 {
            margin-bottom: 14px;
            font-size: 16px;
        }

        .hover-box p {
            font-size: 14px;
            line-height: 1.6;
            margin: 0;
        }


    </style>

    <link href="/static/css/lay.css" rel="stylesheet" media="all">
</head>
<body>
{#顶部导航栏#}
{% include 'top_nav.html' %}

<div class="layui-main" style="
        {#margin-left: 240.5;#}
        margin-left: 180px;
        ">

{#    <div class="img1" style="border-color: #ff5722; height: 300px;width: 200px">#}

    </div>

    <div class="container">
        <div class="background-img">

        </div>

        <div class="icon-container " id="icon-crane" style="top: 29%; left: 67%">
            <i class="layui-icon layui-icon-engine" style="font-size: 25px; color: #00ffff"></i>
            <div class="hover-box">
                <h3>起重机</h3>
                <p>IP：***********</p>
            </div>
        </div>


        <div class="icon-container " id="icon-warehouse" style="top: 65%; left: 99%">
            <i class="layui-icon layui-icon-template-1" style="font-size: 25px; color: #00ff00"></i>
            <div class="hover-box">
                <h3>仓库</h3>
                <p>库存管理</p>
            </div>
        </div>

        <div class="icon-container " id="icon-door" style="top: 80%; left: 54%">
            <i class="layui-icon layui-icon-auz" style="font-size: 25px; color: #ff0000"></i>
            <div class="hover-box">
                <h3>保密部位</h3>
                <p>进入需走流申请进入</p>
            </div>
        </div>

        <div class="icon-container" id="icon-TH" style="top: 115%; left: 78%">
            <i class="layui-icon layui-icon-water" style="font-size: 25px; color: #ff00ff"></i>

            <div class="hover-box">
                <h3>空调监控</h3>
                <p>温湿度、空调状态、人员状态监控</p>
            </div>
        </div>

        <div class="icon-container " id="icon-video" style="top: 102%; left: 56%">
            <i class="layui-icon layui-icon-video" style="font-size: 25px; color: #ce8483"></i>
            <div class="hover-box">
                <h3>视频监控</h3>
                {#                <p>123456789</p>#}
            </div>
        </div>
    </div>
</div>

<script>
    $(".icon-container").click(function () {
        console.log(this.id)
        if(this.id === 'icon-video'){
            layer.msg('当前模块还在开发中')
            return
        }

        window.location.href = '/web/home?to=' + this.id

    })


</script>

</body>
