<div class="layui-fluid">
    <div class="layui-row">
        {#                左侧导航栏#}
        <div class="layui-col-md1">


            {#                        style="width: 100%; color: black; background-color: white; #}
            <ul class="layui-nav layui-nav-tree layui-bg-gray left-nav" lay-filter="left-nav"
                style="width: 100%; height: 100%">
                {#                            <li class="layui-nav-item m-left-nav layui-this " id="base-TS">#}
                {#                                <a>基本态势</a>#}
                {#                            </li>#}
                <li class="layui-nav-item m-left-nav " id="pos-list">
                    <a>点位列表</a>
                </li>
                <li class="layui-nav-item m-left-nav" id="add-pos">
                    <a>新增点位</a>
                </li>
                <li class="layui-nav-item m-left-nav" id="his-data">
                    <a>历史数据</a>
                </li>
                <li class="layui-nav-item m-left-nav" id="bas-set">
                    <a>基本设置</a>
                </li>
            </ul>
        </div>
        {#右侧数据栏目#}
        <div class="layui-col-md11">
            {#新的Tab页面#}
            <div class="layui-tab scrollable-tab" lay-filter="l-tab" lay-allowClose="true">
                <ul class="layui-tab-title tab-title-li">
                    <li class="layui-this " id="base-TS-li" lay-allowClose="false">基本态势</li>
                </ul>
                <div class="layui-tab-content data-tab" id="l-tab-page" style="">
                    <div class="layui-tab-item layui-show" style="" id="base-TS-li-content">
                        <div class="layui-fluid tab-child">
                            <div class="layui-row" id="base-TS-content">
                                <div class="layui-col-md2 tab-input" style="">
                                    <label class="layui-form-label input-label search-label">名称搜索:</label>
                                    <input type="text" name="TS-search-name" id="TS-search-name"
                                           class="layui-input search-input"
                                           placeholder="名称搜索" style="">
                                </div>
                                <div class="layui-col-md2 tab-input" style="">
                                    <label class="layui-form-label input-label search-label">IP搜索:</label>
                                    <input type="text" name="TS-search-ip" id="TS-search-ip"
                                           class="layui-input search-input"
                                           placeholder="IP搜索" style="">
                                </div>
                                <div class="layui-col-md2" style="">
                                    <div class="layui-form">
                                        <div class="layui-form-item tab-layui-form-item" style="">
                                            <label class="layui-form-label input-label search-label">部门搜索:</label>
                                            <div class="layui-input-inline">
                                                <select name="TS-search-department"
                                                        id="TS-search-department"
                                                        class=""
                                                        style="padding-right: 10px">
                                                    <option value=""></option>
                                                    {% for i in department %}
                                                        <option value="{{ i }}">{{ i }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {#                                            <div class="layui-col-md2 tab-input" style="">#}
                                {#                                                <label class="layui-form-label input-label search-label">点位搜索:</label>#}
                                {#                                                <input type="text" name="TS-search-position" id="TS-search-position"#}
                                {#                                                       class="layui-input search-input"#}
                                {#                                                       placeholder="IP搜索" style="">#}
                                {#                                            </div>#}

                                <div class="layui-col-md2" style="">
                                    <div class="layui-form">
                                        <div class="layui-form-item tab-layui-form-item" style="">
                                            <label class="layui-form-label input-label search-label">点位搜索:</label>
                                            <div class="layui-input-inline">
                                                <select name="TS-search-position" id="TS-search-position"
                                                        class=""
                                                        style="padding-right: 10px">
                                                    <option value=""></option>
                                                    {% for i in position %}
                                                        {% for key, value in i.items() %}
                                                            <option value="{{ key }}">{{ value }}</option>
                                                        {% endfor %}
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-col-md2" style="">
                                    <div class="layui-form">
                                        <div class="layui-form-item tab-layui-form-item" style="">
                                            <label class="layui-form-label input-label search-label">状态搜索:</label>
                                            <div class="layui-input-inline">
                                                <select name="TS-search-state" id="TS-search-state"
                                                        class="">
                                                    <option value=""></option>
                                                    {% for i in state %}
                                                        <option value="{{ i }}">{{ i }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md2 search-tool-btn" style="">
                                    <button type="button" class="layui-btn TS-search-btn"
                                            id="TS-search-btn">搜索
                                    </button>
                                    <button type="button" class="layui-btn TS-refresh-btn"
                                            id="TS-refresh-btn">
                                        刷新
                                    </button>
                                </div>
                            </div>
                            <div class="card-page">
                            </div>
                            <div id="card_page_index"></div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>