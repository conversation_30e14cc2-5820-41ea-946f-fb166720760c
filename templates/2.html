{#<!DOCTYPE html>#}
{#<html>#}
{#<head>#}
{#  <meta charset="utf-8">#}
{#  <title>工作审批流进度</title>#}
{#  <link rel="stylesheet" href="/static/css/layui.css">#}
<style>
    .approval-container {
        margin: 20px;
        background: #fff;
        border-radius: 5px;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
    }

    .approval-header {
        padding: 15px 20px;
        border-bottom: 1px solid #f6f6f6;
    }

    .approval-title {
        font-size: 18px;
        font-weight: 500;
        color: #333;
    }

    .approval-body {
        padding: 20px;
    }

    .approval-flow {
        margin: 30px 0;
        position: relative;
    }

    .flow-line {
        position: absolute;
        left: 60px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e8e8e8;
        z-index: 1;
    }

    .flow-line-progress {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 0;
        background: #1E9FFF;
        transition: all .3s;
        z-index: 2;
    }

    .flow-step {
        position: relative;
        margin-bottom: 30px;
        padding-left: 90px;
        min-height: 60px;
        z-index: 3;
    }

    .step-icon {
        position: absolute;
        left: 30px;
        top: 0;
        width: 60px;
        height: 60px;
        line-height: 60px;
        text-align: center;
        border-radius: 50%;
        background: #fff;
        border: 2px solid #e8e8e8;
        color: #999;
        font-size: 24px;
    }

    .step-icon.active {
        border-color: #1E9FFF;
        color: #1E9FFF;
    }

    .step-icon.completed {
        border-color: #5FB878;
        color: #5FB878;
    }

    .step-icon.rejected {
        border-color: #FF5722;
        color: #FF5722;
    }

    .step-content {
        padding: 10px 15px;
        background: #f8f8f8;
        border-radius: 4px;
        position: relative;
    }

    .step-content:before {
        content: '';
        position: absolute;
        left: -8px;
        top: 20px;
        width: 0;
        height: 0;
        border-top: 8px solid transparent;
        border-right: 8px solid #f8f8f8;
        border-bottom: 8px solid transparent;
    }

    .step-title {
        font-size: 16px;
        margin-bottom: 5px;
        font-weight: 500;
    }

    .step-desc {
        color: #666;
        font-size: 14px;
        margin-bottom: 5px;
    }

    .step-time {
        color: #999;
        font-size: 12px;
    }

    .step-actions {
        margin-top: 10px;
    }

    .approval-files {
        margin-top: 20px;
    }

    .file-item {
        display: inline-block;
        margin-right: 15px;
        margin-bottom: 10px;
    }

    .approval-history {
        margin-top: 30px;
    }

    .history-title {
        font-size: 16px;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

    .history-item {
        padding: 10px 15px;
        margin-bottom: 10px;
        background: #f8f8f8;
        border-radius: 4px;
        position: relative;
    }

    .history-item:before {
        content: '';
        position: absolute;
        left: -8px;
        top: 15px;
        width: 0;
        height: 0;
        border-top: 8px solid transparent;
        border-right: 8px solid #f8f8f8;
        border-bottom: 8px solid transparent;
    }

    .history-action {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 2px;
        font-size: 12px;
        margin-right: 10px;
    }

    .action-approve {
        background: #5FB878;
        color: #fff;
    }

    .action-reject {
        background: #FF5722;
        color: #fff;
    }

    .action-submit {
        background: #1E9FFF;
        color: #fff;
    }

    .action-comment {
        background: #FFB800;
        color: #fff;
    }

    .history-content {
        display: inline-block;
    }

    .history-time {
        float: right;
        color: #999;
    }

    .approval-footer {
        padding: 15px 20px;
        border-top: 1px solid #f6f6f6;
        text-align: right;
    }
</style>
{#</head>#}
{#<body>#}


<div class="layui-fluid">
    <div class="layui-row">
        {#                左侧导航栏#}
        <div class="layui-col-md1">


            {#                        style="width: 100%; color: black; background-color: white; #}
            <ul class="layui-nav layui-nav-tree layui-bg-gray left-nav" lay-filter="left-nav"
                style="width: 100%; height: 100%">
                {#                            <li class="layui-nav-item m-left-nav layui-this " id="base-TS">#}
                {#                                <a>基本态势</a>#}
                {#                            </li>#}
                <li class="layui-nav-item m-left-nav" id="d">
                    <a>保密点位申请</a>
                </li>
                <li class="layui-nav-item m-left-nav " id="a">
                    <a>保密点位列表</a>
                </li>
                <li class="layui-nav-item m-left-nav" id="b">
                    <a>新增保密点位</a>
                </li>
                <li class="layui-nav-item m-left-nav" id="c">
                    <a>点位历史数据</a>
                </li>

            </ul>
        </div>
        {#右侧数据栏目#}
        <div class="layui-col-md11">
            {#新的Tab页面#}
            <div class="layui-tab scrollable-tab" lay-filter="e" lay-allowClose="true">
                <ul class="layui-tab-title tab-title-li">
                    <li class="layui-this " id="f" lay-allowClose="false">保密点位申请单</li>
                </ul>
                <div class="layui-tab-content data-tab" id="g" style="">
                    <div class="layui-tab-item layui-show" style="" id="h">
                        <div class="layui-fluid tab-child">
                            <div class="layui-fluid">
                                <div class="approval-container">
                                    <div class="approval-header">
                                        <h1 class="approval-title">申请审批</h1>
                                        <div>
                                            <span class="layui-badge">申请编号: 20230001</span>
                                            <span class="layui-badge layui-bg-blue">申请人: 李埴烺</span>
                                            <span class="layui-badge layui-bg-orange">部门: 技术处</span>
                                            <span class="layui-badge layui-bg-cyan">申请时间: 2023-05-10</span>
                                        </div>
                                    </div>

                                    <div class="approval-body">
                                        <div class="layui-row">
                                            <div class="layui-col-md8">
                                                <fieldset class="layui-elem-field">
                                                    <legend>申请详情</legend>
                                                    <div class="layui-field-box">
                                                        <table class="layui-table">
                                                            <colgroup>
                                                                <col width="100">
                                                                <col>
                                                            </colgroup>
                                                            <tbody>
                                                            <tr>
                                                                <td>审批类型</td>
                                                                <td>申请进入</td>
                                                            </tr>
                                                            <tr>
                                                                <td>申请部位</td>
                                                                <td>XXXX</td>
                                                            </tr>
                                                            <tr>
                                                                <td>申请时间</td>
                                                                <td>2023-05-15 至 2023-05-17 (共3天)</td>
                                                            </tr>

                                                            <tr>
                                                                <td>申请事由</td>
                                                                <td>开展XXXXXX工作</td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </fieldset>
                                            </div>
                                            <div class="layui-col-md4">
                                                <fieldset class="layui-elem-field" style="height: 238px">
                                                    <legend>当前状态</legend>
                                                    <div class="layui-field-box">
                                                        <div style="text-align: center; padding: 20px 0;">
                                                            <div style="font-size: 24px; margin-bottom: 10px;">
                                    <span class="layui-badge layui-bg-orange"
                                          style="font-size: 16px; padding: 5px 15px;">审批中</span>
                                                            </div>
                                                            <div style="color: #666; margin-bottom: 15px;">
                                                                当前审批人: <span style="color: #333;">李四 (技术部经理)</span>
                                                            </div>
                                                            <div class="layui-progress layui-progress-big"
                                                                 lay-filter="progress">
                                                                <div class="layui-progress-bar" lay-percent="50%"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </fieldset>
                                            </div>
                                        </div>

                                        <div class="approval-flow">
                                            <div class="flow-line">
                                                <div class="flow-line-progress" style="height: 50%;"></div>
                                            </div>

                                            <!-- 步骤1 -->
                                            <div class="flow-step">
                                                <div class="step-icon completed">
                                                    <i class="layui-icon">&#xe605;</i>
                                                </div>
                                                <div class="step-content">
                                                    <div class="step-title">提交申请</div>
                                                    <div class="step-desc">申请人: 张三</div>
                                                    <div class="step-time">2023-05-10 09:30:25</div>
                                                </div>
                                            </div>

                                            <!-- 步骤2 -->
                                            <div class="flow-step">
                                                <div class="step-icon completed">
                                                    <i class="layui-icon">&#xe63c;</i>
                                                </div>
                                                <div class="step-content">
                                                    <div class="step-title">部门主管审批</div>
                                                    <div class="step-desc">审批人: 王五 (技术部主管)</div>
                                                    <div class="step-desc">审批意见: 同意，请部门经理审批</div>
                                                    <div class="step-time">2023-05-10 14:15:36</div>
                                                </div>
                                            </div>

                                            <!-- 步骤3 -->
                                            <div class="flow-step">
                                                <div class="step-icon active">
                                                    <i class="layui-icon">&#xe63c;</i>
                                                </div>
                                                <div class="step-content">
                                                    <div class="step-title">部门经理审批</div>
                                                    <div class="step-desc">审批人: 李四 (技术部经理)</div>
                                                    <div class="step-actions">
                                                        <button class="layui-btn layui-btn-sm layui-btn-normal">同意
                                                        </button>
                                                        <button class="layui-btn layui-btn-sm layui-btn-danger">拒绝
                                                        </button>
                                                        <button class="layui-btn layui-btn-sm">转审</button>
                                                        <button class="layui-btn layui-btn-sm layui-btn-primary">添加意见
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 步骤4 -->
                                            <div class="flow-step">
                                                <div class="step-icon">
                                                    <i class="layui-icon ">&#xe616;</i>
                                                </div>
                                                <div class="step-content">
                                                    <div class="step-title">人事备案</div>
                                                    <div class="step-desc">处理人: 人事部</div>
                                                </div>
                                            </div>

                                            <!-- 步骤5 -->
                                            <div class="flow-step">
                                                <div class="step-icon">
                                                    <i class="layui-icon layui-icon-ok"></i>
                                                </div>
                                                <div class="step-content">
                                                    <div class="step-title">完成</div>
                                                </div>
                                            </div>
                                        </div>

{#                                        <div class="approval-files">#}
{#                                            <fieldset class="layui-elem-field">#}
{#                                                <legend>附件材料</legend>#}
{#                                                <div class="layui-field-box">#}
{#                                                    <div class="file-item">#}
{#                                                        <a href="#" class="layui-btn layui-btn-primary layui-btn-xs">#}
{#                                                            <i class="layui-icon">&#xe61e;</i> 请假申请表.pdf#}
{#                                                        </a>#}
{#                                                    </div>#}
{#                                                    <div class="file-item">#}
{#                                                        <a href="#" class="layui-btn layui-btn-primary layui-btn-xs">#}
{#                                                            <i class="layui-icon">&#xe61e;</i> 证明材料.jpg#}
{#                                                        </a>#}
{#                                                    </div>#}
{#                                                </div>#}
{#                                            </fieldset>#}
{#                                        </div>#}

{#                                        <div class="approval-history">#}
{#                                            <div class="history-title">审批历史记录</div>#}
{##}
{#                                            <div class="history-item">#}
{#                                                <span class="history-action action-submit">提交</span>#}
{#                                                <span class="history-content">张三 提交了申请</span>#}
{#                                                <span class="history-time">2023-05-10 09:30:25</span>#}
{#                                            </div>#}
{##}
{#                                            <div class="history-item">#}
{#                                                <span class="history-action action-comment">意见</span>#}
{#                                                <span class="history-content">王五: 请假事由合理，同意申请</span>#}
{#                                                <span class="history-time">2023-05-10 14:15:36</span>#}
{#                                            </div>#}
{##}
{#                                            <div class="history-item">#}
{#                                                <span class="history-action action-approve">同意</span>#}
{#                                                <span class="history-content">王五 同意了申请</span>#}
{#                                                <span class="history-time">2023-05-10 14:15:40</span>#}
{#                                            </div>#}
{#                                        </div>#}
                                    </div>

                                    <div class="approval-footer">
                                        <button class="layui-btn layui-btn-primary">返回列表</button>
                                        <button class="layui-btn">打印申请</button>
                                        <button class="layui-btn layui-btn-danger">撤销申请</button>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{#  <script src="http://127.0.0.1:5000/static/js/layui.js"></script>#}
<script>
    layui.use(['element', 'form'], function () {
        var element = layui.element;
        var form = layui.form;

        // 渲染进度条
        element.progress('progress', '50%');

        // 模拟审批操作
        $('.step-actions button').on('click', function () {
            var type = $(this).text();
            if (type === '同意' || type === '拒绝') {
                layer.open({
                    type: 1,
                    title: type + '申请',
                    area: ['500px', '300px'],
                    content: '<div style="padding: 20px;">' +
                        '<div class="layui-form-item layui-form-text">' +
                        '<label class="layui-form-label">审批意见</label>' +
                        '<div class="layui-input-block">' +
                        '<textarea placeholder="请输入审批意见" class="layui-textarea"></textarea>' +
                        '</div></div></div>',
                    btn: ['确认', '取消'],
                    yes: function (index, layero) {
                        var remark = layero.find('textarea').val();
                        layer.msg(type + '成功', {icon: 1});
                        layer.close(index);

                        // 模拟更新UI
                        if (type === '同意') {
                            $('.flow-line-progress').css('height', '75%');
                            element.progress('progress', '75%');
                            $('.flow-step:nth-child(3) .step-icon').removeClass('active').addClass('completed');
                            $('.flow-step:nth-child(4) .step-icon').addClass('active');

                            // 添加历史记录
                            var historyItem = '<div class="history-item">' +
                                '<span class="history-action action-approve">同意</span>' +
                                '<span class="history-content">李四 同意了申请</span>' +
                                '<span class="history-time">' + new Date().format('yyyy-MM-dd hh:mm:ss') + '</span>' +
                                '</div>';
                            if (remark) {
                                historyItem = '<div class="history-item">' +
                                    '<span class="history-action action-comment">意见</span>' +
                                    '<span class="history-content">李四: ' + remark + '</span>' +
                                    '<span class="history-time">' + new Date().format('yyyy-MM-dd hh:mm:ss') + '</span>' +
                                    '</div>' + historyItem;
                            }
                            $('.approval-history').append(historyItem);

                            // 更新当前审批人
                            $('.step-actions').html('<div class="step-desc">审批意见: ' + (remark || '无') + '</div>');
                        }
                    }
                });
            } else if (type === '添加意见') {
                layer.prompt({
                    title: '添加审批意见',
                    formType: 2
                }, function (text, index) {
                    layer.close(index);

                    // 添加历史记录
                    var historyItem = '<div class="history-item">' +
                        '<span class="history-action action-comment">意见</span>' +
                        '<span class="history-content">李四: ' + text + '</span>' +
                        '<span class="history-time">' + new Date().format('yyyy-MM-dd hh:mm:ss') + '</span>' +
                        '</div>';
                    $('.approval-history').prepend(historyItem);
                });
            }
        });

        // 日期格式化
        Date.prototype.format = function (fmt) {
            var o = {
                "M+": this.getMonth() + 1,
                "d+": this.getDate(),
                "h+": this.getHours(),
                "m+": this.getMinutes(),
                "s+": this.getSeconds(),
                "q+": Math.floor((this.getMonth() + 3) / 3),
                "S": this.getMilliseconds()
            };
            if (/(y+)/.test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
            }
            for (var k in o) {
                if (new RegExp("(" + k + ")").test(fmt)) {
                    fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
                }
            }
            return fmt;
        };
    });
</script>
{#</body>#}
{#</html>#}