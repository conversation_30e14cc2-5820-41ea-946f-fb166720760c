<div class="layui-fluid">
    <div class="layui-row">
        {#                左侧导航栏#}
        <div class="layui-col-md1">

            <ul class="layui-nav layui-nav-tree layui-bg-gray crane-left-nav" lay-filter="crane-left-nav"
                style="width: 100%; height: 100%">
                <li class="layui-nav-item crane-m-left-nav" id="crane-table">
                    <a>起重机列表</a>
                </li>
                <li class="layui-nav-item crane-m-left-nav" id="crane-add-pos">
                    <a>新增起重机</a>
                </li>
                <li class="layui-nav-item crane-m-left-nav" id="crane-his-data">
                    <a>历史数据</a>
                </li>
                <li class="layui-nav-item crane-m-left-nav" id="crane-bas-set">
                    <a>基本设置</a>
                </li>
            </ul>
        </div>
        {#右侧数据栏目#}
        <div class="layui-col-md11">
            {#新的Tab页面#}
            <div class="layui-tab scrollable-tab" lay-filter="crane-l-tab" lay-allowClose="true">
                <ul class="layui-tab-title crane-tab-title-li">
                    <li class="layui-this " id="crane-card" lay-allowClose="false">设备实况</li>
                </ul>
                <div class="layui-tab-content crane-data-tab" id="crane-l-tab-page" style="">

                    <div class="layui-tab-item layui-show" style="" id="crane-card-li-content">
                        <div class="layui-fluid tab-child">
                            <div class="layui-row" id="crane-card-content">
                                <div class="layui-col-md2 tab-input" style="">
                                    <label class="layui-form-label input-label search-label">名称搜索:</label>
                                    <input type="text" name="TS-search-name" id="crane-TS-search-name"
                                           class="layui-input search-input"
                                           placeholder="名称搜索" style="">
                                </div>
                                <div class="layui-col-md2 tab-input" style="">
                                    <label class="layui-form-label input-label search-label">IP搜索:</label>
                                    <input type="text" name="crane-TS-search-ip" id="crane-TS-search-ip"
                                           class="layui-input search-input"
                                           placeholder="IP搜索" style="">
                                </div>
                                <div class="layui-col-md2" style="">
                                    <div class="layui-form">
                                        <div class="layui-form-item tab-layui-form-item" style="">
                                            <label class="layui-form-label input-label search-label">部门搜索:</label>
                                            <div class="layui-input-inline">
                                                <select name="crane-TS-search-department"
                                                        id="crane-TS-search-department"
                                                        class=""
                                                        style="padding-right: 10px">
                                                    <option value=""></option>
                                                    {% for i in department %}
                                                        <option value="{{ i }}">{{ i }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md2" style="">
                                    <div class="layui-form">
                                        <div class="layui-form-item tab-layui-form-item" style="">
                                            <label class="layui-form-label input-label search-label">点位搜索:</label>
                                            <div class="layui-input-inline">
                                                <select name="crane-TS-search-position" id="crane-TS-search-position"
                                                        class=""
                                                        style="padding-right: 10px">
                                                    <option value=""></option>
                                                    {% for i in position %}
                                                        {% for key, value in i.items() %}
                                                            <option value="{{ key }}">{{ value }}</option>
                                                        {% endfor %}
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-col-md2" style="">
                                    <div class="layui-form">
                                        <div class="layui-form-item tab-layui-form-item" style="">
                                            <label class="layui-form-label input-label search-label">状态搜索:</label>
                                            <div class="layui-input-inline">
                                                <select name="crane-TS-search-state" id="crane-TS-search-state"
                                                        class="">
                                                    <option value=""></option>
                                                    {% for i in state %}
                                                        <option value="{{ i }}">{{ i }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md2 search-tool-btn" style="">
                                    <button type="button" class="layui-btn crane-TS-search-btn"
                                            id="TS-search-btn">搜索
                                    </button>
                                    <button type="button" class="layui-btn crane-TS-refresh-btn"
                                            id="TS-refresh-btn">
                                        刷新
                                    </button>
                                </div>
                            </div>
                            <hr>
                            <div class="crane-card-page">
                            </div>
                            <div id="crane_card_page_index"></div>

                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>


<div class="layui-tab-item " style="" id="crane-table-li-content">
    <div class="layui-fluid tab-child">
        <div class="layui-row" id="crane-table-content">
            <div class="layui-col-md2 tab-input" style="">
                <label class="layui-form-label input-label search-label">名称搜索:</label>
                <input type="text" name="TS-search-name" id="crane-TS-search-name"
                       class="layui-input search-input"
                       placeholder="名称搜索" style="">
            </div>
            <div class="layui-col-md2 tab-input" style="">
                <label class="layui-form-label input-label search-label">IP搜索:</label>
                <input type="text" name="crane-TS-search-ip" id="crane-TS-search-ip"
                       class="layui-input search-input"
                       placeholder="IP搜索" style="">
            </div>
            <div class="layui-col-md2" style="">
                <div class="layui-form">
                    <div class="layui-form-item tab-layui-form-item" style="">
                        <label class="layui-form-label input-label search-label">部门搜索:</label>
                        <div class="layui-input-inline">
                            <select name="crane-TS-search-department"
                                    id="crane-TS-search-department"
                                    class=""
                                    style="padding-right: 10px">
                                <option value=""></option>
                                {% for i in department %}
                                    <option value="{{ i }}">{{ i }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2" style="">
                <div class="layui-form">
                    <div class="layui-form-item tab-layui-form-item" style="">
                        <label class="layui-form-label input-label search-label">点位搜索:</label>
                        <div class="layui-input-inline">
                            <select name="crane-TS-search-position" id="crane-TS-search-position"
                                    class=""
                                    style="padding-right: 10px">
                                <option value=""></option>
                                {% for i in position %}
                                    {% for key, value in i.items() %}
                                        <option value="{{ key }}">{{ value }}</option>
                                    {% endfor %}
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md2" style="">
                <div class="layui-form">
                    <div class="layui-form-item tab-layui-form-item" style="">
                        <label class="layui-form-label input-label search-label">状态搜索:</label>
                        <div class="layui-input-inline">
                            <select name="crane-TS-search-state" id="crane-TS-search-state"
                                    class="">
                                <option value=""></option>
                                {% for i in state %}
                                    <option value="{{ i }}">{{ i }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2 search-tool-btn" style="">
                <button type="button" class="layui-btn crane-TS-search-btn"
                        id="TS-search-btn">搜索
                </button>
                <button type="button" class="layui-btn crane-TS-refresh-btn"
                        id="TS-refresh-btn">
                    刷新
                </button>
            </div>
        </div>
        <hr>
        <table id="crane" style="width: 100%;height: 100%"></table>
        <script>

        </script>
        <script id="op-btn" type="text/html">
            <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
        </script>

    </div>
</div>




