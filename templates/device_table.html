{#点位列表#}
<div class="layui-tab-item " style="display: none" id="pos-list-li-content">
    {#搜索栏#}
    <div class="layui-fluid tab-child">
        <div class="layui-row">
            <div class="layui-col-md2 tab-input" style="">
                <label class="layui-form-label input-label search-label">名称搜索:</label>
                <input type="text" name="search-name" id="search-name"
                       class="layui-input search-input"
                       placeholder="名称搜索" style="">
            </div>
            <div class="layui-col-md2 tab-input" style="">
                <label class="layui-form-label input-label search-label">IP搜索:</label>
                <input type="text" name="search-ip" id="search-ip"
                       class="layui-input search-input"
                       placeholder="IP搜索" style="">
            </div>
            <div class="layui-col-md2" style="">
                <div class="layui-form">
                    <div class="layui-form-item tab-layui-form-item" style="">
                        <label class="layui-form-label input-label search-label">部门搜索:</label>
                        <div class="layui-input-inline">
                            <select name="search-department" id="search-department" class=""
                                    style="padding-right: 10px">
                                <option value=""></option>
                                {% for i in department %}
                                    <option value="{{ i }}">{{ i }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2" style="">
                <div class="layui-form">
                    <div class="layui-form-item tab-layui-form-item" style="">
                        <label class="layui-form-label input-label search-label">状态搜索:</label>
                        <div class="layui-input-inline">
                            <select name="search-state" id="search-state" class="">
                                <option value=""></option>
                                {% for i in state %}
                                    <option value="{{ i }}">{{ i }}</option>
                                {% endfor %}
                            </select></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3 search-tool-btn table-search-tool-btn" style="">
                <button type="button" class="layui-btn search-btn " id="search-btn">搜索</button>
                <button type="button" class="layui-btn refresh-btn" id="refresh-btn">刷新</button>
            </div>
        </div>
    </div>
    <hr>
    {#数据表#}
    <table id="AirConditioner" style="width: 100%;height: 100%"></table>
    <script 123>
        {#layui.use('table', function () {#}
        {#    var AirCondition = layui.table;#}
        {#    AirCondition.render({#}
        {#        elem: '#AirConditioner',#}
        {#        id: 'AirConditioner',#}
        {#        height: 500,#}
        {#        url: '/device/AirConditionData',#}
        {#        limits: [10, 15, 20, 30, 50],#}
        {#        page: true,#}
        {#        cols: [[#}
        {#            {type: 'checkbox'},#}
        {#            {#}
        {#                field: 'device_name',#}
        {#                title: "名称",#}
        {#                width: 120,#}
        {#                sort: true,#}
        {#                align: 'center'#}
        {#            },#}
        {#            {#}
        {#                field: 'department',#}
        {#                title: "部门",#}
        {#                width: 120,#}
        {#                sort: false,#}
        {#                align: 'center'#}
        {#            },#}
        {#            {#}
        {#                field: 'position',#}
        {#                title: "点位",#}
        {#                width: 120,#}
        {#                sort: false,#}
        {#                align: 'center'#}
        {#            },#}
        {#            {#}
        {#                field: 'ip',#}
        {#                title: "IP",#}
        {#                width: 150,#}
        {#                sort: false,#}
        {#                align: 'center'#}
        {#            },#}
        {#            {#}
        {#                field: 'port',#}
        {#                title: "端口",#}
        {#                width: 80,#}
        {#                sort: false,#}
        {#                align: 'center'#}
        {#            },#}
        {#            {#}
        {#                field: 'state',#}
        {#                title: "状态",#}
        {#                width: 80,#}
        {#                sort: false,#}
        {#                align: 'center'#}
        {#            },#}
        {#            {#}
        {#                field: 'update_time',#}
        {#                title: "上次更新时间",#}
        {#                width: 150,#}
        {#                sort: true,#}
        {#                align: 'center'#}
        {#            },#}
        {#            {#}
        {#                field: 'creat_time',#}
        {#                title: "创建时间",#}
        {#                width: 150,#}
        {#                sort: true,#}
        {#                align: 'center'#}
        {#            },#}
        {#            {#}
        {#                field: 'creat_role',#}
        {#                title: "创建用户",#}
        {#                width: 120,#}
        {#                sort: true,#}
        {#                align: 'center'#}
        {#            },#}
        {#            {#}
        {#                field: 'operation',#}
        {#                title: "操作",#}
        {#                toolbar: '#op-btn',#}
        {#                width: 120,#}
        {#                sort: false, align: 'center'#}
        {#            },#}
        {#        ]],#}
        {#    })#}
        {#\});#}
    </script>
    <script id="op-btn" type="text/html">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    </script>
</div>