<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>空调检测数据时间线</title>
    <link rel="stylesheet" href="https://www.layuicdn.com/layui-v2.6.8/css/layui.css">
    <style>
        body {
            background-color: #f2f2f2;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .header h2 {
            color: #333;
            font-size: 24px;
        }
        .timeline-container {
            position: relative;
            padding: 40px 0 20px;
        }
        .timeline {
            position: relative;
            height: 4px;
            background-color: #e6e6e6;
            margin: 0 50px;
        }
        .timeline-progress {
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            background-color: #1E9FFF;
            width: 0;
            transition: width 0.3s;
        }
        .timeline-points {
            position: absolute;
            top: -8px;
            left: 0;
            right: 0;
        }
        .timeline-point {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #fff;
            border: 3px solid #1E9FFF;
            transform: translateX(-50%);
            cursor: pointer;
            transition: all 0.3s;
        }
        .timeline-point:hover {
            transform: translateX(-50%) scale(1.2);
            box-shadow: 0 0 10px rgba(30, 159, 255, 0.5);
        }
        .timeline-label {
            position: absolute;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            color: #666;
            white-space: nowrap;
        }
        .timeline-tooltip {
            position: absolute;
            bottom: 60px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #fff;
            padding: 15px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            width: 300px;
            display: none;
            z-index: 100;
        }
        .timeline-tooltip:after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            border-width: 10px 10px 0;
            border-style: solid;
            border-color: #fff transparent transparent;
        }
        .data-card {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f8f8;
            border-radius: 4px;
            display: none;
        }
        .data-card h3 {
            margin-bottom: 15px;
            color: #333;
        }
        .data-item {
            margin-bottom: 10px;
        }
        .data-label {
            display: inline-block;
            width: 120px;
            color: #666;
        }
        .data-value {
            color: #333;
            font-weight: bold;
        }
        .status-normal {
            color: #009688;
        }
        .status-warning {
            color: #FFB800;
        }
        .status-danger {
            color: #FF5722;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>空调检测数据时间线</h2>
        </div>
        
        <div class="timeline-container">
            <div class="timeline">
                <div class="timeline-progress"></div>
                <div class="timeline-points" id="timelinePoints"></div>
            </div>
            <div class="timeline-tooltip" id="timelineTooltip"></div>
        </div>
        
        <div class="data-card" id="dataCard">
            <h3>详细检测数据</h3>
            <div class="data-item">
                <span class="data-label">检测时间:</span>
                <span class="data-value" id="timeValue"></span>
            </div>
            <div class="data-item">
                <span class="data-label">温度:</span>
                <span class="data-value" id="tempValue"></span>
            </div>
            <div class="data-item">
                <span class="data-label">湿度:</span>
                <span class="data-value" id="humidityValue"></span>
            </div>
            <div class="data-item">
                <span class="data-label">风速:</span>
                <span class="data-value" id="windSpeedValue"></span>
            </div>
            <div class="data-item">
                <span class="data-label">运行状态:</span>
                <span class="data-value" id="statusValue"></span>
            </div>
            <div class="data-item">
                <span class="data-label">能耗:</span>
                <span class="data-value" id="energyValue"></span>
            </div>
        </div>
    </div>

    <script src="https://www.layuicdn.com/layui-v2.6.8/layui.js"></script>
    <script>
        layui.use(['jquery', 'layer'], function(){
            var $ = layui.jquery;
            var layer = layui.layer;
            
            // 模拟数据
            var timelineData = [
                {
                    time: "2023-05-01 08:00",
                    displayTime: "08:00",
                    temperature: "24.5℃",
                    humidity: "45%",
                    windSpeed: "低风",
                    status: "正常",
                    energy: "1.2kW·h",
                    statusClass: "status-normal"
                },
                {
                    time: "2023-05-01 10:30",
                    displayTime: "10:30",
                    temperature: "26.1℃",
                    humidity: "50%",
                    windSpeed: "中风",
                    status: "正常",
                    energy: "1.5kW·h",
                    statusClass: "status-normal"
                },
                {
                    time: "2023-05-01 12:45",
                    displayTime: "12:45",
                    temperature: "28.7℃",
                    humidity: "55%",
                    windSpeed: "高风",
                    status: "高温警告",
                    energy: "2.1kW·h",
                    statusClass: "status-warning"
                },
                {
                    time: "2023-05-01 15:20",
                    displayTime: "15:20",
                    temperature: "22.3℃",
                    humidity: "40%",
                    windSpeed: "低风",
                    status: "正常",
                    energy: "1.0kW·h",
                    statusClass: "status-normal"
                },
                {
                    time: "2023-05-01 18:00",
                    displayTime: "18:00",
                    temperature: "30.2℃",
                    humidity: "60%",
                    windSpeed: "高风",
                    status: "高温警报",
                    energy: "2.5kW·h",
                    statusClass: "status-danger"
                }
            ];
            
            // 初始化时间线
            function initTimeline() {
                var $timeline = $('.timeline');
                var $timelinePoints = $('#timelinePoints');
                var $timelineTooltip = $('#timelineTooltip');
                var $dataCard = $('#dataCard');
                
                var timelineWidth = $timeline.width();
                var pointCount = timelineData.length;
                
                // 创建时间点
                timelineData.forEach(function(data, index) {
                    var position = (index / (pointCount - 1)) * 100;
                    
                    // 创建时间点
                    var $point = $('<div class="timeline-point"></div>')
                        .css('left', position + '%')
                        .attr('data-index', index);
                    
                    // 创建时间标签
                    var $label = $('<div class="timeline-label">' + data.displayTime + '</div>')
                        .css('left', position + '%');
                    
                    $point.on('mouseenter', function() {
                        var $this = $(this);
                        var index = $this.attr('data-index');
                        var data = timelineData[index];
                        
                        // 更新进度条
                        $('.timeline-progress').css('width', position + '%');
                        
                        // 显示工具提示
                        var tooltipHtml = '<strong>' + data.time + '</strong><br>' + 
                                           '温度: ' + data.temperature + '<br>' +
                                           '状态: <span class="' + data.statusClass + '">' + data.status + '</span>';
                        $timelineTooltip.html(tooltipHtml)
                            .css('left', position + '%')
                            .fadeIn(200);
                        
                        // 更新数据卡片
                        $('#timeValue').text(data.time);
                        $('#tempValue').text(data.temperature);
                        $('#humidityValue').text(data.humidity);
                        $('#windSpeedValue').text(data.windSpeed);
                        $('#statusValue').removeClass().addClass(data.statusClass).text(data.status);
                        $('#energyValue').text(data.energy);
                        
                        $dataCard.fadeIn(300);
                    });
                    
                    $point.on('mouseleave', function() {
                        $timelineTooltip.fadeOut(200);
                    });
                    
                    $timelinePoints.append($point);
                    $timelinePoints.append($label);
                });
                
                // 默认显示第一个点数据
                if (timelineData.length > 0) {
                    var firstData = timelineData[0];
                    $('.timeline-progress').css('width', '0%');
                    
                    $('#timeValue').text(firstData.time);
                    $('#tempValue').text(firstData.temperature);
                    $('#humidityValue').text(firstData.humidity);
                    $('#windSpeedValue').text(firstData.windSpeed);
                    $('#statusValue').removeClass().addClass(firstData.statusClass).text(firstData.status);
                    $('#energyValue').text(firstData.energy);
                    
                    $dataCard.show();
                }
            }
            
            // 页面加载完成后初始化时间线
            $(document).ready(function() {
                initTimeline();
            });
        });
    </script>
</body>
</html>