2025-03-14 14:40:00,013 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-03-14 14:40:00,013 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-03-14 14:40:00,016 - werkzeug - INFO -  * Restarting with stat
2025-03-14 14:40:02,818 - werkzeug - WARNING -  * Debugger is active!
2025-03-14 14:40:02,849 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-14 14:40:23,583 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 14:40:23] "POST /login HTTP/1.1" 200 -
2025-03-14 14:42:15,955 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-03-14 14:42:15,956 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-03-14 14:42:15,958 - werkzeug - INFO -  * Restarting with stat
2025-03-14 14:42:18,734 - werkzeug - WARNING -  * Debugger is active!
2025-03-14 14:42:18,760 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-14 14:43:35,956 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 14:43:35] "POST /login HTTP/1.1" 200 -
2025-03-14 14:44:41,674 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 14:44:41] "POST /login HTTP/1.1" 200 -
2025-03-14 14:45:37,658 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 14:45:37] "POST /login HTTP/1.1" 200 -
2025-03-14 14:46:27,584 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 14:46:27] "POST /login HTTP/1.1" 200 -
2025-03-14 14:46:42,648 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 14:46:42] "POST /login HTTP/1.1" 200 -
2025-03-14 14:51:38,002 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 14:51:38] "POST /login HTTP/1.1" 200 -
2025-03-14 14:51:45,140 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 14:51:45] "POST /login HTTP/1.1" 200 -
2025-03-14 14:56:09,195 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-14 14:56:09,309 - werkzeug - INFO -  * Restarting with stat
2025-03-14 14:56:12,210 - werkzeug - WARNING -  * Debugger is active!
2025-03-14 14:56:12,235 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-14 15:00:34,616 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-14 15:00:34,725 - werkzeug - INFO -  * Restarting with stat
2025-03-14 15:00:37,497 - werkzeug - WARNING -  * Debugger is active!
2025-03-14 15:00:37,526 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-14 15:04:25,813 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 15:04:25] "POST /login HTTP/1.1" 200 -
2025-03-14 15:05:00,544 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 15:05:00] "POST /login HTTP/1.1" 200 -
2025-03-14 15:32:29,199 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 15:32:29] "[35m[1mPOST /login HTTP/1.1[0m" 500 -
2025-03-14 15:32:57,707 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 15:32:57] "[35m[1mPOST /login HTTP/1.1[0m" 500 -
2025-03-14 15:34:52,427 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 15:34:52] "POST /login HTTP/1.1" 200 -
2025-03-14 15:41:30,721 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-14 15:41:30,832 - werkzeug - INFO -  * Restarting with stat
2025-03-14 15:41:33,765 - werkzeug - WARNING -  * Debugger is active!
2025-03-14 15:41:33,792 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-14 15:47:30,608 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-14 15:47:30,706 - werkzeug - INFO -  * Restarting with stat
2025-03-14 15:47:33,592 - werkzeug - WARNING -  * Debugger is active!
2025-03-14 15:47:33,620 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-14 15:47:48,590 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 15:47:48] "POST /login HTTP/1.1" 200 -
2025-03-14 15:47:48,946 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 15:47:48] "POST /login HTTP/1.1" 200 -
2025-03-14 15:49:56,444 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 15:49:56] "POST /login HTTP/1.1" 200 -
2025-03-14 15:50:03,359 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 15:50:03] "POST /login HTTP/1.1" 200 -
2025-03-14 15:50:33,750 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-14 15:50:33,843 - werkzeug - INFO -  * Restarting with stat
2025-03-14 15:50:36,594 - werkzeug - WARNING -  * Debugger is active!
2025-03-14 15:50:36,619 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-14 15:50:56,435 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 15:50:56] "POST /login HTTP/1.1" 200 -
2025-03-14 15:52:20,582 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-14 15:52:20,710 - werkzeug - INFO -  * Restarting with stat
2025-03-14 15:52:23,538 - werkzeug - WARNING -  * Debugger is active!
2025-03-14 15:52:23,563 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-14 16:07:14,203 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\sql_operate.py', reloading
2025-03-14 16:07:14,310 - werkzeug - INFO -  * Restarting with stat
2025-03-14 16:07:17,532 - werkzeug - WARNING -  * Debugger is active!
2025-03-14 16:07:17,559 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-14 17:03:03,058 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 17:03:03] "POST /login HTTP/1.1" 200 -
2025-03-14 17:06:42,967 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-14 17:06:43,092 - werkzeug - INFO -  * Restarting with stat
2025-03-14 17:06:46,158 - werkzeug - WARNING -  * Debugger is active!
2025-03-14 17:06:46,184 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-14 17:13:44,042 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 17:13:44] "POST /login HTTP/1.1" 200 -
2025-03-14 17:20:29,241 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 17:20:29] "POST /login HTTP/1.1" 200 -
2025-03-14 17:23:51,408 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 17:23:51] "POST /login HTTP/1.1" 200 -
2025-03-14 17:24:50,537 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 17:24:50] "POST /login HTTP/1.1" 200 -
2025-03-14 17:30:06,809 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 17:30:06] "POST /login HTTP/1.1" 200 -
2025-03-14 17:30:25,817 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 17:30:25] "POST /register HTTP/1.1" 200 -
2025-03-14 17:30:36,180 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 17:30:36] "POST /login HTTP/1.1" 200 -
2025-03-14 17:30:38,748 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 17:30:38] "POST /login HTTP/1.1" 200 -
2025-03-14 17:30:46,075 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 17:30:46] "POST /login HTTP/1.1" 200 -
2025-03-14 17:32:50,827 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-14 17:32:50,927 - werkzeug - INFO -  * Restarting with stat
2025-03-14 17:32:53,814 - werkzeug - WARNING -  * Debugger is active!
2025-03-14 17:32:53,842 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-14 17:37:25,402 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 17:37:25] "POST /login HTTP/1.1" 200 -
2025-03-14 17:51:07,233 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-14 17:51:07,338 - werkzeug - INFO -  * Restarting with stat
2025-03-14 17:51:10,239 - werkzeug - WARNING -  * Debugger is active!
2025-03-14 17:51:10,312 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-14 17:51:26,986 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 17:51:26] "POST /login HTTP/1.1" 200 -
2025-03-14 17:51:36,433 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 17:51:36] "POST /login HTTP/1.1" 200 -
2025-03-14 17:53:18,041 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-14 17:53:18,144 - werkzeug - INFO -  * Restarting with stat
2025-03-14 17:53:21,068 - werkzeug - WARNING -  * Debugger is active!
2025-03-14 17:53:21,096 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-14 17:53:26,370 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 17:53:26] "POST /login HTTP/1.1" 200 -
2025-03-14 17:53:31,892 - werkzeug - INFO - 127.0.0.1 - - [14/Mar/2025 17:53:31] "POST /login HTTP/1.1" 200 -
2025-03-21 10:09:16,630 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-03-21 10:09:16,631 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-03-21 10:09:27,371 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-03-21 10:09:27,371 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-03-21 10:09:27,374 - werkzeug - INFO -  * Restarting with stat
2025-03-21 10:09:30,228 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 10:09:30,263 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-21 10:09:49,403 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 10:09:49] "POST /login HTTP/1.1" 200 -
2025-03-21 10:15:34,371 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\sql_operate.py', reloading
2025-03-21 10:15:34,477 - werkzeug - INFO -  * Restarting with stat
2025-03-21 10:15:37,535 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 10:15:37,561 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-21 10:15:45,929 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\sql_operate.py', reloading
2025-03-21 10:15:46,022 - werkzeug - INFO -  * Restarting with stat
2025-03-21 10:15:48,772 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 10:15:48,797 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-21 10:20:00,205 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-21 10:20:00,314 - werkzeug - INFO -  * Restarting with stat
2025-03-21 10:20:03,082 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 10:20:03,113 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-21 10:20:09,212 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 10:20:09] "POST /login HTTP/1.1" 200 -
2025-03-21 10:22:00,654 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-21 10:22:00,763 - werkzeug - INFO -  * Restarting with stat
2025-03-21 10:22:03,916 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 10:22:03,946 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-21 10:22:10,346 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 10:22:10] "POST /login HTTP/1.1" 200 -
2025-03-21 10:22:44,389 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-21 10:22:44,482 - werkzeug - INFO -  * Restarting with stat
2025-03-21 10:22:47,278 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 10:22:47,307 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-21 10:22:50,892 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 10:22:50] "POST /login HTTP/1.1" 200 -
2025-03-21 10:24:09,745 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-21 10:24:09,843 - werkzeug - INFO -  * Restarting with stat
2025-03-21 10:24:12,631 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 10:24:12,658 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-21 10:24:16,552 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 10:24:16] "POST /login HTTP/1.1" 200 -
2025-03-21 10:25:14,570 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-21 10:25:14,661 - werkzeug - INFO -  * Restarting with stat
2025-03-21 10:25:17,493 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 10:25:17,519 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-21 10:25:26,397 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 10:25:26] "POST /login HTTP/1.1" 200 -
2025-03-21 10:46:00,386 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-21 10:46:00,491 - werkzeug - INFO -  * Restarting with stat
2025-03-21 10:46:07,410 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 10:46:07,436 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-21 10:46:52,359 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-21 10:46:52,454 - werkzeug - INFO -  * Restarting with stat
2025-03-21 10:46:55,322 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 10:46:55,348 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-21 10:46:59,908 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 10:46:59] "POST /login HTTP/1.1" 200 -
2025-03-21 10:54:52,947 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 10:54:52] "POST /login HTTP/1.1" 200 -
2025-03-21 10:55:44,796 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 10:55:44] "POST /login HTTP/1.1" 200 -
2025-03-21 10:56:32,885 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 10:56:32] "POST /login HTTP/1.1" 200 -
2025-03-21 10:57:39,366 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 10:57:39] "POST /login HTTP/1.1" 200 -
2025-03-21 11:06:46,265 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 11:06:46] "POST /login HTTP/1.1" 200 -
2025-03-21 11:14:59,975 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 11:14:59] "POST /login HTTP/1.1" 200 -
2025-03-21 11:33:54,519 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 11:33:54] "POST /login HTTP/1.1" 200 -
2025-03-21 11:36:10,279 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 11:36:10] "POST /login HTTP/1.1" 200 -
2025-03-21 11:52:18,416 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 11:52:18] "POST /login HTTP/1.1" 200 -
2025-03-21 11:52:54,855 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 11:52:54] "POST /login HTTP/1.1" 200 -
2025-03-21 11:53:19,722 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 11:53:19] "POST /register HTTP/1.1" 200 -
2025-03-21 11:53:26,002 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 11:53:26] "POST /login HTTP/1.1" 200 -
2025-03-21 11:54:35,920 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 11:54:35] "POST /login HTTP/1.1" 200 -
2025-03-21 11:55:06,809 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 11:55:06] "POST /login HTTP/1.1" 200 -
2025-03-21 11:58:32,667 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 11:58:32] "POST /login HTTP/1.1" 200 -
2025-03-21 11:58:39,364 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 11:58:39] "POST /login HTTP/1.1" 200 -
2025-03-21 11:58:54,720 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 11:58:54] "POST /login HTTP/1.1" 200 -
2025-03-21 11:58:58,864 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 11:58:58] "POST /login HTTP/1.1" 200 -
2025-03-21 11:59:00,829 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 11:59:00] "POST /login HTTP/1.1" 200 -
2025-03-21 11:59:20,147 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 11:59:20] "POST /login HTTP/1.1" 200 -
2025-03-21 14:04:27,136 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:04:27] "POST /login HTTP/1.1" 200 -
2025-03-21 14:04:47,967 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:04:47] "POST /login HTTP/1.1" 200 -
2025-03-21 14:05:06,861 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:05:06] "POST /login HTTP/1.1" 200 -
2025-03-21 14:05:15,936 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:05:15] "POST /login HTTP/1.1" 200 -
2025-03-21 14:05:25,453 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:05:25] "POST /login HTTP/1.1" 200 -
2025-03-21 14:05:32,629 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:05:32] "POST /login HTTP/1.1" 200 -
2025-03-21 14:08:51,228 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:08:51] "POST /login HTTP/1.1" 200 -
2025-03-21 14:08:56,408 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:08:56] "POST /login HTTP/1.1" 200 -
2025-03-21 14:09:01,556 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:09:01] "POST /login HTTP/1.1" 200 -
2025-03-21 14:11:44,005 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-21 14:11:44,169 - werkzeug - INFO -  * Restarting with stat
2025-03-21 14:11:47,589 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 14:11:47,620 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-21 14:12:01,678 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:12:01] "POST /login HTTP/1.1" 200 -
2025-03-21 14:13:43,895 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-21 14:13:43,991 - werkzeug - INFO -  * Restarting with stat
2025-03-21 14:13:46,930 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 14:13:46,962 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-21 14:13:55,629 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:13:55] "POST /login HTTP/1.1" 200 -
2025-03-21 14:13:57,218 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:13:57] "POST /login HTTP/1.1" 200 -
2025-03-21 14:14:08,732 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:14:08] "POST /login HTTP/1.1" 200 -
2025-03-21 14:16:35,579 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:16:35] "POST /login HTTP/1.1" 200 -
2025-03-21 14:18:05,989 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-21 14:18:06,091 - werkzeug - INFO -  * Restarting with stat
2025-03-21 14:18:09,015 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 14:18:09,041 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-21 14:20:36,712 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-21 14:20:36,806 - werkzeug - INFO -  * Restarting with stat
2025-03-21 14:20:39,707 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 14:20:39,734 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-21 14:20:55,000 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:20:55] "POST /login HTTP/1.1" 200 -
2025-03-21 14:23:36,109 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-21 14:23:36,201 - werkzeug - INFO -  * Restarting with stat
2025-03-21 14:23:38,984 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 14:23:39,017 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-21 14:23:52,082 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:23:52] "POST /login HTTP/1.1" 200 -
2025-03-21 14:25:17,694 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-21 14:25:17,786 - werkzeug - INFO -  * Restarting with stat
2025-03-21 14:25:20,705 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 14:25:20,730 - werkzeug - INFO -  * Debugger PIN: 139-401-168
2025-03-21 14:25:28,717 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:25:28] "POST /login HTTP/1.1" 200 -
2025-03-21 14:26:03,811 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:26:03] "POST /login HTTP/1.1" 200 -
2025-03-21 14:27:41,516 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:27:41] "POST /login HTTP/1.1" 200 -
2025-03-21 14:27:49,784 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:27:49] "POST /login HTTP/1.1" 200 -
2025-03-21 14:29:28,813 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:29:28] "POST /login HTTP/1.1" 200 -
2025-03-21 14:29:37,022 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:29:37] "POST /login HTTP/1.1" 200 -
2025-03-21 14:29:43,110 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:29:43] "POST /login HTTP/1.1" 200 -
2025-03-21 14:30:09,488 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:30:09] "POST /login HTTP/1.1" 200 -
2025-03-21 14:30:38,653 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:30:38] "POST /login HTTP/1.1" 200 -
2025-03-21 14:31:01,825 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:31:01] "POST /login HTTP/1.1" 200 -
2025-03-21 14:32:34,965 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:32:34] "POST /login HTTP/1.1" 200 -
2025-03-21 14:32:39,759 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:32:39] "POST /login HTTP/1.1" 200 -
2025-03-21 14:32:50,319 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:32:50] "POST /login HTTP/1.1" 200 -
2025-03-21 14:33:12,383 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:33:12] "POST /login HTTP/1.1" 200 -
2025-03-21 14:34:52,258 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:34:52] "POST /login HTTP/1.1" 200 -
2025-03-21 14:36:52,778 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:36:52] "POST /login HTTP/1.1" 200 -
2025-03-21 14:46:08,109 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:46:08] "POST /login HTTP/1.1" 200 -
2025-03-21 14:49:36,117 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:49:36] "POST /login HTTP/1.1" 200 -
2025-03-21 14:50:14,327 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:50:14] "POST /login HTTP/1.1" 200 -
2025-03-21 14:50:28,064 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:50:28] "POST /login HTTP/1.1" 200 -
2025-03-21 14:50:42,052 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 14:50:42] "POST /login HTTP/1.1" 200 -
2025-03-21 17:03:59,361 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-03-21 17:03:59,361 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-03-21 17:03:59,364 - werkzeug - INFO -  * Restarting with stat
2025-03-21 17:04:02,104 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 17:04:02,142 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-21 17:04:20,068 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\sql_operate.py', reloading
2025-03-21 17:04:20,165 - werkzeug - INFO -  * Restarting with stat
2025-03-21 17:04:22,983 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 17:04:23,017 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-21 17:17:07,441 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\returnformat.py', reloading
2025-03-21 17:17:07,535 - werkzeug - INFO -  * Restarting with stat
2025-03-21 17:17:10,200 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 17:17:10,234 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-21 17:18:15,900 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 17:18:15] "POST /login HTTP/1.1" 200 -
2025-03-21 17:18:26,487 - werkzeug - INFO - 127.0.0.1 - - [21/Mar/2025 17:18:26] "POST /update_avatarurl HTTP/1.1" 200 -
2025-03-21 17:19:40,861 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\returnformat.py', reloading
2025-03-21 17:19:40,955 - werkzeug - INFO -  * Restarting with stat
2025-03-21 17:19:43,648 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 17:19:43,682 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-21 17:20:28,362 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\returnformat.py', reloading
2025-03-21 17:20:28,447 - werkzeug - INFO -  * Restarting with stat
2025-03-21 17:20:31,104 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 17:20:31,137 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-21 17:21:24,858 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\returnformat.py', reloading
2025-03-21 17:21:24,953 - werkzeug - INFO -  * Restarting with stat
2025-03-21 17:21:27,631 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 17:21:27,668 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-21 17:22:55,777 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\app_test.py', reloading
2025-03-21 17:22:55,870 - werkzeug - INFO -  * Restarting with stat
2025-03-21 17:22:58,622 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 17:22:58,655 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-21 17:27:00,096 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\routes\\chat.py', reloading
2025-03-21 17:27:00,195 - werkzeug - INFO -  * Restarting with stat
2025-03-21 17:27:02,871 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 17:27:02,906 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-21 17:27:56,743 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\routes\\chat.py', reloading
2025-03-21 17:27:56,838 - werkzeug - INFO -  * Restarting with stat
2025-03-21 17:27:59,643 - werkzeug - WARNING -  * Debugger is active!
2025-03-21 17:27:59,677 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-21 17:29:03,986 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\routes\\chat.py', reloading
2025-03-21 17:29:04,072 - werkzeug - INFO -  * Restarting with stat
2025-03-24 10:07:22,768 - werkzeug - INFO -  * Restarting with stat
2025-03-24 10:10:49,391 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-03-24 10:10:49,391 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-03-24 10:10:49,395 - werkzeug - INFO -  * Restarting with stat
2025-03-24 10:10:52,666 - werkzeug - WARNING -  * Debugger is active!
2025-03-24 10:10:52,703 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-24 10:11:26,810 - werkzeug - INFO - 127.0.0.1 - - [24/Mar/2025 10:11:26] "POST /login HTTP/1.1" 200 -
2025-03-24 10:19:24,484 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\ws_app.py', reloading
2025-03-24 10:19:24,620 - werkzeug - INFO -  * Restarting with stat
2025-03-24 10:19:30,652 - werkzeug - WARNING -  * Debugger is active!
2025-03-24 10:19:30,697 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-24 10:19:43,101 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\sql_operate.py', reloading
2025-03-24 10:19:43,219 - werkzeug - INFO -  * Restarting with stat
2025-03-24 10:19:49,304 - werkzeug - WARNING -  * Debugger is active!
2025-03-24 10:19:49,377 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-24 10:21:05,119 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\sql_operate.py', reloading
2025-03-24 10:21:05,234 - werkzeug - INFO -  * Restarting with stat
2025-03-24 10:21:10,865 - werkzeug - WARNING -  * Debugger is active!
2025-03-24 10:21:10,924 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-24 10:29:10,811 - werkzeug - INFO - 127.0.0.1 - - [24/Mar/2025 10:29:10] "POST /login HTTP/1.1" 200 -
2025-03-24 10:29:24,778 - werkzeug - INFO - 127.0.0.1 - - [24/Mar/2025 10:29:24] "POST /login HTTP/1.1" 200 -
2025-03-24 10:29:32,021 - werkzeug - INFO - 127.0.0.1 - - [24/Mar/2025 10:29:32] "POST /login HTTP/1.1" 200 -
2025-03-24 10:32:02,413 - werkzeug - INFO - 127.0.0.1 - - [24/Mar/2025 10:32:02] "POST /login HTTP/1.1" 200 -
2025-03-24 10:32:55,228 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\sql_operate.py', reloading
2025-03-24 10:32:55,351 - werkzeug - INFO -  * Restarting with stat
2025-03-24 10:33:00,927 - werkzeug - WARNING -  * Debugger is active!
2025-03-24 10:33:01,029 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-24 10:42:26,353 - werkzeug - INFO - 127.0.0.1 - - [24/Mar/2025 10:42:26] "POST /login HTTP/1.1" 200 -
2025-03-24 10:44:01,757 - werkzeug - INFO - 127.0.0.1 - - [24/Mar/2025 10:44:01] "POST /login HTTP/1.1" 200 -
2025-03-24 10:50:41,101 - werkzeug - INFO - 127.0.0.1 - - [24/Mar/2025 10:50:41] "POST /login HTTP/1.1" 200 -
2025-03-24 10:51:59,175 - werkzeug - INFO - 127.0.0.1 - - [24/Mar/2025 10:51:59] "POST /login HTTP/1.1" 200 -
2025-03-24 10:54:03,747 - werkzeug - INFO - 127.0.0.1 - - [24/Mar/2025 10:54:03] "POST /login HTTP/1.1" 200 -
2025-03-24 10:54:46,248 - werkzeug - INFO - 127.0.0.1 - - [24/Mar/2025 10:54:46] "POST /login HTTP/1.1" 200 -
2025-03-24 10:55:42,948 - werkzeug - INFO - 127.0.0.1 - - [24/Mar/2025 10:55:42] "POST /login HTTP/1.1" 200 -
2025-03-24 11:21:58,389 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\sql_operate.py', reloading
2025-03-24 11:21:58,567 - werkzeug - INFO -  * Restarting with stat
2025-03-24 11:22:03,660 - werkzeug - WARNING -  * Debugger is active!
2025-03-24 11:22:03,698 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-24 11:23:37,743 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\app.py', reloading
2025-03-24 11:23:37,864 - werkzeug - INFO -  * Restarting with stat
2025-03-24 11:23:44,284 - werkzeug - WARNING -  * Debugger is active!
2025-03-24 11:23:44,322 - werkzeug - INFO -  * Debugger PIN: 854-038-306
2025-03-24 11:24:21,993 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\app.py', reloading
2025-03-24 11:24:22,119 - werkzeug - INFO -  * Restarting with stat
2025-03-24 11:24:28,033 - werkzeug - WARNING -  * Debugger is active!
2025-03-24 11:24:28,107 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-24 11:36:22,794 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\weworkapi_python\\callback_json\\Sample.py', reloading
2025-03-24 11:36:22,938 - werkzeug - INFO -  * Restarting with stat
2025-03-24 11:36:28,346 - werkzeug - WARNING -  * Debugger is active!
2025-03-24 11:36:28,414 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-24 11:41:37,700 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\callback\\Sample.py', reloading
2025-03-24 11:41:37,910 - werkzeug - INFO -  * Restarting with stat
2025-03-24 11:41:43,888 - werkzeug - WARNING -  * Debugger is active!
2025-03-24 11:41:43,941 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-24 11:42:50,277 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\callback\\Sample.py', reloading
2025-03-24 11:42:50,400 - werkzeug - INFO -  * Restarting with stat
2025-03-24 11:42:55,216 - werkzeug - WARNING -  * Debugger is active!
2025-03-24 11:42:55,256 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-24 11:43:18,851 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\callback\\Sample.py', reloading
2025-03-24 11:43:18,974 - werkzeug - INFO -  * Restarting with stat
2025-03-24 11:43:24,671 - werkzeug - WARNING -  * Debugger is active!
2025-03-24 11:43:24,765 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-24 11:48:26,891 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\callback\\WXBizMsgCrypt3.py', reloading
2025-03-24 11:48:27,034 - werkzeug - INFO -  * Restarting with stat
2025-03-24 11:48:32,939 - werkzeug - WARNING -  * Debugger is active!
2025-03-24 11:48:32,980 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-24 11:50:27,348 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\callback\\WXBizMsgCrypt3.py', reloading
2025-03-24 11:50:27,579 - werkzeug - INFO -  * Restarting with stat
2025-03-24 11:50:33,002 - werkzeug - WARNING -  * Debugger is active!
2025-03-24 11:50:33,051 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-24 11:54:48,923 - werkzeug - INFO -  * Detected change in 'E:\\programmer\\flask\\syzx\\modules\\callback\\Sample.py', reloading
2025-03-24 11:54:49,061 - werkzeug - INFO -  * Restarting with stat
2025-03-24 11:54:54,637 - werkzeug - WARNING -  * Debugger is active!
2025-03-24 11:54:54,731 - werkzeug - INFO -  * Debugger PIN: 392-610-352
2025-03-24 11:57:11,427 - werkzeug - INFO - 127.0.0.1 - - [24/Mar/2025 11:57:11] "POST /login HTTP/1.1" 200 -
2025-04-10 08:23:15,924 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-04-10 08:23:15,924 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-04-10 08:23:15,925 - apscheduler.scheduler - INFO - Added job "tft_off1" to job store "default"
2025-04-10 08:23:15,926 - apscheduler.scheduler - INFO - Added job "tft_off2" to job store "default"
2025-04-10 08:23:15,926 - apscheduler.scheduler - INFO - Scheduler started
2025-04-10 08:23:15,942 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-04-10 08:23:15,942 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
