# -*- coding:utf-8 -*-
from flask import Blueprint, jsonify, request

from modules.sql_operate import operate_user

web_notice = Blueprint('notice', __name__)


@web_notice.route('/notice', methods = ['POST', 'GET'])
def notice():
    if request.method == 'POST':
        data = request.get_json()
        mysql = operate_user()
        mysql.update_push(type_ = data['type'], state = data['status'], openid = data['openid'])
        return jsonify({'state': 'ok'})
