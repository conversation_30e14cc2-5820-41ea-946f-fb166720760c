from datetime import datetime

from flask import Blueprint, request, jsonify, Response

from modules.HKVSspider import Hikvision
from modules.op_sqlite import OpOAUser, OpWebOAUser

web_door = Blueprint('door', __name__)

try:
    print('门禁后台登录初始化')
    hikvision = Hikvision('admin', 'hm480103')
    hikvision.login()
except TimeoutError:
    print('门禁连接故障，请检查！！！')


@web_door.route('/addUser', methods=['POST', 'GET'])
def addUser():
    if request.method == 'POST':
        id_ = request.form.get('id')
        name = request.form.get('name')
        if 'user_img' not in request.files:
            return '未上传照片'
        file = request.files['user_img']

        # webOA = OpWebOAUser()

        # print(webOA.get_workcode_id_by_name(name))
        hikvision.add_user(id_, name)
        res_json = hikvision.updata_img(id_, file)

        return hikvision.get_user_list()


@web_door.route('/editUser', methods=['POST', 'GET'])
def editUser():
    json_data = request.get_json()
    print(json_data)
    return '123456'


@web_door.route('/delUser', methods=['POST'])
def delUser():
    if request.method == 'POST':
        employeeNo = request.get_json()['id']

        return_data = hikvision.del_user(employeeNo)

        return jsonify({'msg': return_data})


@web_door.route('/getFaceImg', methods=['GET'])
def get_face_img():
    if request.method == 'GET':
        url = request.args['url']
        img_base64 = hikvision.get_img_content(url)
        # print(img_base64.decode('utf-8'))
        return Response(img_base64, mimetype='image/jpeg')


@web_door.route('/delUsers', methods=['POST'])
def delUsers():
    if request.method == 'POST':
        # employeeNo = request.get_json()['id']

        # return_data = hikvision.del_user(employeeNo)

        id_list = request.get_json()['id_list']
        return_list = []
        for i in id_list:
            return_list.append(hikvision.del_user(i))

        # return jsonify({'msg': return_data})
        return return_list


@web_door.route('/ctrldoor', methods=['POST'])
def ctrldoor():
    if request.method == 'POST':
        get_data = request.get_json()
        opsql = OpWebOAUser()
        print(get_data)
        user_id = get_data['sqr']
        req_department = get_data['sqbm']
        start_time_temp = get_data['sqsj'].split(' ')
        start_time = f'{start_time_temp[0]}T{start_time_temp[1]}:00'
        user_work_code = opsql.get_workcode_by_id(user_id)
        if not user_work_code:
            return jsonify({
                "statusCode": 400,
                "header": {},
                "msg": "当前用户未录入门数据库，请联系管理员"
            })
        # print(user_work_code)
        end_time_temp = get_data['nhsj'].split(' ')
        end_time = f'{end_time_temp[0]}T{end_time_temp[1]}:00'
        op_oa = OpOAUser()
        user_name = op_oa.get_user_name_by_id(user_id)[0][0]
        if hikvision.set_user_info(user_work_code[0][0], user_name, start_time, end_time) == 'OK':
            return jsonify({
                "statusCode": 200,
                "header": {},
                "msg": "申请提交成功，权限已打开"
            })
        else:
            return jsonify({
                "statusCode": 400,
                "header": {},
                'msg': "当前用户未录入门禁人脸数据"
            })


# 设备卡片过滤搜索API
@web_door.route('/getInfo', methods=['POST', 'GET'])
def getInfo():
    if request.method == 'POST':
        return hikvision.get_user_list()
    if request.method == 'GET':
        # try:
        user_list = hikvision.get_user_list()
        # print(user_list)
        now_time = datetime.now()
        # except KeyError:
        #     hikvision.login()
        #     user_list = hikvision.get_user_list()
        data_list = []
        for i in user_list:
            # print(i)
            start_time = i['Valid']['beginTime'].replace('T', ' ')
            end_time = i['Valid']['endTime'].replace('T', ' ')
            data_list.append({
                'id': i['employeeNo'],
                'name': i['name'],
                'start_time': start_time,
                'end_time': end_time,
                'state': '权限已打开' if datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S') > now_time else '权限过期',
                'img_url': i['img']
            })
            # print(i['Valid']['enable'] == 'True', i['Valid']['enable'])
        return jsonify({'code': 0, 'count': len(data_list), 'data': data_list, 'msg': ''})
