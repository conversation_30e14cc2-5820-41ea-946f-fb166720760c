from flask import Blueprint, request
from modules.op_sqlite import OpRecord, OpDevice
from routes.web.web import format_info, format_info2list
import math
from global_config import _config

web_edit_task = Blueprint('task', __name__)


# 设备卡片过滤搜索API
@web_edit_task.route('/editTask', methods=['POST', 'GET'])
def edit_task():
    if request.method == 'POST':
        pass
    if request.method == 'GET':
        temp = {
            "ip": "***********",
            "port": 8000,
            "slave_address": 1,
            "start_address": 1,
            "num_registers": 2
        }
        _config['temperature_humidity_info_list'].append(temp)
        with open('global_config.py', 'w', encoding='utf-8') as f:
            f.write(f'_config = {_config}')
            print('写入完成')


        return '123456'









