from flask import Blueprint, render_template, jsonify, request, session, redirect, g

from modules.op_sqlite import OpRecord, OpDevice
from routes.web.web import format_info, format_info2list
from test import *

web_device = Blueprint('device', __name__)


# 设备编辑更新数据API
@web_device.route('/editDevice', methods=['POST'])
def editdevice():
    if request.method == 'POST':
        arg_list = request.get_json()
        ip = arg_list['ip']
        creat_time = arg_list['creat_time']
        sql = OpDevice()
        ip_check = sql.query_ip(ip)
        if (not ip_check) or ip_check[0][1] == creat_time:
            device_name = arg_list['device_name']
            department = arg_list['department']
            state = arg_list['state']
            position = arg_list['position']
            port = arg_list['port']
            sql.edit_device(device_name, department, position, ip, port, state, creat_time)
            return jsonify({'code': 0, 'msg': '更新成功'})
        else:
            return jsonify({'code': 1, 'msg': '当前IP已占用'})


# 添加设备信息
@web_device.route('/addDevice', methods=['POST'])
def index():
    if request.method == 'POST':
        arg_list = request.get_json()
        ip = arg_list['ip']
        sql = OpDevice()
        ip_check = sql.query_ip(ip)
        if not ip_check:
            device_name = arg_list['device_name']
            department = arg_list['department']
            state = arg_list['state']
            position = arg_list['position']
            port = arg_list['port']
            if session.get('role') == 'admin':
                role = '管理员'
            else:
                role = '普通用户'
            sql.add_device_info(device_name=device_name, department=department, position=position, ip=ip,
                                port=port, state=state, creat_role=role)
            return jsonify({'code': 0, 'msg': '设备添加成功'})
        else:
            return jsonify({'code': 1, 'msg': '当前IP已占用'})


# 删除设备， table表格内删除按钮
@web_device.route('/deldevice', methods=['POST'])
def del_device():
    if request.method == 'POST':
        creat_time = request.form['creat_time']
        opsql = OpDevice()
        try:
            opsql.delete_device_data(creat_time)
            return_data = {
                'state': 'Ok',
                'mag': '设备删除成功'
            }
            return jsonify(return_data)
        except:
            return_data = {
                'state': 'Err',
                'mag': '设备删除失败'
            }
            return jsonify(return_data)


# 多选删除设备
@web_device.route('/deldevices', methods=['POST'])
def del_devices():
    if request.method == 'POST':
        creat_time_list = request.get_json()['creat_time_list']
        opsql = OpDevice()
        try:
            sql_str = f'DELETE FROM device WHERE creat_time IN '
            temp_str = '('
            for i in creat_time_list:
                temp_str += f"'{i}', "
            sql_str += (temp_str + ')')
            opsql.exec_sql_str(sql_str.replace(', )', ')'))
            return_data = {
                'state': 'Ok',
                'mag': '设备删除成功'
            }
            return jsonify(return_data)
        except:
            return_data = {
                'state': 'Err',
                'mag': '设备删除失败'
            }
            return jsonify(return_data)


# table 搜索筛选设备
@web_device.route('/searchDevice', methods=['POST', 'GET'])
def search_device():
    if request.method == 'GET':
        args = dict(request.args)
        page = int(args.pop('page'))
        limit = int(args.pop('limit'))
        # offset = (int(page) - 1) * int(limit)
        # print(args, page, limit)

        search_str = 'select * from device where'
        temp = 0
        for i in args:
            if args[i] != '' and temp != 0:
                search_str += f'and {i}="{args[i]}" '
                # temp += 1
            elif args[i] != '':
                search_str += f' {i}="{args[i]}" '
                temp += 1
        # search_str += f'limit {limit} offset {offset}'
        # print(search_str)
        op_sql = OpDevice()
        data, keys = op_sql.exec_sql_str(search_str)
        data_list = format_info(data, keys)

        temp_list = []

        # 将数据根据limit分为二维数组
        for i in range(0, len(data_list), limit):
            child_row = data_list[i:i + limit]
            temp_list.append(child_row)

        count = ((len(temp_list) - 1) * limit) + len(temp_list[-1])

        if data_list:
            data = {
                'code': 0,
                'msg': "",
                "count": count,
                'data': temp_list[page - 1]
            }
            return data
        else:
            data = {
                'code': 1,
                'msg': "无结果",
                "count": 0,
                'data': []
            }
            return data


# table 回调API，刷新按钮API
@web_device.route('/AirConditionData', methods=['GET'])
def air_condition_data():
    # print(request.remote_addr)
    if request.method == 'GET':
        arg = request.args
        page = arg['page']
        limit = arg['limit']

        offset = (int(page) - 1) * int(limit)
        opsql = OpDevice()
        data, keys = opsql.get_all_device_info(limit=limit, offset=offset)
        data_list = []
        for i in data:
            data_list.append(dict(zip(keys, i)))
        data = {
            'code': 0,
            'msg': "",
            "count": opsql.get_device_num()[0][0],
            'data': data_list
        }
        return jsonify(data)










