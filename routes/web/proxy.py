import requests
from werkzeug.datastructures import Headers
import re
from flask import Blueprint, request, Response
import urllib.parse

web_proxy = Blueprint('proxy', __name__)

base_url = 'http://************:200'
proxy_session = requests.Session()
target_site = 'http://************:200'
proxy_prefix = '/proxy'


def rewrite_content(content):
    if not isinstance(content, str):
        print('未替换错误')
        return content
    target_url = target_site.rstrip('/')
    proxy_url = f"{target_site}{proxy_prefix}"

    content = re.sub(rf'(["\']])({re.escape(target_url)})([^"\']*)(["\'])',
                     lambda m: f'{m.group(1)}{proxy_url}{m.group(3)}{m.group(4)}', content)
    content = re.sub(rf'(["\'])/([^"\']*)(["\'])', lambda m: f'{m.group(1)}{proxy_prefix}/{m.group(2)}{m.group(3)}'
    if not m.group(2).startswith(('http://', 'https://', 'data:', '#'))
    else m.group(0), content)
    return content.encode('utf-8') if isinstance(content, str) else content


def login(session):
    url = 'http://************:200/Common/LogOn'
    headers = {
        'Origin': 'http://************:200', 'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36',
        'Referer': 'http://************:200/Common/LogOn?message=%E8%AF%B7%E7%99%BB%E5%BD%95',
        # 'Accept-Encoding': 'gzip, deflate', 'Accept-Language': 'zh-CN,zh;q=0.9',
        # 'Cookie': 'session=eyJsb2dpbl9zdGF0ZSI6MSwicm9sZSI6ImFkbWluIn0.aDeoSg.cM6egjaH8uft4L1KrwXfmVyOb0M',
        'Host': '************:200'}

    data = {
        'UserName': 'admin',
        'Password': '123456',
    }

    res = session.post(url=url, headers=headers, data=data)
    # print(session.cookies.get_dict(), res.headers)
    return res.content, session.cookies.get_dict()


@web_proxy.route('/', defaults={'path': ''})
@web_proxy.route('/<path:path>', methods=['GET', 'POST'])
def proxy(path):
    target_url = f"{base_url}/{path}"
    # print('path', path, request.args)
    headers = {name: value for (name, value) in request.headers
               if name.lower() not in ['host', 'connection']}
    # cookie = {name: value for (name, value) in request.cookies.items()}

    # print(proxy_session.cookies, cookie)
    # proxy_session.cookies.update(cookie)
    print('path', path)

    if request.method == 'GET':
        if 'Show' in target_url or 'Download' in target_url:
            arg = request.args
            query_string = urllib.parse.urlencode(arg)
            target_url = f'{target_url}?{query_string}'
            resp = proxy_session.get(target_url)

            return Response(
                resp.iter_content(chunk_size=8192),
                mimetype=resp.headers.get('Content-Type'),
                status=resp.status_code,
                headers=dict(resp.headers)
            )

        elif '.map' in path:
            return Response(
                '不准你发些乱七八糟的请求',
                status=500
            )

        elif 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,' \
             '*/*;q=0.8,application/signed-exchange;v=b3;' in request.headers.get('Accept'):
            arg = request.args
            query_string = urllib.parse.urlencode(arg)
            target_url = f'{target_url}?{query_string}'
            if path == 'Home/Index' or path == 'Common/LogOn':  # 访问首页
                content, cookie_dict = login(proxy_session)
                content = rewrite_content(content.decode('utf-8'))
                response = Response(
                    content,
                    status=200,
                )
                for i in cookie_dict:
                    response.set_cookie(key=i, value=cookie_dict[i])
                return response
            elif path == 'Craft':
                resp = proxy_session.get(target_url)

                content = rewrite_content(resp.content.decode('utf-8')).decode('utf-8')

                # 逻辑改写
                content = re.sub("\$\('#showCraftIframe'\)\.attr\('src','/proxy/Craft/Show\?id='\+id\);",
                                 'window.parent.open("/proxy/Craft/Show?id="+id, "_blank")', content, re.S)
                content = re.sub('href="#" data-toggle="modal" data-target="#ShowCraftModal">', ' ', content, re.S)
                content = re.sub('function showloding', 'function showloding2', content, re.S)
                content = re.sub('href="/proxy/Craft/Edit\?id=(\w)"',
                                 lambda
                                     m: f'''href="javascript: window.parent.open('/proxy/Craft/Edit?id={m.group(1)}', '_blank')"''',
                                 content, re.S)

                content = content.encode('utf-8')

                response = Response(
                    content,
                    status=resp.status_code,
                )
                return response

            resp = proxy_session.get(target_url)
            # print(target_url, resp.text)
            try:
                content = rewrite_content(resp.content.decode('utf-8'))
            except UnicodeDecodeError:
                content = resp.content
                # with open('1.pdf', 'wb') as f:
                #     f.write(resp.content)
            return Response(
                content,
                status=resp.status_code,
                # headers=proxy_header
            )

        elif target_url == 'http://************:200/dashbord/js/echart.js':
            resp = proxy_session.get(target_url)
            content = resp.content.decode('utf-8').replace('/Home/GetDashBoardData',
                                                           '/proxy/Home/GetDashBoardData')  # .replace('console.log(alldata)', 'console.log(window.parent)')
            # response = make_response(content)
            # response.status_code = resp.status_code
            response = Response(content, resp.status_code)
            return response
        elif '.js' in path or '.css' in path:
            resp = proxy_session.get(target_url)
            proxy_header = Headers()
            for header, value in resp.headers.items():
                if header.lower() not in ['connent-length', 'connent-length']:  # 'content-encoding', 'connent-length'
                    proxy_header[header] = value
            return Response(
                resp.content,
                status=resp.status_code,
                headers=proxy_header
            )
        else:
            resp = proxy_session.get(target_url)
    elif request.method == 'POST':
        content_type = request.headers.get('Content-Type')
        request_args = {
            'headers': headers,
            'params': request.args
        }
        if 'json' in content_type:
            request_args['json'] = request.get_json()
        elif 'form' in content_type:
            request_args['data'] = request.form
        else:
            request_args['data'] = request.get_data()
        resp = proxy_session.post(target_url, **request_args)
        # print(resp.status_code, dict(resp.cookies))
    else:
        print('请求方式不支持')
        return '请求方式不支持'
    # print(resp.text)
    if target_url == 'http://************:200/Common/LogOn':
        content = rewrite_content(resp.content.decode('utf-8'))
        response = Response(content, resp.status_code)
        cookie_dict = proxy_session.cookies.get_dict()
        for i in cookie_dict:
            response.set_cookie(key=i, value=cookie_dict[i])
        return response
    response = Response(resp.content, resp.status_code)
    return response
