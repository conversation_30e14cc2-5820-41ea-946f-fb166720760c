from flask import Blueprint, request
from modules.op_sqlite import OpRecord, OpDevice
from routes.web.web import format_info, format_info2list
import math

web_search = Blueprint('search', __name__)


# 设备卡片过滤搜索API
@web_search.route('/searchCard', methods=['POST', 'GET'])
def search_card():
    if request.method == 'POST':
        data = request.get_json()
        page = int(data['page'])
        limit = int(data['limit'])
        offset = (int(page) - 1) * int(limit)
        op_device_sql = OpDevice()
        op_record_sql = OpRecord()

        sql_str = 'select *, MAX(update_time) from deviceRecord where '
        search_params = data['search_params']
        temp = 0

        # sql命令组合
        for key, value in search_params.items():
            if value != '' and (key == 'position' or key == 'department'):
                sql_str += f'and position like "%{value}%" ' if temp != 0 else f'position like "%{value}%" '
                temp += 1
            elif value != '' and key == 'AirConditioner_state':
                ip_list = op_device_sql.get_ip_from_devices_state(value)
                temp_sql_str = f'AirConditioner_state="{value}" and ip in {tuple(format_info2list(ip_list))} '
                sql_str += f'and {temp_sql_str}' if temp != 0 else temp_sql_str
                temp += 1
            elif value != '' and temp != 0:
                sql_str += f'and {key}="{value}" '
            elif value != '':
                sql_str += f' {key}="{value}" '
                temp += 1

        sql_str = (sql_str.replace(', )', ')') + f' group by ip limit {limit} offset {offset};')
        # print(sql_str)
        data, keys = op_record_sql.exec_sql_str(sql_str)
        data = format_info(data, keys)
        print(data, keys)
        card_list = []
        for i in data:
            temperature_flag = 'normal' if 10 <= float(i['temperature']) <= 35 else 'warning'
            voltage_flag = 'normal' if 215 <= float(i['voltage']) <= 225 else 'danger'
            humidity_flag = 'normal' if 40 <= float(i['humidity']) <= 65 else 'warning'
            current_flag = 'normal' if 8 <= float(i['current']) <= 20 else 'danger'
            data_list = op_device_sql.get_device_state(i['ip'])
            # print(i, data_list)
            if data_list:
                state = data_list[0][0]
            else:
                continue
            card = f'''
                <div class="layui-col-md3" style="">
                    <div class="compact-data-module {i['creat_time']}">
                        <div class="module-header">
                            <span>
                                <i class="layui-icon layui-icon-location"></i>
                                {i['position']}
                            </span>

                            <span class="layui-badge {'layui-bg-green' if state == '开启' else 'layui-bg-gray'}">{state}</span>

                        </div>
                        <div class="card-ip">IP信息：{i['ip']}</div>
                        <div class="module-body">
                            <div class="data-grid">
                                <div class="data-item">
                                    <div class="data-lable">温度</div>
                                    <div class="data-value">
                                        <span class="status-indicator status-{temperature_flag}"></span>
                                        {i['temperature']}<span class="value-unit">℃</span>
                                    </div>
                                </div>
                                <div class="data-item">
                                    <div class="data-lable">电压</div>
                                    <div class="data-value">
                                        <span class="status-indicator status-{voltage_flag}"></span>
                                        {i['voltage']}<span class="value-unit">V</span>
                                    </div>
                                </div>
                                <div class="data-item">
                                    <div class="data-lable">湿度</div>
                                    <div class="data-value">
                                        <span class="status-indicator status-{humidity_flag}"></span>
                                        {i['humidity']}<span class="value-unit">%RH</span>
                                    </div>
                                </div>
                                <div class="data-item">
                                    <div class="data-lable">电流</div>
                                    <div class="data-value">
                                        <span class="status-indicator status-{current_flag}"></span>
                                        {i['current']}<span class="value-unit">A</span>
                                    </div>
                                </div>
                            </div>
                            <div class="updata-time">空调开启时间：{i['open_time']}</div>
                            <div class="updata-time">数据更新时间：{i['update_time']}</div>
                        </div>
                    </div>
                </div>
                '''
            card_list.append(card)

        temp_list = []

        for i in range(0, len(card_list), 4):
            child_row = card_list[i:i + 4]
            temp_list.append(child_row)
        # print(len(temp_list))
        html = ''
        for i in temp_list:
            html_str = '<div class="layui-row">'
            for j in i:
                html_str += j
            html_str += '</div>'
            html += html_str

        # print(len(data))
        return_data = {
            'code': 0,
            'count': math.ceil(len(data) / 4),
            'html': html
        }
        return return_data


# table 搜索筛选设备
@web_search.route('/searchDevice', methods=['POST', 'GET'])
def search_device():
    if request.method == 'GET':
        args = dict(request.args)
        page = int(args.pop('page'))
        limit = int(args.pop('limit'))
        # offset = (int(page) - 1) * int(limit)
        # print(args, page, limit)

        search_str = 'select * from device where'
        temp = 0
        for i in args:
            if args[i] != '' and temp != 0:
                search_str += f'and {i}="{args[i]}" '
                # temp += 1
            elif args[i] != '':
                search_str += f' {i}="{args[i]}" '
                temp += 1
        # search_str += f'limit {limit} offset {offset}'
        print(search_str)
        op_sql = OpDevice()
        data, keys = op_sql.exec_sql_str(search_str)
        data_list = format_info(data, keys)
        print(data_list)
        temp_list = []

        # 将数据根据limit分为二维数组
        for i in range(0, len(data_list), limit):
            child_row = data_list[i:i + limit]
            temp_list.append(child_row)

        count = ((len(temp_list) - 1) * limit) + len(temp_list[-1])

        if data_list:
            data = {
                'code': 0,
                'msg': "",
                "count": count,
                'data': temp_list[page - 1]
            }
            return data
        else:
            data = {
                'code': 1,
                'msg': "无结果",
                "count": 0,
                'data': []
            }
            return data
