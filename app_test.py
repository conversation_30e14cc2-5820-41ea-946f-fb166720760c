import requests

# url = 'http://************:200/Common/LogOn'
#
# headers = {
#     # 'Content-Length': '213', 'Pragma': 'no-cache', 'Cache-Control': 'no-cache', 'Upgrade-Insecure-Requests': '1',
#            'Origin': 'http://************:200', 'Content-Type': 'application/x-www-form-urlencoded',
#            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36',
#            # 'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
#            'Referer': 'http://************:200/Common/LogOn?message=%E8%AF%B7%E7%99%BB%E5%BD%95',
#            # 'Accept-Encoding': 'gzip, deflate', 'Accept-Language': 'zh-CN,zh;q=0.9',
#            # 'Cookie': 'session=eyJsb2dpbl9zdGF0ZSI6MSwicm9sZSI6ImFkbWluIn0.aDeoSg.cM6egjaH8uft4L1KrwXfmVyOb0M',
#            'Host': '************:200'}
#
# data = {
#     'UserName': 'admin',
#     'Password': '123456',
#     # '__RequestVerificationToken':
#     # 'CfDJ8ElKzEFNfK1OswlY4S7DLu4ISGyWzfc3G5fmLFuz3Vv9HtqCtt0PgajkSPA-Pq-VeJRu-TLQukWyq7oZZluY2g_6uDYR1leL9OAl0xT_WDXKSMJVTDrHYKk1xmxqIpgs1SzGLh37v1Fnocp1orBlGo0'
#     }
#
# res = requests.post(url=url, headers=headers, data=data,)
# print(res.cookies, res.headers, res.text)


# url = 'http://************:200/Craft/Show?id=5'
#
# headers = {
#     'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
#
#     'Cookie': '.AspNetCore.Antiforgery.BPrXCZHqUiU=CfDJ8ElKzEFNfK1OswlY4S7DLu4xy1edHUZBOu6g2utEy2nYT6QIagwj7DVv-FdZLHwT0T_MACpyyE9_hFWCknsrDtvFpUNJ8Gm9U5Nj9_SbhkbzT7dPzQ4YD9PcK4T3_zz1VfbGC27S5JwFSqtz_oofelw; .AspNetCore.Session=CfDJ8ElKzEFNfK1OswlY4S7DLu7wee420aLyXBtj32P%2BY7E1IRL6VPxbdBOj5KCy4dGda1pl8Eqpxc6IUzb4FtFSX3hP4%2FNF6xTvFbhKfFQ0ozs%2FWCxsxQNvF06bP9nF2UlhU4mvWWF3bTB6VEE9v3lf8EHn9WTRi0WbofcPl442U1gD',
#     'Host': '************:200',
#
#     'Referer': 'http://************:200/Craft',
#
#     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36',
#     'Accept-Encoding': 'gzip, deflate',
#     'Accept-Language': 'zh-CN,zh;q=0.9',
#     'Cache-Control': 'no-cache',
#     'Connection': 'keep-alive',
#     'Pragma': 'no-cache',
#     'Upgrade-Insecure-Requests': '1'
# }
#
# res = requests.get(url, headers=headers)
# print(res.history)
# print(res.text)


import re
import json

a = '<iq xmlns="jabber:client" type="result" id="ea7c78e3-45ea-45cc-82be-5ec923e2b1cd" from="weaver" to="943|l@weaver/pc">' \
    '<query>[{"onlineUsers":["157","379","456","457","216","348","623","208","95","351"]},{"offlineUsers":["154","255","558","943","944","934","945","946"]}]</query></iq>'

print(eval(re.findall('":(.*?)}.*?":(.*?)}', a)[0][0]))
print(type(re.findall('>\[(.*?}),(.*?)]<', a)[0][0]))
print(json.loads(re.findall('>\[(.*?}),(.*?)]<', a)[0][0]))
