import sqlite3
import time
from datetime import datetime
import json
from dbutils.pooled_db import PooledDB
from contextlib import contextmanager
from sqlite_config import db_pool


# sqlite_pool = PooledDB(
#     creator=sqlite3,
#     dat
# )

class BaseBAO:
    @contextmanager
    def _get_cursor(self):
        with db_pool.get_connection() as conn:
            cursor = conn.cursor()
            try:
                yield cursor
                conn.commit()
            except:
                conn.rollback()
                raise
            finally:
                cursor.close()

    def _execute(self, query, params=None):
        with self._get_cursor() as cursor:
            cursor.execute(query, params or ())
            return cursor

    def _fetch_all(self, query, params=None):
        with self._get_cursor() as cursor:
            cursor.execute(query, params or ())
            return cursor.fetchall()

    def _fetch_all_description(self, query, params=None):
        with self._get_cursor() as cursor:
            cursor.execute(query, params or ())
            return cursor.fetchall(), [keys[0] for keys in cursor.description]

    def get_date_time(self):
        now = datetime.now()
        format_time = now.strftime("%Y-%m-%d %H:%M:%S")
        return format_time

    def format_info(self, data, keys):
        data_list = []
        for i in data:
            data_list.append(dict(zip(keys, i)))
        return data_list

    # 将查询数据格式化为列表
    def format_info2list(self, data):
        data_list = []
        for i in data:
            data_list.append(i[0])
        return data_list


class OpRecord(BaseBAO):
    def __init__(self):
        self.device_table = 'deviceRecord'

    # 获取所有设备信息
    def get_all_device_info(self, limit, offset):
        sql = f"""select * from {self.device_table} limit {limit} offset {offset}"""
        return self._fetch_all_description(sql)

    # 获取单个设备的所有信息
    def get_device_info_name(self, device_name):
        sql = f"""select * from {self.device_table} where name="{device_name}";"""
        return self._fetch_all(sql)

    # 获取单个设备的所有信息
    def get_device_info_ip(self, ip):
        sql = f"""select * from {self.device_table} where ip="{ip}";"""
        return self._fetch_all(sql)

    # 添加设备信息
    def add_device_data(self, device_name, position, ip, temperature, humidity, voltage, current, creat_time,
                        time_type, state, time_temp_name, time_temp_value):
        sql = f"""insert into {self.device_table} (device_name, position, ip, temperature, humidity, voltage, current, 
                creat_time, update_time, {time_type}, AirConditioner_state, {time_temp_name}) 
                values ('{device_name}', '{position}', '{ip}', '{temperature}', '{humidity}', '{voltage}', '{current}', 
                '{creat_time}', '{self.get_date_time()}', '{self.get_date_time()}', '{state}', '{time_temp_value}');"""
        self._execute(sql)

    # 删除某设备信息
    def delete_device_data(self, creat_time):
        sql = f'DELETE FROM {self.device_table} WHERE creat_time="{creat_time}";'
        return self._fetch_all(sql)

    # 执行sql语句
    def exec_sql_str(self, sql_str):
        return self._fetch_all_description(sql_str)

    # 获取设备数量
    def get_device_num(self):
        sql = f'select count(*) from {self.device_table};'
        return self._fetch_all(sql)

    # 更新空调打开时间
    def updat_device_open_time(self, ip):
        sql = f'UPDATE {self.device_table} SET open_time="{self.get_date_time()}" WHERE ip="{ip}";'
        return self._fetch_all(sql)

    # 更新空调关闭时间
    def updat_device_close_open_time(self, ip, time_type):
        sql = f'UPDATE {self.device_table} SET {time_type}="{self.get_date_time()}" WHERE ip="{ip}";'
        return self._fetch_all(sql)

    # 获取设备最新开启关闭时间
    def get_device_open_close_time(self, ip):
        sql = f'select MAX(open_time), MAX(close_time) from {self.device_table} WHERE ip="{ip}";'
        return self._fetch_all(sql)

    # 获取设备最新开启时间
    def get_device_open_time(self, ip):
        sql = f'select MAX(open_time) from {self.device_table} WHERE ip="{ip}";'
        return self._fetch_all(sql)

    # 获取设备最新关闭时间
    def get_device_close_time(self, ip):
        sql = f'select MAX(close_time) from {self.device_table} WHERE ip="{ip}";'
        return self._fetch_all(sql)


class OpDevice(BaseBAO):
    def __init__(self):
        self.device_table = 'device'

    # 获取所有设备
    def get_all_device(self):
        sql = f"""select device from {self.device_table};"""
        return self._fetch_all(sql)

    # 获取所有设备信息
    def get_all_device_info(self, limit, offset):
        sql = f"""select * from {self.device_table} limit {limit} offset {offset}"""
        return self._fetch_all_description(sql)

    # 获取单个设备的所有信息
    def get_device_info_name(self, device_name):
        sql = f"""select * from {self.device_table} where name="{device_name}";"""
        return self._fetch_all_description(sql)

    # 获取单个用户的所有信息
    def get_device_info_ip(self, ip):
        sql = f"""select * from {self.device_table} where ip="{ip}";"""
        return self._fetch_all_description(sql)

    # 获取所有ip
    def get_all_ip(self):
        sql = 'select ip from device;'
        return self._fetch_all(sql)

    # 查询ip
    def query_ip(self, ip):
        sql = f'select ip, creat_time from device where ip="{ip}";'
        return self._fetch_all(sql)

    # 查询创建时间
    def query_creat_time(self, creat_time):
        sql = f'select ip, creat_time from device where creat_time="{creat_time}";'
        return self._fetch_all(sql)

    # 添加设备信息
    def add_device_info(self, device_name, department, position, ip, port, state, creat_role):
        sql = f"""insert into {self.device_table} (device_name, department, position, ip, port, state, creat_time, update_time, creat_role) 
                values ('{device_name}', '{department}', '{position}', '{ip}', '{port}', '{state}', '{self.get_date_time()}', '{self.get_date_time()}', '{creat_role}');"""
        print(sql)
        self._execute(sql)

    # 编辑更新设备信息
    def edit_device(self, device_name, department, position, ip, port, state, creat_time):
        sql = f'''UPDATE {self.device_table} SET device_name="{device_name}", department="{department}",
                  position="{position}", ip="{ip}", port="{port}", state="{state}" WHERE creat_time="{creat_time}";'''
        return self._fetch_all(sql)

    # 删除某设备信息
    def delete_device_data(self, creat_time):
        sql = f'DELETE FROM {self.device_table} WHERE creat_time="{creat_time}";'
        return self._fetch_all(sql)

    # 执行sql语句
    def exec_sql_str(self, sql_str):
        return self._fetch_all_description(sql_str)

    # 获取设备数量
    def get_device_num(self):
        sql = f'select count(*) from {self.device_table};'
        return self._fetch_all(sql)

    # 更新设备在线状态
    def updat_device_state(self, state, ip):
        sql = f'UPDATE {self.device_table} SET AirConditioner_state="{state}" WHERE ip="{ip}";'
        return self._fetch_all(sql)

    # 更新设备在线状态
    def get_device_state(self, ip):
        sql = f'select AirConditioner_state from {self.device_table} WHERE ip="{ip}";'
        # print(sql)
        return self._fetch_all(sql)

    # 获取某状态下的所有IP
    def get_ip_from_devices_state(self, state):
        sql = f'select ip from {self.device_table} WHERE AirConditioner_state="{state}";'
        # print(sql)
        return self._fetch_all(sql)

    # 获取设备和空调某状态下的所有IP
    def get_department_from_devices_2_states(self, state1, state2):
        """
        state1:  设备状态
        state2： 空调状态
        return:  筛选部门
        """
        sql = f'select department from {self.device_table} WHERE state="{state1}" and AirConditioner_state="{state2}";'
        # print(sql)
        return self._fetch_all(sql)


class OpHistory(BaseBAO):
    def __init__(self):
        self.device_table = 'history'

    def add_data(self, department, airconditioner_state, device_state, online_num):
        sql = f"""insert into {self.device_table} (department, airconditioner_state, device_state, online_num, update_time)
                  values ('{department}', '{airconditioner_state}', '{device_state}', '{online_num}', '{self.get_date_time()}');"""
        self._execute(sql)

class OpOAUser(BaseBAO):
    def __init__(self):
        self.device_table = 'oaUser'

    def add_data(self, id_, name, position, department, departmentid, total_count):
        sql = f"""insert into {self.device_table} (id, name, position, department, departmentid, total_count)
                  values ('{id_}', '{name}', '{position}', '{department}', '{departmentid}', '{total_count}');"""
        self._execute(sql)

    def get_department(self, department):
        sql = f'select departmentid from {self.device_table} WHERE department="{department}";'
        return self._fetch_all_description(sql)

    def get_department_user_by_name(self, department):
        sql = f'select id, name from {self.device_table} WHERE department="{department}";'
        return self._fetch_all_description(sql)

    def get_department_user_by_id(self, id_):
        sql = f'select id, name from {self.device_table} WHERE departmentid="{id_}";'
        return self._fetch_all_description(sql)

    def get_user_name_by_id(self, id_):
        sql = f'select name from {self.device_table} WHERE id="{id_}";'
        return self._fetch_all(sql)

class OpWebOAUser(BaseBAO):
    def __init__(self):
        self.device_table = 'webOAUser'

    def add_data(self, id_, workcode, name, departmentid, department, jobtitle, mobile):
        sql = f"""insert into {self.device_table} (id, workcode, name, departmentid, department, jobtitle, mobile)
                  values ('{id_}', '{workcode}', '{name}', '{departmentid}', '{department}', '{jobtitle}', '{mobile}');"""
        self._execute(sql)

    def get_department(self, department):
        sql = f'select departmentid from {self.device_table} WHERE department="{department}";'
        return self._fetch_all_description(sql)

    def get_department_user_by_name(self, department):
        sql = f'select id, name from {self.device_table} WHERE department="{department}";'
        return self._fetch_all_description(sql)

    def get_department_user_by_id(self, id_):
        sql = f'select id, name from {self.device_table} WHERE departmentid="{id_}";'
        return self._fetch_all_description(sql)

    def get_user_name_by_id(self, id_):
        sql = f'select name from {self.device_table} WHERE id="{id_}";'
        return self._fetch_all(sql)

    def get_workcode_id_by_name(self, name):
        sql = f'select id workcode from {self.device_table} WHERE name="{name}";'
        return self._fetch_all(sql)

    def get_workcode_by_id(self, id_):
        sql = f'select workcode from {self.device_table} WHERE id="{id_}";'
        return self._fetch_all(sql)

    def get_id_by_workcode(self, workcode):
        sql = f'select id from {self.device_table} WHERE id="{workcode}";'
        return self._fetch_all(sql)

class OpCrane(BaseBAO):
    def __init__(self):
        self.device_table = 'crane'

    def add_data(self, ip, name, position, weight, up_height):
        sql = f"""insert into {self.device_table} (ip, name, position, weight, up_height)
                  values ('{ip}', '{name}', '{position}', '{weight}', '{up_height}', '{self.get_date_time()}');"""
        self._execute(sql)



if __name__ == '__main__':
    # op = OpUserSqlite()
    # op.create_user_table()
    # op.create_device_table()

    # op.add_user_info('admin', 'admin', '管理员', 1)

    # op2 = OpDevice()
    # op2.create_device_table()
    # op2.add_device_info('测试', '机关楼', '技术处', '*************', '502', '关闭', '管理员')
    # print(op2.query_ip('*************'))
    # print(op2.get_department_from_devices_2_states('开启', '开启'))
    # print(op.get_all_user())
    # keys = op2.cursor.description()
    # print(keys)
    # data, keys = op2.get_all_device_info()
    # data_list = []
    # for i in data:
    #     data_list.append(dict(zip(keys, i)))
    # print(data_list)

    # op3 = OpRecordSqlite()
    # sql_str = "select * from deviceRecord where ip in ('***********', '***********')"

    # select *, MAX(update_time) from deviceRecord where ip in ('***********', '************') group by ip

    # op3.exec_sql_str()



    op = OpOAUser()
    # get_ = op.get_department_user('技术处')
    # print(op.format_info(get_[0], get_[1]))
    print(op.get_user_name_by_id(943)[0][0])


