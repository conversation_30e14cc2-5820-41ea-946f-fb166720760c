# -*- coding:utf-8 -*-
import hashlib
import json
import re
import time
import uuid

import requests
from requests.packages.urllib3.exceptions import InsecureRequestWarning
import xml.etree.ElementTree as ET
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad, pad
import base64

requests.packages.urllib3.disable_warnings(InsecureRequestWarning)  # 禁用证书校验警告

base_ip = 'https://**************'


def sha256(input_str):
    """
    计算输入字符串的SHA-256哈希值
    """
    if isinstance(input_str, str):
        input_bytes = input_str.encode('utf-8')
    else:
        input_bytes = input_str

    return hashlib.sha256(input_bytes).hexdigest()


def calculate_hash(t, e, n=False):
    """
    根据不同条件计算哈希值

    参数:
    t: 包含userName, salt, challenge和iIterate属性的字典
    e: 输入字符串
    n: 布尔值，决定使用哪种哈希算法路径

    返回:
    计算后的哈希字符串
    """
    r = ""

    if n:
        # 第一种逻辑路径
        r = sha256(t['userName'] + t['salt'] + e)
        r = sha256(r + t['challenge'])
        for o in range(2, t['iIterate']):
            r = sha256(r)
    else:
        # 第二种逻辑路径
        r = sha256(e) + t['challenge']
        for i in range(1, t['iIterate']):
            r = sha256(r)

    return r


class Hikvision():
    def __init__(self, user, pwd):
        self.user = user
        self.pwd = pwd
        self.session = requests.Session()

        self.Host = '**************'

        self.session.headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': base_ip,
            'Referer': f'{base_ip}/doc/index.html',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a1b) XWEB/8447 Flue',
        }
        self.sessionID = None
        self.encrypt_pwd = None
        self.iv = '31f1e2166154c9b7e4ddc7e2b5e799b0'
        self.key = '70102f9cbf98593f91563f060734e9e6'

        self.img_token = ''

    def capture_finger(self, user_id):
        url = f'https://{self.Host}/ISAPI/AccessControl/CaptureFingerPrint'
        data = f'''<CaptureFingerPrintCond version="2.0" xmlns="http://www.isapi.org/ver20/XMLSchema">
          <fingerNo>{user_id}</fingerNo>
        </CaptureFingerPrintCond>'''
        res = self.session.post(url, data=data, verify=False)
        return res.status_code, res.text

    def get_params(self):
        params = {
            'username': self.user,
            'random': self.pwd,
        }
        response = self.session.get(
            f'{base_ip}/ISAPI/Security/sessionLogin/capabilities',
            params=params,
            verify=False,
        )
        # 解析XML（注意处理命名空间）
        root = ET.fromstring(response.text)
        ns = {'ns': 'http://www.isapi.org/ver20/XMLSchema'}
        data = {
            "challenge": root.find('ns:challenge', ns).text,
            "iIterate": int(root.find('ns:iterations', ns).text),
            "salt": root.find('ns:salt', ns).text,
            "userName": "admin",
        }
        self.sessionID = root.find('ns:sessionID', ns).text
        self.encrypt_pwd = calculate_hash(data, self.pwd, True)

    # 模拟登录
    def login(self):
        print('HKVS登录')
        try:
            self.get_params()
            data = f"<SessionLogin><userName>admin</userName><password>{self.encrypt_pwd}</password><sessionID>{self.sessionID}</sessionID><isSessionIDValidLongTerm>false</isSessionIDValidLongTerm><sessionIDVersion>2</sessionIDVersion><isNeedSessionTag>true</isNeedSessionTag></SessionLogin>"
            url = f'{base_ip}/ISAPI/Security/sessionLogin?timeStamp={time.time() * 1000}'
            response = self.session.post(url, data=data, verify=False)
            root = ET.fromstring(response.text)
            ns = {'ns': 'http://www.isapi.org/ver20/XMLSchema'}
            sessionTag = root.find('ns:sessionTag', ns).text
            self.session.headers.update({
                'SessionTag': sessionTag
            })
            print('登录完成')
        except requests.exceptions.ConnectionError:
            print('门禁连接故障，请检查！！！')

    # 获取用户列表
    def get_user_list(self):
        params = {
            'format': 'json',
            'security': '1',
            'iv': self.iv,
        }
        data = '{"UserInfoSearchCond":{"searchID":"6c6dd3415dc74f27906a72e87ba68b65","maxResults":20,"searchResultPosition":0}}'
        response = self.session.post(
            f'{base_ip}/ISAPI/AccessControl/UserInfo/Search',
            params=params,
            data=data,
            verify=False,
            timeout=5000
        )
        try:
            user_list = response.json()['UserInfoSearch']['UserInfo']
        except KeyError:
            self.login()
            response = self.session.post(
                f'{base_ip}/ISAPI/AccessControl/UserInfo/Search',
                params=params,
                data=data,
                verify=False,
            )

            user_list = response.json()['UserInfoSearch']['UserInfo']
        # print(response.json())
        data_list = []
        # print(user_list)

        for i in user_list:
            try:
                faceURL = i['faceURL']
            except KeyError:
                faceURL = '/static/img/logo.jpg'
            data_list.append({
                'employeeNo': self.decrypt_data(i['employeeNo']),
                'name': self.decrypt_data(i['name']),
                'Valid': i['Valid'],
                'img': f"/door/getFaceImg?url={faceURL}" if 'logo' not in faceURL else faceURL
            })
        return data_list

    def get_img_content(self, url):
        token = self.get_token()
        params = {
            'token': token
        }
        res = self.session.get(url, params=params, verify=False)
        return res.content

    # 设置用户信息，授权等
    def set_user_info(self, employeeNo, name, beginTime, endTime):
        params = {
            'format': 'json',
            'security': '1',
            'iv': self.iv,
        }
        data = {
            "UserInfo": {
                "employeeNo": self.encrypt_data(employeeNo),
                "name": self.encrypt_data(name),
                "userType": "normal",  # normal  blackList
                "Valid": {"enable": True, "beginTime": beginTime, "endTime": endTime, "timeType": "local"},
                "doorRight": "1",
                "RightPlan": [{"doorNo": 1, "planTemplateNo": "1"}],
                "gender": "male",
                "localUIRight": False,
                "maxOpenDoorTime": 1,
                "userVerifyMode": "",
                "groupId": 1,
                "userLevel": "Employee",
            }
        }
        data = json.dumps(data)
        response = self.session.put(
            f'{base_ip}/ISAPI/AccessControl/UserInfo/Modify',
            params=params,
            data=data,
            verify=False,
        )
        # print()
        return response.json()['statusString']

    # 删除用户信息
    def del_user(self, employeeNo):
        url = f'https://{self.Host}/ISAPI/AccessControl/UserInfo/Delete?format=json&security=1&iv={self.iv}'
        data = {"UserInfoDelCond": {"EmployeeNoList": [{"employeeNo": self.encrypt_data(employeeNo)}]}}
        # self.session.headers['Content-Type'] = 'application/json'
        try:
            res = self.session.put(url, json=data, verify=False)
            self.session.headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=UTF-8'
            # print(self.session.headers, res.text)
            # print(res.)
            return res.json()
        except requests.exceptions.ConnectionError:
            self.login()
            res = self.session.put(url, json=data, verify=False)
            self.session.headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=UTF-8'
            # print(self.session.headers, res.text)
            # print(res.)
            return res.json()

    # 添加用户
    def add_user(self, employeeNo, name, beginTime="2025-06-23T00:00:00", endTime="2025-06-23T00:00:00"):
        params = {
            'format': 'json',
            'security': '1',
            'iv': self.iv,
        }
        data = {
            "UserInfo": {
                "employeeNo": self.encrypt_data(employeeNo),
                "name": self.encrypt_data(name),
                "userType": "normal",  # normal  blackList
                "Valid": {"enable": True, "beginTime": beginTime, "endTime": endTime, "timeType": "local"},
                "doorRight": "1",
                "RightPlan": [{"doorNo": 1, "planTemplateNo": "1"}],
                "gender": "male",
                "localUIRight": False,
                "maxOpenDoorTime": 1,
                "userVerifyMode": "",
                "groupId": 1,
                "userLevel": "Employee",
                "password": ""
            }
        }
        data = json.dumps(data)
        response = self.session.post(
            f'{base_ip}/ISAPI/AccessControl/UserInfo/Record',
            params=params,
            data=data,
            verify=False,
        )
        # print(response.text)

    # 上传用户人脸图像
    def updata_img(self, user_id, img):
        self.get_token()

        url = f'https://{self.Host}/ISAPI/Intelligent/FDLib/FDSetUp?format=json&token={self.img_token}'

        data = {'FaceDataRecord': json.dumps({
            "faceLibType": "blackFD",
            "FDID": "1",
            "FPID": user_id})}

        files = {
            'img': ('test.jpg', img, 'image/jpeg')
        }
        req = requests.Request('PUT', url, data=data, files=files).prepare()  # 预构建
        org_str = req.body.decode('utf-8', errors='ignore')
        aaa = re.findall('--(.*)--', org_str)[0]
        self.session.headers.update({'Content-Type':
                                         f'multipart/form-data; boundary=----{aaa}'})

        res = self.session.send(req, verify=False)
        print(res.text)
        return res.json()

    # 获取tooken
    def get_token(self):
        url = f'https://{self.Host}/ISAPI/Security/token?format=json'

        try:
            res = self.session.get(url, verify=False)
            # print(res.text)
            self.img_token = res.json()['Token']['value']
        except KeyError:
            self.login()
            res = self.session.get(url, verify=False)
            # print(res.text)
            self.img_token = res.json()['Token']['value']
        except requests.exceptions.ConnectionError:
            print('get token 连接超时')

    # 生成搜索ID
    def generate_searchid(self, length=32):
        """
        生成指定长度的随机十六进制字符串

        参数:
        length: 要生成的字符串长度（默认为32）

        返回:
        随机十六进制字符串
        """
        # 计算需要多少个UUID来拼接
        num_uuid = (length + 31) // 32  # 等同于Math.ceil(e/32)

        result = ""
        for _ in range(num_uuid):
            # 生成UUID并移除连字符
            hex_str = uuid.uuid4().hex
            result += hex_str

        # 截取到指定长度
        return result[:length]

    # 数据AES解密
    def decrypt_aes_cbc(self, ciphertext, secret_key, iv):
        original_data = bytes.fromhex(ciphertext)  # 自动转换为字节串
        ciphertext = base64.b64encode(original_data).decode('ascii')
        secret_key = bytes.fromhex(secret_key)
        iv = bytes.fromhex(iv)

        # 确保输入是字节格式
        if isinstance(ciphertext, str):
            ciphertext = base64.b64decode(ciphertext)
        if isinstance(secret_key, str):
            secret_key = secret_key.encode('utf-8')
        if isinstance(iv, str):
            iv = iv.encode('utf-8')

        # 创建解密器
        cipher = AES.new(secret_key, AES.MODE_CBC, iv)

        # 解密并去除填充
        decrypted_data = unpad(cipher.decrypt(ciphertext), AES.block_size)

        # 返回UTF-8字符串
        decoded_bytes = base64.b64decode(decrypted_data)
        # print(decoded_bytes)
        return decoded_bytes.decode('utf-8')

    # 数据AES加密
    def encrypt_aes_cbc(self, plaintext, secret_key, iv):
        """
        与decrypt_aes_cbc函数配对的加密函数

        参数:
        plaintext: 要加密的明文字符串
        secret_key: 十六进制格式的密钥字符串
        iv: 十六进制格式的初始化向量字符串

        返回: 十六进制格式的加密结果
        """
        # 确保明文是字节格式，如果是字符串，先编码为UTF-8字节
        if isinstance(plaintext, str):
            plaintext = plaintext.encode('utf-8')

        # 对明文进行base64编码，因为解密函数期望解密后的结果可以进行base64解码
        plaintext_b64 = base64.b64encode(plaintext)

        # 将密钥和IV从十六进制转换为字节
        secret_key = bytes.fromhex(secret_key)
        iv = bytes.fromhex(iv)

        # 确保所有输入都是字节格式
        if isinstance(secret_key, str):
            secret_key = secret_key.encode('utf-8')
        if isinstance(iv, str):
            iv = iv.encode('utf-8')

        # 创建加密器
        cipher = AES.new(secret_key, AES.MODE_CBC, iv)

        # 加密（添加PKCS7填充）
        ciphertext = cipher.encrypt(pad(plaintext_b64, AES.block_size))

        # 返回十六进制格式的加密结果
        return ciphertext.hex()

    def decrypt_data(self, data):
        return self.decrypt_aes_cbc(data, self.key, self.iv)

    def encrypt_data(self, data):
        return self.encrypt_aes_cbc(data, self.key, self.iv)


if __name__ == '__main__':
    hikvision = Hikvision('admin', 'hm480103')
    hikvision.login()
    hikvision.del_user('6666')
    # user_list = hikvision.get_user_list()
    # print(user_list)

    # hikvision.set_user_info('HMJX30188', '屈家新')
    # hikvision.set_user_info('test', '222222', "2025-06-23T00:00:00", "2025-06-23T00:00:00")
    # hikvision.add_user('test', '222222', "2025-06-23T00:00:00", "2025-06-23T00:00:00")
    # hikvision.get_token()
    # with open('../static/img/test2.jpg', 'rb') as f:
    #     img = f.read()
    #     hikvision.updata_img('test', img)
    # hikvision.capture_finger(1)
