import time

from pymodbus.client import ModbusTcpClient


def check_wr(func):
    def wrapper(self, *arg, **kwargs):
        """
        rr: 读寄存器
        rc: 读线圈
        wc: 写寄存器
        wc: 写线圈
        """
        if self.client.connect():
            try:
                type_, response = func(self, *arg, **kwargs)
            except:
                print('连接错误')
                return False
            if type_ == 'rr' and not response.isError():
                data = response.registers
                print('寄存器读取到数据：', data)
                self.client.close()
                return data
            elif type_ == 'rc' and not response.isError():
                data = response.bits
                print('线圈读取到数据：', data)
                self.client.close()
                return data
            elif type_ == 'wr' or type_ == 'wc' and not response.isError():
                print(f'数据 {arg[2]} 已写入..')
                self.client.close()
                return True
            else:
                print('读取错误')
        else:
            print('无法连接到设备...')
        return False

    return wrapper


class PLC:
    def __init__(self, ip: str, port: int):
        self.ip = ip
        self.port = port
        self.client = ModbusTcpClient(ip, port=port)
        self.data = None

    @check_wr
    def read_registers(self, slave_address: int, start_address: int, num_registers):
        return 'rr', self.client.read_holding_registers(start_address, count=num_registers, slave=slave_address)

    @check_wr
    def write_registers(self, slave_address: int, start_address: int, data: list):
        return 'wr', self.client.write_registers(start_address, values=data, slave=slave_address)

    @check_wr
    def read_coils(self, slave_address: int, start_address: int, num_coils):
        return 'rc', self.client.read_coils(start_address, count=num_coils, slave=slave_address)

    @check_wr
    def write_coils(self, slave_address: int, start_address: int, data: list):
        return 'wc', self.client.write_coils(start_address, values=data, slave=slave_address)


if __name__ == '__main__':
    plc = PLC(ip='*************', port=502)
    plc.write_coils(1, 0, [0, 0, 0, 0, 0, 0, 0, 0])
    # plc.write_registers(1, 4800, [1, 1, 0, 0])
    # plc.read_registers(1, 4800, 8)
    plc.read_coils(1, 0, 8)

    # for i in range(5):
    #     time.sleep(6)
    #     plc = PLC(ip='***********', port=8000)
    #     # plc.write_coils(1, 0, [1, 1, 1, 1, 1, 1, 1, 1])
    #     # plc.write_registers(1, 4800, [1, 1, 0, 0])
    #     plc.read_registers(1, 0, 2)
