import hashlib
import base64
import requests
from modules.task_thread import ThreadPool

check_list = ["信息化中心", "综合计划处", "党群人力处", "生产经营处", "技术处", "质量安全处", "物资供应处", "财务处", "审计处", "保障处", "修理项目部", "制造分厂",
              "修理项目部", "海南分厂", "中转刻录", "集中文印室", "厂领导", "会议用", ]


class Spider360:
    def __init__(self):
        self.session = requests.session()

        self.session.headers = {
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': '************:8081',
            'Origin': 'http://************:8081',
            'Referer': 'http://************:8081/dist/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) '
                          'Chrome/86.0.4240.198 Safari/537.36',
        }
        self.login_url = 'http://************:8081/user/login'
        self.corp_id = 'em6ef360f71ef411ef8862b839f41db532'

        self.query_data = {}
        self.user_name = None
        self.user_pwd = None

    def login(self, user_name, user_pwd):
        self.user_name = user_name
        self.user_pwd = user_pwd
        data = {
            'username': self.user_name,
            'password': md5_encrypt(self.user_pwd)
        }
        return self.session.post(url=self.login_url, data=data).json()

    def thread_task(self, i):
        online_num, info = self.get_department_PC_state(i['id'])
        # print(f'"{i["group_name"]}": {i["id"]}, ', end='')
        return i["group_name"], {
            "id": i['id'],
            'group_name': i["group_name"],
            'total': i["total"],
            'online_num': online_num,
            'info': info
        }

    def get_gorp(self, _check_list: list = None, ):
        url = f'http://************:8081/group/getMyGroupTree?d={get_time_temp()}_0.5582166448920183'
        res = self.session.get(url)
        grop_list = res.json()['data'][0]['children']
        # print(grop_list)
        pool = ThreadPool(max_workers=18, return_type='dict')
        for i in grop_list:
            if i["group_name"] not in check_list:
                continue
            # print(f'"{i["group_name"]}",', end='')
            pool.submit(self.thread_task, i)
        pool.wait_completion()
        # self.query_data['result_data'] = pool.shutdown()

        return pool.shutdown()

    def get_department_PC_state(self, id_):
        url = f'http://************:8081/host/list?sort=&limit=100&page=1&filter=%7B%22id%22%3A{id_}%2C%22systemname' \
              '%22%3A%22%22%2C%22sysbrowser%22%3A%22%22%2C%22cpu_name%22%3A%22%22%2C%22net_violate_punish%22%3A%22' \
              '%22%2C%22plat_id%22%3A%22%22%2C%22treeChange%22%3Afalse%7D&prodcombo=360exthost%2Fpcinfo&d=17471932917' \
              '24_0.012867049507219619'
        res = self.session.get(url)
        try:
            pc_list = res.json()['data']['list']
        except TypeError:
            self.login(self.user_name, self.user_pwd)
            pc_list = res.json()['data']['list']
        temp_list = []
        online_num = 0
        for i in pc_list:
            # print(f'编号：{i["computername"]}\nIP：{i["clientip"]}\n用户名：{i["username"]}\n状态：'
            #       f'{"在线" if i["status"] == "1" else "离线"}\n')
            if i["status"] == "1":
                online_num += 1
            temp_list.append({
                'computername': i["computername"],
                'clientip': i["clientip"],
                'username': i['username'],
                'state': "在线" if i["status"] == "1" else "离线"
            })
        return online_num, temp_list


def md5_encrypt(pwd):
    md5 = hashlib.md5()
    md5.update(pwd.encode('utf-8'))
    return md5.hexdigest()


def get_time_temp():
    return int(time.time() * 1000)


from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_OAEP
import binascii
import execjs
import time
import requests
from urllib.parse import quote
import json
from modules.op_sqlite import OpWebOAUser

def rsa_encrypt(public_key, text):
    rsa_key = RSA.importKey(public_key)
    rsa_cipher = PKCS1_OAEP.new(rsa_key)
    encrypt_text = rsa_cipher.encrypt(text.encode())
    return binascii.hexlify(encrypt_text).decode()


class OALogin:
    def __init__(self):
        self.pk = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAp2aay1967x1mLWb7Z9gUXfPnEFttUcNeCnmMAPx9WotkiWFBr34QQSVhnyLlR2J0pHHKv0UleKbsqYoJD/T8tiPk8Cm17uMXpP0iDPeppsBWssTDIgZxDqxTstsl4wsuyT/UB/djYEqabuObpl9tGKG3qAh4IFn6ZxxQTmlsKHhQG5Nxhdi7ChwhwcrSl04H9Q2kapQngdG8wOmjpG5KZ8ctHBmsolFxWd1/MFe8IAG4+3YkI4SwFLWG7a3DywS2OL4YYfL4+vJPXjmIyTGnzrlPjxil8SlVQuawZAJtHBooHJytKdvO93yt8w3VX9oXhn2fsIBWc46dkYulZgSoqQIDAQAB
-----END PUBLIC KEY-----"""
        self.session = requests.session()

        self.get_rsa_url = f'http://************:8888/rsa/weaver.rsa.GetRsaInfo?ts={get_time_temp()}'
        self.login_url = 'http://************:8888/api/hrm/login/checkLogin'

        self.code, self.flag = self.get_login_code()
        self.corp_id = "em6ef360f71ef411ef8862b839f41db532"

    def hex2b64(self, hex_str):
        # print(hex_str)
        byte_date = bytes.fromhex(hex_str)
        b64_data = base64.b64encode(byte_date)
        # print(b64_data)
        return b64_data.decode('utf-8')

    def get_login_code(self):
        print(self.session.cookies)
        res = self.session.get(self.get_rsa_url).json()
        print(f"获取code:{res['rsa_code']}")
        # print(self.session.cookies)
        return res['rsa_code'], res['rsa_flag']

    def encryptjs(self, text):
        with open('1.js', 'r', encoding='utf-8') as f:
            temp = execjs.compile(f.read())
            res = temp.call('get_result', self.code, text)
        return res

    def text2_rsa(self, text):
        self.code, self.flag = self.get_login_code()
        # self.get_login_code()
        encrypt_text = text + self.code
        name_en = rsa_encrypt(self.pk, encrypt_text)
        rsa_text = self.hex2b64(name_en) + self.flag
        rsa_text = quote(rsa_text)
        print(f'{text}-{encrypt_text} RSA加密结果： {rsa_text}')
        return rsa_text

    def get_department_info(self, dept_id=262):
        url = f'http://************:8999/emp/api/department/client/child?getTime={get_time_temp()}&' \
              f'corpid={self.corp_id}&deptid={dept_id}&fetch_mine=0'
        res = self.session.get(url)
        print(res.json())
        return res.json()

    def search_user_name(self, name):
        url = 'http://************:8888/api/hrm/search/getHrmSearchResult'
        data = {
            'tabkey': 'default_3',
            'showAllLevel': '0',
            'virtualtype': '',
            'resourcename': name,
            'manager': '',
            'subcompany': '',
            'department': '',
            'telephone': '',
            'mobile': '',
            'mobilecall': '',
        }
        sessionkey = self.session.post(url, data=data).json()['sessionkey']
        data = {
            'dataKey': sessionkey,
            'current': 1,
            'sortParams': []
        }
        url = 'http://************:8888/api/ec/dev/table/datas'
        data = self.session.post(url, data=data).json()
        data_list = data['datas'][0]
        return {'id': data_list['id'], 'workcode': data_list['workcode'], 'name': data_list['lastname']}

    def get_all_user_info(self):
        a = OpWebOAUser()
        sessionkey = ''
        for i in [1, 2, 3, 4, 5]:
            url = 'http://************:8888/api/hrm/search/getHrmSearchResult'
            data = {
                'tabkey': 'default_3',
                'showAllLevel': i,
                'virtualtype': '',
                'resourcename': '',
                'manager': '',
                'subcompany': '',
                'department': '',
                'telephone': '',
                'mobile': '',
                'mobilecall': '',
            }
            if i == 1:
                sessionkey = self.session.post(url, data=data).json()['sessionkey']
            print(sessionkey)
            data = {
                'dataKey': sessionkey,
                'current': i,
                'sortParams': []
            }
            url = 'http://************:8888/api/ec/dev/table/datas'
            data = self.session.post(url, data=data).json()
            for i in data['datas']:
                a.add_data(i['id'], i['workcode'], i['lastname'], i['departmentid'], i['departmentidspan'], i['jobtitlespan'], i['mobile'])

            time.sleep(1)
            # print({'id': i['id'], 'workcode': i['workcode'], 'name': i['lastname'], 'departmentid': i['departmentid'],
            #        'department': i['departmentidspan'],
            #        'jobtitle': i['jobtitlespan'], 'mobile': i['mobile']})


        # data_list = data['datas'][0]
        # return {'id': data_list['id'], 'workcode': data_list['workcode'], 'name': data_list['lastname']}

    def web_login(self):
        pass

    def login(self, username, pwd):
        data = {
            "islanguid": 7,
            "loginid": 'gbkpanPjCCtTzvW8yLdxVCK7D2Yv4YOrllpZ6LicW36xfaJ9muFGewR0YDiOXa2wTk1clLmQWiahisvb4O3VTZlkCLkSePQodFAMEreI/W12E0/HzUbT9RC7K86JrVgxFTE40SyJpQiR7pINMkGLuNUJxX3ib2h5fVH14sFEkcIY9T4tP1UCaRZIbo22Il8wEeYFVCSEGTgfaN7YnIr4u4wPDdmN4woiAdbwhwUWP8FmqYEGaOqefymgkYwO2vOUqRaCtIemFyUrCrs/oPg2eryNXooBsuKslqE6oIcpzcGt+rgJ02Tj7N6J0r/iCJ/iZWbMyrSBj9o9pL/S8GYWaA==``RSA``',
            "userpassword": 'K8tyg//dKj4kW0/jF7kQO+RCgSNCWsCSuAU+M1H1/iILZzNcOeIxplaxZvA3gvmH/oUmztSJ99mJnForuxTV60kxa9jV+psztlt//8Iwsnzytvv03ZpvVUoM7VJ6hKI7vWghpnAcjfYBdLOCVh8oj0Hi14r008wWJzjjZUP+DnFkxlpNkQWAmlz0X7Iqp+tPptrVwxaMPeD6f9pdSOh3i5cUlHM/ncmislZuRe6DBwQdppC+yCRYJBrIp4PXE1iHYUj8T3P4QmB3iSVQgLe1Sn3XgxK5QfIMKRPa51k29CquhDTu3fbhGxTwJaL01FEXIltjMMYSsMPTI6/KQ80Bcg==``RSA``',
            "logintype": 1,
            "isie": "false",
            "isRememberPassword": "false",
        }
        # self.session.headers = headers
        res = self.session.post(url=self.login_url, data=data)
        print(res.text)

        url = 'http://************:8888/api/ec/dev/app/getSSOCode?_ec_ismobile=true&_ec_browser=EMobile&' \
              '_ec_browserVersion=&_ec_os=Windows&_ec_osVersion=7&ismobile=1&&__random__=************* '
        res = self.session.get(url=url).json()
        keycode = res['keycode']
        data = {
            "ssokey": f"{keycode}", "account": "", "login_type": 1, "client_type": 1,
        }
        url = 'http://************:8999/emp/passport/ssologin'
        # 请求头重置
        # self.session.headers = {}
        res = self.session.post(url=url, json=data)
        # print('ssologin', res.json())
        access_token = res.json()['access_token']
        self.session.headers['emaccesstk'] = access_token
        self.session.headers['access_token'] = access_token

        # self.get_department_info()
        # self.get_department_info('3124')

    def save_cookie_headers(self):
        with open('session_headers.json', 'w', encoding='utf-8') as f:
            print(self.session.cookies)
            session_data = {
                "cookies": dict(self.session.cookies),
                "headers": dict(self.session.headers)
            }
            f.write(json.dumps(session_data))

    def load_cookie_headers(self):
        try:
            with open('session_headers.json', 'r', encoding='utf-8') as f:
                session_data = json.loads(f.read())
                self.session.cookies.update(session_data['cookies'])
                self.session.headers.update(session_data['headers'])
        except json.decoder.JSONDecodeError:
            print('Session从JSON文件获取错误')

    # def __del__(self):
    # self.save_cookie_headers()


if __name__ == '__main__':
    # text = 'MlhWhb4SU72ohrxjnXd2kQ=='
    #
    # str_ = base64.b64decode(text)
    # print(text)

    a = OALogin()
    a.login('HMJX30592', 'gsh1493829867')

    print(a.search_user_name('赵祥冯'))
    print(a.get_all_user_info())

    # a.web_login_search_user()
    # for i in a.get_department_info()['department']:
    #     print(f'"{i["name"]}": {{"total_count": {i["total_count"]},  "id": {i["id"]}}}')

    # for i in a.get_department_info(3125)["userlist"]:
    #     print(f'name: {i["name"]} total_count: {i["total_count"]}  userid: {i["userid"]}')
    # check_PC = Spider360()
    # print(f"{'登录成功' if check_PC.login('eppadmin', 'Humenjx@4801')['errno'] == 0 else '登录失败'}")
    # with open('test.json', 'w', encoding='utf-8') as f:
    #     f.write(json.dumps(check_PC.get_gorp(), indent=2, sort_keys=True, ensure_ascii=False))
    # print(json.dumps(check_PC.get_gorp(), indent=2, sort_keys=True))
    # check_PC.get_gorp()
    # print(check_PC.get_department_PC_state(15))
