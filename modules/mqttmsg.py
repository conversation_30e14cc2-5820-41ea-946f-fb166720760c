import time

import requests


class MqttNotice:
    """
    MQTT HNFC主题消息发布类
    """

    def __init__(self, num = 0):
        self.send_url = 'https://apis.bemfa.com/va/postJsonMsg'
        self.get_url = "https://apis.bemfa.com/va/getmsg"
        self.headers = {
            "Content-Type": "application/json; charset=utf-8"
        }
        self.send_data = {
            "uid": "adb34cf3582f4764a9f8dac93cf5deaa",
            "topic": "HNFC",
            "type": 1,
            "msg": f"OK#{num}"
        }
        self.send_data2 = {
            "uid": "adb34cf3582f4764a9f8dac93cf5deaa",
            "topic": "HNFC",
            "type": 1,
            "msg": "off_tft#"
        }
        self.params = {
            "uid": "adb34cf3582f4764a9f8dac93cf5deaa",
            "topic": "HNFC",
            "type": 1,
        }

    def sendMqttMsg(self):
        requests.post(url = self.send_url, headers = self.headers, json = self.send_data)

    def get_msg(self):
        res = requests.get(url = self.get_url, params = self.params)
        data = res.json()
        return data['data'][0]['msg']

    def off_tft(self):
        requests.post(url = self.send_url, headers = self.headers, json = self.send_data2)

    def notice(self):
        self.sendMqttMsg()
        time.sleep(5)
        msg = self.get_msg()
        if msg == 'Get':
            print('设备已播报')
            return True
        else:
            return False


if __name__ == '__main__':
    mqttnotice = MqttNotice(5)
    mqttnotice.notice()
