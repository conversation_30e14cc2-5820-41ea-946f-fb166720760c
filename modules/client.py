import requests
import random
import time
import dbutils

ip_list = [('*************',), ('***********',), ('************',), ('************',), ('***********',),
           ('************',), ('************',), ('************',), ('************',), ('************',),
           ('***********',), ('***********',), ('************',), ('***********',)]

for i in ip_list:
    data = {
        'ip': i[0],
        'temperature': random.randint(10, 40),
        'humidity': random.randint(40, 80),
        'voltage': random.randint(215, 230),
        'current': random.randint(0, 10)
        # 'current': 1
    }

    res = requests.post('http://127.0.0.1:50/web/updateData', json=data)
    print(res.json())
    time.sleep(0.1)
# print(random.randint(0, 5))
