import sqlite3
import time
from datetime import datetime
import json
from dbutils.pooled_db import PooledDB
from contextlib import contextmanager
from sqlite_config import db_pool


# sqlite_pool = PooledDB(
#     creator=sqlite3,
#     dat
# )

class BaseBAO:
    @contextmanager
    def _get_cursor(self):
        with db_pool.get_connection() as conn:
            cursor = conn.cursor()
            try:
                yield cursor
                conn.commit()
            except:
                conn.rollback()
                raise
            finally:
                cursor.close()

    def _execute(self, query, params=None):
        with self._get_cursor() as cursor:
            cursor.execute(query, params or ())
            return cursor

    def _fetch_all(self, query, params=None):
        with self._get_cursor() as cursor:
            cursor.execute(query, params or ())
            return cursor.fetchall()

    def _fetch_all_description(self, query, params=None):
        with self._get_cursor() as cursor:
            cursor.execute(query, params or ())
            return cursor.fetchall(), [keys[0] for keys in cursor.description]


class OpRecord(BaseBAO):
    def __init__(self):
        self.device_table = 'deviceRecord'

    # 获取所有设备信息
    def get_all_device_info(self, limit, offset):
        sql = f"""select * from {self.device_table} limit {limit} offset {offset}"""
        return self._fetch_all_description(sql)

    # 获取单个设备的所有信息
    def get_device_info_name(self, device_name):
        sql = f"""select * from {self.device_table} where name="{device_name}";"""
        return self._fetch_all(sql)

    # 获取单个设备的所有信息
    def get_device_info_ip(self, ip):
        sql = f"""select * from {self.device_table} where ip="{ip}";"""
        return self._fetch_all(sql)

    # 添加设备信息
    def add_device_data(self, device_name, position, ip, temperature, humidity, voltage, current, creat_time,
                        time_type, state, time_temp_name, time_temp_value):
        sql = f"""insert into {self.device_table} (device_name, position, ip, temperature, humidity, voltage, current, 
                creat_time, update_time, {time_type}, AirConditioner_state, {time_temp_name}) 
                values ('{device_name}', '{position}', '{ip}', '{temperature}', '{humidity}', '{voltage}', '{current}', 
                '{creat_time}', '{self.get_date_time()}', '{self.get_date_time()}', '{state}', '{time_temp_value}');"""
        self._execute(sql)

    # 删除某设备信息
    def delete_device_data(self, creat_time):
        sql = f'DELETE FROM {self.device_table} WHERE creat_time="{creat_time}";'
        return self._fetch_all(sql)

    # 执行sql语句
    def exec_sql_str(self, sql_str):
        return self._fetch_all_description(sql_str)

    # 获取设备数量
    def get_device_num(self):
        sql = f'select count(*) from {self.device_table};'
        return self._fetch_all(sql)

    # 更新空调打开时间
    def updat_device_open_time(self, ip):
        sql = f'UPDATE {self.device_table} SET open_time="{self.get_date_time()}" WHERE ip="{ip}";'
        return self._fetch_all(sql)

    # 更新空调关闭时间
    def updat_device_close_open_time(self, ip, time_type):
        sql = f'UPDATE {self.device_table} SET {time_type}="{self.get_date_time()}" WHERE ip="{ip}";'
        return self._fetch_all(sql)

    # 获取设备最新开启关闭时间
    def get_device_open_close_time(self, ip):
        sql = f'select MAX(open_time), MAX(close_time) from {self.device_table} WHERE ip="{ip}";'
        return self._fetch_all(sql)

    # 获取设备最新开启时间
    def get_device_open_time(self, ip):
        sql = f'select MAX(open_time) from {self.device_table} WHERE ip="{ip}";'
        return self._fetch_all(sql)

    # 获取设备最新关闭时间
    def get_device_close_time(self, ip):
        sql = f'select MAX(close_time) from {self.device_table} WHERE ip="{ip}";'
        return self._fetch_all(sql)

    def get_date_time(self):
        now = datetime.now()
        format_time = now.strftime("%Y-%m-%d %H:%M:%S")
        return format_time


class OpDevice(BaseBAO):
    def __init__(self):
        self.device_table = 'device'

    # 获取所有设备
    def get_all_device(self):
        sql = f"""select device from {self.device_table};"""
        return self._fetch_all(sql)

    # 获取所有设备信息
    def get_all_device_info(self, limit, offset):
        sql = f"""select * from {self.device_table} limit {limit} offset {offset}"""
        return self._fetch_all_description(sql)

    # 获取单个设备的所有信息
    def get_device_info_name(self, device_name):
        sql = f"""select * from {self.device_table} where name="{device_name}";"""
        return self._fetch_all_description(sql)

    # 获取单个用户的所有信息
    def get_device_info_ip(self, ip):
        sql = f"""select * from {self.device_table} where ip="{ip}";"""
        return self._fetch_all_description(sql)

    # 获取所有ip
    def get_all_ip(self):
        sql = 'select ip from device;'
        return self._fetch_all(sql)

    # 查询ip
    def query_ip(self, ip):
        sql = f'select ip, creat_time from device where ip="{ip}";'
        return self._fetch_all(sql)

    # 查询创建时间
    def query_creat_time(self, creat_time):
        sql = f'select ip, creat_time from device where creat_time="{creat_time}";'
        return self._fetch_all(sql)

    # 添加设备信息
    def add_device_info(self, device_name, department, position, ip, port, state, creat_role):
        sql = f"""insert into {self.device_table} (device_name, department, position, ip, port, state, creat_time, update_time, creat_role) 
                values ('{device_name}', '{department}', '{position}', '{ip}', '{port}', '{state}', '{self.get_date_time()}', '{self.get_date_time()}', '{creat_role}');"""
        print(sql)
        self._execute(sql)

    # 编辑更新设备信息
    def edit_device(self, device_name, department, position, ip, port, state, creat_time):
        sql = f'''UPDATE {self.device_table} SET device_name="{device_name}", department="{department}",
                  position="{position}", ip="{ip}", port="{port}", state="{state}" WHERE creat_time="{creat_time}";'''
        return self._fetch_all(sql)

    # 删除某设备信息
    def delete_device_data(self, creat_time):
        sql = f'DELETE FROM {self.device_table} WHERE creat_time="{creat_time}";'
        return self._fetch_all(sql)

    # 执行sql语句
    def exec_sql_str(self, sql_str):
        return self._fetch_all_description(sql_str)

    # 获取设备数量
    def get_device_num(self):
        sql = f'select count(*) from {self.device_table};'
        return self._fetch_all(sql)

    # 更新设备在线状态
    def updat_device_state(self, state, ip):
        sql = f'UPDATE {self.device_table} SET AirConditioner_state="{state}" WHERE ip="{ip}";'
        return self._fetch_all(sql)

    # 更新设备在线状态
    def get_device_state(self, ip):
        sql = f'select AirConditioner_state from {self.device_table} WHERE ip="{ip}";'
        # print(sql)
        return self._fetch_all(sql)

    # 获取某状态下的所有IP
    def get_ip_from_devices_state(self, state):
        sql = f'select ip from {self.device_table} WHERE AirConditioner_state="{state}";'
        # print(sql)
        return self._fetch_all(sql)

    # 获取设备和空调某状态下的所有IP
    def get_department_from_devices_2_states(self, state1, state2):
        """
        state1:  设备状态
        state2： 空调状态
        return:  筛选部门
        """
        sql = f'select department from {self.device_table} WHERE state="{state1}" and AirConditioner_state="{state2}";'
        # print(sql)
        return self._fetch_all(sql)

    def get_date_time(self):
        now = datetime.now()
        format_time = now.strftime("%Y-%m-%d %H:%M:%S")
        return format_time


class OpUserSqlite:
    def __init__(self):
        if __name__ == '__main__':
            self.conn = sqlite3.connect('../data/data.db')
        else:
            self.conn = sqlite3.connect('./data/data.db')
        self.cursor = self.conn.cursor()
        self.user_table = 'user'

    def create_user_table(self):
        try:
            self.cursor.execute("""
            CREATE TABLE user (
            id integer primary key autoincrement,
            user_name        VARCHAR (10),
            pwd              VARCHAR (20),
            department       VARCHAR (10),
            auth             VARCHAR (10),
            creat_time       VARCHAR (10)
            );
            """)
            self.conn.commit()
        except Exception as err:
            self.conn.rollback()
            print('erroe: ', err)

    # 获取所有用户名
    def get_all_user(self):
        sql = f"""select user_name from {self.user_table};"""
        self.cursor.execute(sql)
        self.conn.commit()
        info = self.cursor.fetchall()
        return info

    # 获取所有用户信息
    def get_all_user_info(self):
        sql = f"""select * from {self.user_table};"""
        self.cursor.execute(sql)
        self.conn.commit()
        info = self.cursor.fetchall()
        return info

    # 获取单个用户的所有信息
    def get_user_info(self, user_name):
        sql = f"""select * from {self.user_table} where user="{user_name}";"""
        self.cursor.execute(sql)
        self.conn.commit()
        info = self.cursor.fetchall()
        return info

    # 添加用户信息
    def add_user_info(self, user_name, pwd, department, auth):
        sql = f"""insert into {self.user_table} (user_name, pwd, department, creat_time, auth) 
                values ('{user_name}', '{pwd}', '{department}', '{self.get_date_time()}', '{auth}');"""
        self.cursor.execute(sql)
        self.conn.commit()

    # 删除某用户信息
    def delete_all_data(self, creat_time):
        sql = f'DELETE FROM {self.user_table} WHERE creat_time="{creat_time}";'
        self.cursor.execute(sql)
        self.conn.commit()

    def get_date_time(self):
        now = datetime.now()
        format_time = now.strftime("%Y-%m-%d %H:%M:%S")
        return format_time


class OpDeviceSqlite:
    def __init__(self):
        self.conn = None
        if __name__ == '__main__':
            self.conn = sqlite3.connect('../data/data.db')
        else:
            self.conn = sqlite3.connect('./data/data.db')
        self.cursor = self.conn.cursor()
        self.device_table = 'device'

    def create_device_table(self):
        try:
            self.cursor.execute("""
            CREATE TABLE device (
            id integer primary key autoincrement,
            device_name        VARCHAR (20),
            department         VARCHAR (20),
            position           VARCHAR (20),
            ip                 VARCHAR (20),
            port               VARCHAR (10),
            state              VARCHAR (20),
            creat_time         VARCHAR (20),
            update_time        VARCHAR (20),
            creat_role         VARCHAR (20)    
            );
            """)
            self.conn.commit()
        except Exception as err:
            self.conn.rollback()
            print('erroe: ', err)

    # 获取所有设备
    def get_all_device(self):
        sql = f"""select device from {self.device_table};"""
        self.cursor.execute(sql)
        self.conn.commit()
        info = self.cursor.fetchall()
        return info

    # 获取所有设备信息
    def get_all_device_info(self, limit, offset):
        sql = f"""select * from {self.device_table} limit {limit} offset {offset}"""
        self.cursor.execute(sql)
        self.conn.commit()
        info = self.cursor.fetchall()
        return info, [keys[0] for keys in self.cursor.description]

    # 获取单个设备的所有信息
    def get_device_info_name(self, device_name):
        sql = f"""select * from {self.device_table} where name="{device_name}";"""
        self.cursor.execute(sql)
        self.conn.commit()
        info = self.cursor.fetchall()
        return info, [keys[0] for keys in self.cursor.description]

    # 获取单个用户的所有信息
    def get_device_info_ip(self, ip):
        sql = f"""select * from {self.device_table} where ip="{ip}";"""
        self.cursor.execute(sql)
        self.conn.commit()
        info = self.cursor.fetchall()
        return info, [keys[0] for keys in self.cursor.description]

    # 获取所有ip
    def get_all_ip(self):
        sql = 'select ip from device;'
        self.cursor.execute(sql)
        self.conn.commit()
        info = self.cursor.fetchall()
        return info

    # 添加设备信息
    def add_device_info(self, device_name, department, position, ip, port, state, creat_role):
        sql = f"""insert into {self.device_table} (device_name, department, position, ip, port, state, creat_time, update_time, creat_role) 
                values ('{device_name}', '{department}', '{position}', '{ip}', '{port}', '{state}', '{self.get_date_time()}', '{self.get_date_time()}', '{creat_role}');"""
        self.cursor.execute(sql)
        self.conn.commit()

    # 删除某设备信息
    def delete_device_data(self, creat_time):
        sql = f'DELETE FROM {self.device_table} WHERE creat_time="{creat_time}";'
        self.cursor.execute(sql)
        self.conn.commit()
        return self.cursor.fetchall()

    # 执行sql语句
    def exec_sql_str(self, sql_str):
        self.cursor.execute(sql_str)
        self.conn.commit()
        info = self.cursor.fetchall()
        return info, [keys[0] for keys in self.cursor.description]

    # 获取设备数量
    def get_device_num(self):
        sql = f'select count(*) from {self.device_table};'
        self.cursor.execute(sql)
        self.conn.commit()
        return self.cursor.fetchall()

    # 更新设备在线状态
    def updat_device_state(self, state, ip):
        sql = f'UPDATE {self.device_table} SET AirConditioner_state="{state}" WHERE ip="{ip}";'
        self.cursor.execute(sql)
        self.conn.commit()
        return self.cursor.fetchall()

    # 获取设备在线状态
    def get_device_state(self, ip):
        sql = f'select AirConditioner_state from {self.device_table} WHERE ip="{ip}";'
        # print(sql)
        self.cursor.execute(sql)
        self.conn.commit()
        return self.cursor.fetchall()

    # 获取某状态设备列表
    def get_devices_state(self, AirConditioner_state):
        sql = f'select ip from {self.device_table} WHERE AirConditioner_state="{AirConditioner_state}";'
        # print(sql)
        self.cursor.execute(sql)
        self.conn.commit()
        return self.cursor.fetchall()

    # # 获取设备数量
    # def get_device_num(self, openid, uid):
    #     sql = f'UPDATE {self.device_table} SET wxpusheruid = "{uid}" WHERE openid="{openid}";'
    #     self.cursor.execute(sql)
    #     self.db.commit()

    def get_date_time(self):
        now = datetime.now()
        format_time = now.strftime("%Y-%m-%d %H:%M:%S")
        return format_time

    def __del__(self):
        self.cursor.close()
        self.conn.close()


class OpRecordSqlite:
    def __init__(self):
        self.conn = None
        if __name__ == '__main__':
            self.conn = sqlite3.connect('../data/data.db')
        else:
            self.conn = sqlite3.connect('./data/data.db')
        self.cursor = self.conn.cursor()
        self.device_table = 'deviceRecord'

    def create_device_table(self):
        try:
            self.cursor.execute("""
            CREATE TABLE deivceRecord (
                name        VARCHAR (20),
                position    VARCHAR (20),
                ip          VARCHAR (20) REFERENCES device (ip) ON DELETE NO ACTION
                                                                ON UPDATE NO ACTION,
                temperature VARCHAR (5),
                humidity    VARCHAR (5),
                voltage     VARCHAR (5),
                current     VARCHAR (5),
                power       VARCHAR (5),
                update_time VARCHAR (10) 
            );
            """)
            self.conn.commit()
        except Exception as err:
            self.conn.rollback()
            print('erroe: ', err)

    # 获取所有设备
    # def get_all_device(self):
    #     sql = f"""select device from {self.device_table};"""
    #     self.cursor.execute(sql)
    #     self.conn.commit()
    #     info = self.cursor.fetchall()
    #     return info

    # 获取所有设备信息
    def get_all_device_info(self, limit, offset):
        sql = f"""select * from {self.device_table} limit {limit} offset {offset}"""
        self.cursor.execute(sql)
        self.conn.commit()
        info = self.cursor.fetchall()
        return info, [keys[0] for keys in self.cursor.description]

    # 获取单个设备的所有信息
    def get_device_info_name(self, device_name):
        sql = f"""select * from {self.device_table} where name="{device_name}";"""
        self.cursor.execute(sql)
        self.conn.commit()
        info = self.cursor.fetchall()
        return info

    # 获取单个设备的所有信息
    def get_device_info_ip(self, ip):
        sql = f"""select * from {self.device_table} where ip="{ip}";"""
        self.cursor.execute(sql)
        self.conn.commit()
        info = self.cursor.fetchall()
        return info

    # 添加设备信息
    def add_device_data(self, device_name, position, ip, temperature, humidity, voltage, current,
                        time_type, state, time_temp_name, time_temp_value):
        sql = f"""insert into {self.device_table} (device_name, position, ip, temperature, humidity, voltage, current, 
                update_time, {time_type}, AirConditioner_state, {time_temp_name}) 
                values ('{device_name}', '{position}', '{ip}', '{temperature}', '{humidity}', '{voltage}', '{current}', 
                '{self.get_date_time()}', '{self.get_date_time()}', '{state}', '{time_temp_value}');"""
        # print(sql)
        self.cursor.execute(sql)
        self.conn.commit()

    # 删除某设备信息
    def delete_device_data(self, creat_time):
        sql = f'DELETE FROM {self.device_table} WHERE creat_time="{creat_time}";'
        self.cursor.execute(sql)
        self.conn.commit()
        return self.cursor.fetchall()

    # 执行sql语句
    def exec_sql_str(self, sql_str):
        self.cursor.execute(sql_str)
        self.conn.commit()
        info = self.cursor.fetchall()
        return info, [keys[0] for keys in self.cursor.description]

    # 获取设备数量
    def get_device_num(self):
        sql = f'select count(*) from {self.device_table};'
        self.cursor.execute(sql)
        self.conn.commit()
        return self.cursor.fetchall()

    # 更新空调打开时间
    def updat_device_open_time(self, ip):
        sql = f'UPDATE {self.device_table} SET open_time="{self.get_date_time()}" WHERE ip="{ip}";'
        self.cursor.execute(sql)
        self.conn.commit()
        return self.cursor.fetchall()

    # 更新空调关闭时间
    def updat_device_close_open_time(self, ip, time_type):
        sql = f'UPDATE {self.device_table} SET {time_type}="{self.get_date_time()}" WHERE ip="{ip}";'
        self.cursor.execute(sql)
        self.conn.commit()
        return self.cursor.fetchall()

    # 获取设备最新开启关闭时间
    def get_device_open_close_time(self, ip):
        sql = f'select MAX(open_time), MAX(close_time) from {self.device_table} WHERE ip="{ip}";'
        self.cursor.execute(sql)
        self.conn.commit()
        return self.cursor.fetchall()

    # 获取设备最新开启时间
    def get_device_open_time(self, ip):
        sql = f'select MAX(open_time) from {self.device_table} WHERE ip="{ip}";'
        self.cursor.execute(sql)
        self.conn.commit()
        return self.cursor.fetchall()

    # 获取设备最新关闭时间
    def get_device_close_time(self, ip):
        sql = f'select MAX(close_time) from {self.device_table} WHERE ip="{ip}";'
        self.cursor.execute(sql)
        self.conn.commit()
        return self.cursor.fetchall()

    # # 获取设备数量
    # def get_device_num(self, openid, uid):
    #     sql = f'UPDATE {self.device_table} SET wxpusheruid = "{uid}" WHERE openid="{openid}";'
    #     self.cursor.execute(sql)
    #     self.db.commit()

    def get_date_time(self):
        now = datetime.now()
        format_time = now.strftime("%Y-%m-%d %H:%M:%S")
        return format_time

    def __del__(self):
        self.cursor.close()
        self.conn.close()


if __name__ == '__main__':
    # op = OpUserSqlite()
    # op.create_user_table()
    # op.create_device_table()

    # op.add_user_info('admin', 'admin', '管理员', 1)

    op2 = OpDevice()
    # op2.create_device_table()
    # op2.add_device_info('测试', '机关楼', '技术处', '*************', '502', '关闭', '管理员')
    # print(op2.query_ip('*************'))
    print(op2.get_department_from_devices_2_states('开启', '开启'))
    # print(op.get_all_user())
    # keys = op2.cursor.description()
    # print(keys)
    # data, keys = op2.get_all_device_info()
    # data_list = []
    # for i in data:
    #     data_list.append(dict(zip(keys, i)))
    # print(data_list)

    # op3 = OpRecordSqlite()
    # sql_str = "select * from deviceRecord where ip in ('***********', '***********')"

    # select *, MAX(update_time) from deviceRecord where ip in ('***********', '************') group by ip

    # op3.exec_sql_str()




'''
ifram 请求hook
const iframe = document.getElementById('iframe')
    iframe.onload = function () {
        const script = `
        // const originalXHR = window.XMLHttpRequest;
        const originalXHR = $.ajax;
        $.ajax = function () {
            const xhr = new originalXHR();
            const originalOpen = xhr.open;
            const originalSend = xhr.send;
    
            let requestMethod, requestUrl;
    
            xhr.open = function (method, url) {
                requestMethod = method;
                requestUrl = url;
                return originalOpen.apply(this, arguments);
            }
    
            xhr.send = function (data) {
                console.log('拦截到请求：', {
                    method: requestMethod,
                    url: requestUrl,
                    data: data
                })
    
                if (requestUrl.includes('/api/')) {
                    data = JSON.parse(data || '{}')
                    data.modified = true
                    data = JSON.stringify(data)
                }
    
                xhr.addEventListener('load', function () {
                    console.log('拦截响应: ', {
                        status: xhr.status,
                        response: xhr.responseText
                    })
                    if(xhr.status === 200){
                        const response = JSON.parse(xhr.responseText)
                        Object.defineProperty(xhr, 'responseText', {
                            value: xhr.responseText,
                            writable: false
                        })
                    }
                })
                return originalSend.call(this, data)
            };
            
            return xhr;
        }
            `

        const scriptElement = iframe.contentDocument.createElement('script')
        scriptElement.textContent = script;
        iframe.contentDocument.head.appendChild(scriptElement)
    }
'''


'''
// const iframe = document.getElementById('iframe')
    // // debugger
    // iframe.onload = function () {
    //     // debugger
    //     const script = `
    //     !(function () {
    //     const originalXHR = $.ajax;
    //     $.ajax = function (url, options) {
    //         if (typeof url === 'object') {
    //             options = url
    //             url = options.url
    //         } else {
    //             options = options || {}
    //             options.url = url
    //         }
    //         debugger
    //         console.log('拦截到请求：', {
    //             method: options.type,
    //             url: options.url,
    //             data: options.data
    //         })
    //
    //         const xhr = originalXHR.call(this, options)
    //
    //         // const xhr = new originalXHR();
    //         // const originalOpen = xhr.open;
    //         // const originalSend = xhr.send;
    //
    //         // let requestMethod, requestUrl;
    //         //
    //         // xhr.open = function (method, url) {
    //         //     requestMethod = method;
    //         //     requestUrl = url;
    //         //     return originalOpen.apply(this, arguments);
    //         // }
    //         //
    //         // xhr.send = function (data) {
    //         //
    //         //
    //         //     if (requestUrl.includes('/api/')) {
    //         //         data = JSON.parse(data || '{}')
    //         //         data.modified = true
    //         //         data = JSON.stringify(data)
    //         //     }
    //         //
    //         //     xhr.addEventListener('load', function () {
    //         //         console.log('拦截响应: ', {
    //         //             status: xhr.status,
    //         //             response: xhr.responseText
    //         //         })
    //         //         if (xhr.status === 200) {
    //         //             const response = JSON.parse(xhr.responseText)
    //         //             Object.defineProperty(xhr, 'responseText', {
    //         //                 value: xhr.responseText,
    //         //                 writable: false
    //         //             })
    //         //         }
    //         //     })
    //         //     return originalSend.call(this, data)
    //         // };
    //
    //         return xhr;
    //     }
    // })();
    //         `
    //
    //     const scriptElement = iframe.contentDocument.createElement('script')
    //
    //     scriptElement.textContent = script;
    //     iframe.contentDocument.head.appendChild(scriptElement)
    // }
'''