from pymodbus.client import AsyncModbusTcpClient
import asyncio


def check_wr(func):
    async def wrapper(self, *arg, **kwargs):
        """
        rr: 读寄存器
        rc: 读线圈
        wc: 写寄存器
        wc: 写线圈
        """
        if self.client.connected:
            try:
                type_, response = await func(self, *arg, **kwargs)
            except:
                print('连接错误')
                return False
            if type_ == 'rr' and not response.isError():
                data = response.registers
                print('寄存器读取到数据：', data)
                # self.client.close()
                return data
            elif type_ == 'rc' and not response.isError():
                data = response.bits
                print('线圈读取到数据：', data)
                # self.client.close()
                return data
            elif type_ == 'wr' or type_ == 'wc' and not response.isError():
                print(f'数据 {arg[2]} 已写入..')
                # self.client.close()
                return True
            else:
                print('读取错误')
        else:
            print('无法连接到设备...')
        return False

    return wrapper


class PLC:
    def __init__(self, ip: str, port: int):
        self.ip = ip
        self.port = port
        self.client = None
        self.data = None

    async def connect_plc(self):
        print(123)
        if self.client and self.client.connected:
            return
        self.client = AsyncModbusTcpClient(host=self.ip, port=self.port, timeout=5, retries=3)
        await self.client.connect()

        if not self.client.connected:
            raise ConnectionError(f'{self.ip}:{self.port} 连接错误！！！请检查线路连接')

    async def disconnect_plc(self):
        if self.client and self.client.connected:
            print(self.client)
            await self.client.close()

    async def __aenter__(self):
        try:
            await self.connect_plc()
            return self
        except RecursionError:
            raise ConnectionError(f'{self.ip}:{self.port} 连接错误！！！请检查线路连接')

    async def __aexit__(self, exc_type, exc_val, exc_tb):

        await self.disconnect_plc()

    @check_wr
    async def read_registers(self, slave_address: int, start_address: int, num_registers):
        res = await self.client.read_holding_registers(start_address, count=num_registers, slave=slave_address)
        return 'rr', res

    @check_wr
    async def write_registers(self, slave_address: int, start_address: int, data: list):
        res = await self.client.write_registers(start_address, values=data, slave=slave_address)
        return 'wr', res

    @check_wr
    async def read_coils(self, slave_address: int, start_address: int, num_coils):
        res = await self.client.read_coils(start_address, count=num_coils, slave=slave_address)
        return 'rc', res

    @check_wr
    async def write_coils(self, slave_address: int, start_address: int, data: list):
        res = await self.client.write_coils(start_address, values=data, slave=slave_address)
        return 'wc', res


async def test():
    for i in range(10):
        await asyncio.sleep(3)
        async with PLC(ip='***********', port=8000) as plc:
            await plc.read_registers(1, 0, 2)


if __name__ == '__main__':
    # plc = PLC(ip='*************', port=502)
    # plc.write_coils(1, 0, [1, 1, 1, 1, 1, 1, 1, 1])
    # plc.write_registers(1, 4800, [1, 1, 0, 0])
    # plc.read_registers(1, 4800, 8)
    # plc.read_coils(1, 0, 8)
    asyncio.run(test())

    # _plc = PLC(ip='***********', port=8000)
    # plc.write_coils(1, 0, [1, 1, 1, 1, 1, 1, 1, 1])
    # plc.write_registers(1, 4800, [1, 1, 0, 0])
    # _plc.read_registers(1, 0, 2)
