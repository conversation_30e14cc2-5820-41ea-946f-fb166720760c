import base64
import re

import websocket
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_OAEP
import binascii
import execjs
import time
import requests
from urllib.parse import quote
import json
import threading
import queue
from modules.op_sqlite import OpOAUser


class OALogin:
    def __init__(self):
        self.pk = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAp2aay1967x1mLWb7Z9gUXfPnEFttUcNeCnmMAPx9WotkiWFBr34QQSVhnyLlR2J0pHHKv0UleKbsqYoJD/T8tiPk8Cm17uMXpP0iDPeppsBWssTDIgZxDqxTstsl4wsuyT/UB/djYEqabuObpl9tGKG3qAh4IFn6ZxxQTmlsKHhQG5Nxhdi7ChwhwcrSl04H9Q2kapQngdG8wOmjpG5KZ8ctHBmsolFxWd1/MFe8IAG4+3YkI4SwFLWG7a3DywS2OL4YYfL4+vJPXjmIyTGnzrlPjxil8SlVQuawZAJtHBooHJytKdvO93yt8w3VX9oXhn2fsIBWc46dkYulZgSoqQIDAQAB
-----E<PERSON> PUBLIC KEY-----"""
        self.session = requests.session()

        self.get_rsa_url = f'http://************:8888/rsa/weaver.rsa.GetRsaInfo?ts={self.get_time_temp()}'
        self.login_url = 'http://************:8888/api/hrm/login/checkLogin'

        self.code, self.flag = self.get_login_code()
        self.corp_id = "em6ef360f71ef411ef8862b839f41db532"

    def get_time_temp(self):
        return int(time.time() * 1000)

    def rsa_encrypt(self, public_key, text):
        rsa_key = RSA.importKey(public_key)
        rsa_cipher = PKCS1_OAEP.new(rsa_key)
        encrypt_text = rsa_cipher.encrypt(text.encode())
        return binascii.hexlify(encrypt_text).decode()

    def hex2b64(self, hex_str):
        # print(hex_str)
        byte_date = bytes.fromhex(hex_str)
        b64_data = base64.b64encode(byte_date)
        # print(b64_data)
        return b64_data.decode('utf-8')

    def get_login_code(self):
        print(self.session.cookies)
        res = self.session.get(self.get_rsa_url).json()
        print(f"获取code:{res['rsa_code']}")
        # print(self.session.cookies)
        return res['rsa_code'], res['rsa_flag']

    def encryptjs(self, text):
        with open('1.js', 'r', encoding='utf-8') as f:
            temp = execjs.compile(f.read())
            res = temp.call('get_result', self.code, text)
        return res

    def text2_rsa(self, text):
        self.code, self.flag = self.get_login_code()
        # self.get_login_code()
        encrypt_text = text + self.code
        name_en = self.rsa_encrypt(self.pk, encrypt_text)
        rsa_text = self.hex2b64(name_en) + self.flag
        rsa_text = quote(rsa_text)
        print(f'{text}-{encrypt_text} RSA加密结果： {rsa_text}')
        return rsa_text

    def get_department_info(self, dept_id=262):
        url = f'http://************:8999/emp/api/department/client/child?getTime={self.get_time_temp()}&' \
              f'corpid={self.corp_id}&deptid={dept_id}&fetch_mine=0'
        res = self.session.get(url)
        print(res.json())
        return res.json()

    def login(self, username, pwd):
        data = {
            "islanguid": 7,
            "loginid": 'gbkpanPjCCtTzvW8yLdxVCK7D2Yv4YOrllpZ6LicW36xfaJ9muFGewR0YDiOXa2wTk1clLmQWiahisvb4O3VTZlkCLkSePQodFAMEreI/W12E0/HzUbT9RC7K86JrVgxFTE40SyJpQiR7pINMkGLuNUJxX3ib2h5fVH14sFEkcIY9T4tP1UCaRZIbo22Il8wEeYFVCSEGTgfaN7YnIr4u4wPDdmN4woiAdbwhwUWP8FmqYEGaOqefymgkYwO2vOUqRaCtIemFyUrCrs/oPg2eryNXooBsuKslqE6oIcpzcGt+rgJ02Tj7N6J0r/iCJ/iZWbMyrSBj9o9pL/S8GYWaA==``RSA``',
            "userpassword": 'K8tyg//dKj4kW0/jF7kQO+RCgSNCWsCSuAU+M1H1/iILZzNcOeIxplaxZvA3gvmH/oUmztSJ99mJnForuxTV60kxa9jV+psztlt//8Iwsnzytvv03ZpvVUoM7VJ6hKI7vWghpnAcjfYBdLOCVh8oj0Hi14r008wWJzjjZUP+DnFkxlpNkQWAmlz0X7Iqp+tPptrVwxaMPeD6f9pdSOh3i5cUlHM/ncmislZuRe6DBwQdppC+yCRYJBrIp4PXE1iHYUj8T3P4QmB3iSVQgLe1Sn3XgxK5QfIMKRPa51k29CquhDTu3fbhGxTwJaL01FEXIltjMMYSsMPTI6/KQ80Bcg==``RSA``',
            "logintype": 1,
            "isie": "false",
            "isRememberPassword": "false",
        }
        # self.session.headers = headers
        res = self.session.post(url=self.login_url, data=data)
        print(res.text)

        url = 'http://************:8888/api/ec/dev/app/getSSOCode?_ec_ismobile=true&_ec_browser=EMobile&' \
              '_ec_browserVersion=&_ec_os=Windows&_ec_osVersion=7&ismobile=1&&__random__=************* '
        res = self.session.get(url=url).json()
        keycode = res['keycode']
        data = {
            "ssokey": f"{keycode}", "account": "", "login_type": 1, "client_type": 1,
        }
        url = 'http://************:8999/emp/passport/ssologin'
        # 请求头重置
        # self.session.headers = {}
        res = self.session.post(url=url, json=data)
        # print('ssologin', res.json())
        access_token = res.json()['access_token']
        self.session.headers['emaccesstk'] = access_token
        self.session.headers['access_token'] = access_token

        # self.get_department_info()
        # self.get_department_info('3124')

    def save_cookie_headers(self):
        with open('session_headers.json', 'w', encoding='utf-8') as f:
            print(self.session.cookies)
            session_data = {
                "cookies": dict(self.session.cookies),
                "headers": dict(self.session.headers)
            }
            f.write(json.dumps(session_data))

    def load_cookie_headers(self):
        try:
            with open('session_headers.json', 'r', encoding='utf-8') as f:
                session_data = json.loads(f.read())
                self.session.cookies.update(session_data['cookies'])
                self.session.headers.update(session_data['headers'])
        except json.decoder.JSONDecodeError:
            print('Session从JSON文件获取错误')

    def update_sql_user(self):
        # op = OpOAUser()

        department_info = self.get_department_info()['department']
        department_info.append({'total_count': 69, 'name': "修理项目部", 'id': 3182})
        department_info.append({'total_count': 88, 'name': "修理项目部", 'id': 3183})
        for i in department_info:
            # print(f'"{i["name"]}": {{"total_count": {i["total_count"]},  "id": {i["id"]}}}')

            # print(data, a.get_department_info(i['id']))
            user_list = []
            # print(i)
            for _ in self.get_department_info(i['id'])['userlist']:
                # print(_)
                user_list.append({"id": _['userid'], 'name': _['name'], 'position': _['position']})
                # data[i["name"]] = {"total_count": i["total_count"], "id": i["id"], "userlist": user_list}
                # op.add_data(_['userid'], _['name'], _['position'], i["name"], i["id"], i["total_count"])

        # print('data', data)

    # def __del__(self):
    # self.save_cookie_headers()


class MyWebSocket:
    def __init__(self, share_queue):
        # 从六楼转移至四楼后，OA Websocket参数发生变化，pass: "os8yy6w19vfyw1312c2wp1qr"参数，下次注意检查
        # msg_token 从 http://************:8999/emp/api/passport/getinit API中获取
        self.header = {
            'Sec-WebSocket-Protocol': 'xmpp',
            # 'Accept-Encoding': 'gzip, deflate',
            # 'Accept-Language': 'zh-CN',
            # 'Cache-Control': 'no-cache',
            # 'Connection': 'Upgrade',
            'Host': '************:7070',
            'Origin': 'http://************:8999',
            # 'Pragma': 'no-cache',
            # 'Sec-WebSocket-Extensions': 'permessage-deflate; client_max_window_bits',
            'Sec-WebSocket-Key': 'zFI2pREyZWGZuHIFdHUIAw==',
            'Sec-WebSocket-Version': '13',
            # 'Upgrade': 'websocket',
            # 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) E-Mobile/7.2.6 Chrome/69.0.3497.128 Electron/4.2.12 Safari/537.3'
        }
        self.ws = None
        self.ws_url = 'ws://************:7070/ws/'
        self.step_index = 0
        self.get_info = ''
        self.base_str = ''
        self.get_data_flag = False
        self.get_data_ok = False
        self.share_queue = share_queue
        self.connect()

    def get_result(self, str_):
        with open('./OA_websocket.js', 'r', encoding='utf-8') as f:
            js = execjs.compile(f.read())
            result = js.call('get_result', str_)
            return result

    def getUniqueId(self, ):
        js_code = '''
        getUniqueId = function (e) {
            var t = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (function (e) {
                var t = 16 * Math.random() | 0;
                return ("x" === e ? t : 3 & t | 8).toString(16)
            }));
            return "string" === typeof e || "number" === typeof e ? t + ":" + e : t + ""
        }'''

        js = execjs.compile(js_code)
        return js.call('getUniqueId', 'sendPresence')

    def connect(self):
        if self.ws:
            self.ws.close()
        self.ws = websocket.WebSocketApp(self.ws_url,
                                         header=self.header,
                                         on_message=self.on_message,
                                         on_open=self.on_open,
                                         on_error=self.on_error,
                                         on_close=self.on_close
                                         )

        threading.Thread(target=self.ws.run_forever, daemon=True).start()
        print('准备开启OA WebSocket连接')
        self.login()

    def __exit__(self, exc_type, exc_val, exc_tb):
        # pass
        self.ws.close()

    def on_message(self, ws_, message):
        print('接收到消息: ', message)
        if '<challenge xmlns="urn:ietf:params:xml:ns:xmpp-sasl">' in message:
            self.base_str = re.findall('>(.*?)<', message)[0]
        if '<close' in message:
            print('尝试重连！！！')
            self.ws.close()
            self.connect()
        elif 'error' in message:
            self.login()
        self.step_index += 1
        # self.get_info = message
        if self.get_data_flag:
            self.get_data_flag = False
            self.share_queue.put(message)
            self.get_data_ok = True

        # return message

    def on_open(self, ws_):
        # self.ws.close()
        # print('打开连接：', '<open to="weaver" version="1.0" xmlns="urn:ietf:params:xml:ns:xmpp-framing"/>')
        self.ws.send('<open to="weaver" version="1.0" xmlns="urn:ietf:params:xml:ns:xmpp-framing"/>')

    def on_error(self, ws_, error):
        print('连接错误: ', error)

    def on_close(self, ws_, code, close_msg):
        print('关闭连接: ', code, close_msg)
        self.step_index = 0
        # if code == 1000:
        #     self.connect()

    def send_msg(self, msg):
        print('发送： ', msg)
        if self.ws and self.ws.sock and self.ws.sock.connected:
            self.ws.send(msg)
        # else:
        # self.ws.close()
        # self.connect()
        # if self.ws and self.ws.sock and self.ws.sock.connected:
        #     self.ws.send(msg)

    def send_query(self, str_):
        send_str_ = f'<iq id="{self.getUniqueId().split(":")[0]}" to="weaver" type="get" xmlns="http://weaver.com.cn/status"><query>{str_}</query></iq>'
        self.send_msg(send_str_)
        while_flag = True
        while while_flag:
            self.get_data_flag = True
            if self.get_data_ok:
                self.get_data_ok = False
                self.get_data_flag = False
                # print("获取队列数据", )
                return self.share_queue.get()

    def close(self):
        self.ws.close()

    def login(self):
        while 1:
            if self.step_index == 2:
                self.step_index += 1
                self.send_msg(
                    '<auth mechanism="SCRAM-SHA-1" xmlns="urn:ietf:params:xml:ns:xmpp-sasl">biwsbj05NDN8bCxyPTUyMjMxMGNhMDY0ZjNiOGIzMWM0NjM5ODU0ZWQ1NWMx</auth>')
            elif self.step_index == 4:
                self.step_index += 1
                self.send_msg(
                    f'<response xmlns="urn:ietf:params:xml:ns:xmpp-sasl">{self.get_result(self.base_str)}</response>')
            elif self.step_index == 6:
                self.step_index += 1
                self.on_open('1')
            elif self.step_index == 9:
                self.step_index += 1
                self.send_msg(
                    '<iq id="_bind_auth_2" type="set" xmlns="jabber:client"><bind xmlns="urn:ietf:params:xml:ns:xmpp-bind"><resource>pc</resource></bind></iq>')
                break
            # elif self.step_index == 11:
            #     self.step_index += 1
            #     self.send_msg(
            #         f'<iq id="{sock.getUniqueId().split(":")[0]}" to="weaver" type="get" xmlns="http://weaver.com.cn/status"><query>[";623";,";157";,";367";,";98";,";425";,";95";]</query></iq>')
            #     self.get_data_flag = True
            #     break

    def get_online_info(self, department_id_):
        a = OpOAUser()
        user_list = a.get_department_user_by_id(department_id_)[0]
        # print(user_list)
        temp_str = '['
        temp_dict = '{'
        for i in user_list:
            temp_str += f'"{i[0]}",'
            temp_dict += f'"{i[0]}": "{i[1]}",'
            # print()
        temp_str += ']'
        temp_dict += '}'
        # print(temp_str)
        query = self.send_query(temp_str)
        # print(query)

        try:
            if 'not-authorized' in query:
                return {"online_num": 'null', "total": 'null', "info": 'OA登录认证错误'}
            elif 'offlineUsers' in query and 'onlineUsers' in query:
                online = eval(re.findall('":(.*?)}.*?":(.*?)}', query)[0][0])
                offline = eval(re.findall('":(.*?)}.*?":(.*?)}', query)[0][1])
            elif 'offlineUsers' in query and 'onlineUsers' not in query:
                online = []
                offline = eval(re.findall('":(.*?)}', query)[0])
            else:
                # print(re.findall('":(.*?)}', query))
                online = eval(re.findall('":(.*?)}', query)[0])

                offline = []
        except IndexError:
            return {"online_num": '后台错误', "total": '', "info": ''}
        result_user_list = []
        online_num = len(online)
        online_offline_num = len(online) + len(offline)
        offline_list = []
        for key, value in eval(temp_dict).items():
            if key in online:
                result_user_list.append({'id': key, 'name': value,
                                         'state': "在线",
                                         'statueColor': "layui-bg-green"})
            else:
                offline_list.append({'id': key, 'name': value,
                                     'state': '离线',
                                     'statueColor': "layui-bg-gray"})

        result_user_list += offline_list
        # print(online, offline)
        return {"online_num": online_num, "total": online_offline_num, "info": result_user_list}


from global_config import _config

if __name__ == '__main__':
    # text = 'MlhWhb4SU72ohrxjnXd2kQ=='
    #
    # str_ = base64.b64decode(text)
    # print(text)
    # data = {}

    # a = OALogin()
    # a.login('HMJX30592', 'gsh1493829867')
    # print(a.update_sql_user())

    # department_id = _config['oa_department_data']['技术处']['id']
    # a = OpOAUser()
    # user_list = a.get_department_user_by_id(department_id)[0]
    # temp_str = '['
    # temp_dict = '{'
    # for i in user_list:
    #     temp_str += f'"{i[0]}",'
    #     temp_dict += f'"{i[0]}": "{i[1]}",'
    #     # print()
    # temp_str += ']'
    # temp_dict += '}'
    # print(temp_str, temp_dict)
    # for key, value in eval(temp_dict).items():
    #     print(key, value)
    # print(department_id)
    # import re
    #
    my_queue = queue.Queue()
    # # #
    sock = MyWebSocket(my_queue)
    # a = sock.getUniqueId().split(":")[0]
    # b = f'<message from="943|l@weaver/pc" id="{a}" to="623|l@weaver" type="chat" xmlns="jabber:client"><body>{{"content":"测测测","extra":{{\"senderid\":\"943\",\"msg_id\":\"{a}\",\"countids\":\"\",\"receiverids\":\"623\",\"issetted\":\"\",\"msg_at_userid\":\"\"}},"objectName":"RC:TxtMsg"}}</body></message>'
    # print(b)
    # sock.send_msg(b)
    # print(sock.get_online_info(3124))

    while 1:
        pass

    # query = sock.send_query(temp_str)
    # online = eval(re.findall('":(.*?)}.*?":(.*?)}', query)[0][0])
    # offline = eval(re.findall('":(.*?)}.*?":(.*?)}', query)[0][1])
    # result_user_list = []
    # online_num = len(online)
    # online_offline_num = len(online) + len(offline)
    # for key, value in eval(temp_dict).items():
    #     result_user_list.append({'id': key, 'name': value,
    #                              'state': f'{"在线" if key in online else "离线"}',
    #                              'statueColor': f'{"layui-bg-green" if key in online else "layui-bg-gray"}'})
    #
    # print({"online_num": online_num, "tatal": online_offline_num, "info": result_user_list})

    # print(a.get_department_info(3124))

    # department_info = a.get_department_info()['department']
    # department_info.append({'total_count': 69, 'name': "修理项目部", 'id': 3182})
    # department_info.append({'total_count': 88, 'name': "修理项目部", 'id': 3183})
    # for i in department_info:
    #     # print(f'"{i["name"]}": {{"total_count": {i["total_count"]},  "id": {i["id"]}}}')
    #
    #     # print(data, a.get_department_info(i['id']))
    #     user_list = []
    #     for _ in a.get_department_info(i['id'])['userlist']:
    #         user_list.append({"id": _['userid'], 'name': _['name'], 'position': _['position']})
    #         data[i["name"]] = {"total_count": i["total_count"], "id": i["id"], "userlist": user_list}
    #         # op.add_data(_['userid'], _['name'], _['position'], i["name"], i["id"], i["total_count"])
    #
    # print('data', data)

    # new_msg_queue = queue.Queue(maxsize=1)
    # sock = MyWebSocket(new_msg_queue)
    # print(sock.send_query('["623";,";157";,";367";,";98";,";425";,";95";]'))
    # while_flag = True
    # sock.send_msg(
    #     f'<iq id="{sock.getUniqueId().split(":")[0]}" to="weaver" type="get" xmlns="http://weaver.com.cn/status"><query>[";623";,";157";,";367";,";98";,";425";,";95";]</query></iq>')
    #
    # while while_flag:
    #     sock.get_data_flag = True
    #
    #     if sock.get_data_ok:
    #         sock.get_data_ok = False
    #         print("获取队列数据", new_msg_queue.get())
    #         while_flag = False
    #         sock.close()

    # for i in a.get_department_info(3125)["userlist"]:
    #     print(f'name: {i["name"]} total_count: {i["total_count"]}  userid: {i["userid"]}')

    # def getUniqueId():
    #     js_code = '''
    #     getUniqueId = function (e) {
    #         var t = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (function (e) {
    #             var t = 16 * Math.random() | 0;
    #             return ("x" === e ? t : 3 & t | 8).toString(16)
    #         }));
    #         return "string" === typeof e || "number" === typeof e ? t + ":" + e : t + ""
    #     }'''
    #
    #     js = execjs.compile(js_code)
    #     return js.call('getUniqueId', 'sendPresence')
    # print(getUniqueId().split(':'))

    '<message from="943|l@weaver/pc" id="54f1ce41-347d-4c7a-abb8-c587eafdf9ba" to="623|l@weaver" type="chat" xmlns="jabber:client"><body>{";content";:";123456";,";extra";:";{\";senderid\";:\";943\";,\";msg_id\";:\";54f1ce41-347d-4c7a-abb8-c587eafdf9ba\";,\";countids\";:\";\";,\";receiverids\";:\";623\";,\";issetted\";:\";\";,\";msg_at_userid\";:\";\";}";,";objectName";:";RC:TxtMsg";}</body></message>'

    '[";623";,";157";,";367";,";98";,";425";,";95";]'
    '[{"onlineUsers":["157","367","623","95","98"]},{"offlineUsers":["425"]}]'
    '<iq id="90a32d9b-ed55-4d75-b40c-d0852ec67878" to="weaver" type="get" xmlns="http://weaver.com.cn/history"><query>{";targetId";:";623";,";targetType";:0,";fromUserId";:";943";,";lastTime";:3500756510636,";pageSize";:10,";isEM7";:true}</query></iq>'
