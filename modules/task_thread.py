import threading
import queue
import time
from typing import Callable, Any
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import PIL


# 线程池类
class QueueThreadPool:
    def __init__(self, num_threads: int):
        self.tasks = queue.Queue()
        self.workers = []
        self.shutdown_flag = False
        self.lock = threading.Lock()
        self.all_tasks_done = threading.Condition(self.lock)
        self.tasks_count = 0

        # 创建工作线程
        for _ in range(num_threads):
            worker = threading.Thread(target=self._worker)
            worker.daemon = True
            worker.start()
            self.workers.append(worker)

    def _worker(self):
        while True:
            try:
                # 从队列获取任务
                func, args, kwargs = self.tasks.get(timeout=0.1)
                try:
                    # 执行任务
                    func(*args, **kwargs)
                except Exception as e:
                    print(f'任务执行报错：{e}')
                finally:
                    with self.lock:
                        self.tasks_count -= 1
                        if self.tasks_count == 0:
                            self.all_tasks_done.notify_all()
                    self.tasks.task_done()
            except queue.Empty:
                if self.shutdown_flag:
                    return

    def submit(self, func: Callable, *args, **kwargs):
        if self.shutdown_flag:
            raise RuntimeError('不能在任务完成后再提交任务')

        with self.lock:
            self.tasks_count += 1
        self.tasks.put((func, args, kwargs))

    def wait_completion(self):
        """等待所有任务完成"""
        with self.lock:
            while self.tasks_count > 0:
                self.all_tasks_done.wait()

    def shutdown(self):
        """关闭线程池"""
        self.shutdown_flag = True
        self.wait_completion()

        # 等待所有工作线程退出
        for worker in self.workers:
            worker.join()


class ThreadPool:
    def __init__(self, max_workers: int = None, return_type: str = 'list'):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.futures = set()
        self.result_list = []
        self.result_dict = {}
        self.return_type = return_type

    def submit(self, func: Callable[..., Any], *args: Any, **kwargs: Any) -> None:
        future = self.executor.submit(func, *args, **kwargs)
        self.futures.add(future)

    def wait_completion(self, return_when: str = "ALL_COMPLETED") -> None:
        if return_when == "ALL_COMPLETED":
            for future in as_completed(self.futures):
                try:
                    if self.return_type == 'list':
                        result = future.result()  # 获取结果，如果有异常会在这里抛出
                        self.result_list.append(result if len(result) == 1 else result[1])
                    else:
                        name, result = future.result()  # 获取结果，如果有异常会在这里抛出
                        self.result_dict[name] = result
                        # print(self.result_dict)
                except Exception as e:
                    print(f'任务执行出错：{e}')
            self.futures.clear()
        else:
            raise ValueError("Only 'ALL_COMPLETED' is currently supported")

    def shutdown(self):
        self.executor.shutdown(wait=True)
        return self.result_list if self.return_type == 'list' else self.result_dict


if __name__ == '__main__':
    # pool = QueueThreadPool(8)
    #
    #
    # def example_task(task_id, sleep_time):
    #     print(f'Task {task_id} started, sleeping for {sleep_time} seconds')
    #     time.sleep(sleep_time)
    #     print(f'Task {task_id} completed')
    #
    #
    # for i in range(10):
    #     pool.submit(example_task, i, i % 3 + 1)
    #
    # pool.wait_completion()
    #
    # pool.shutdown()

    pool = ThreadPool(max_workers=5)


    def example_task(task_id, sleep_time):
        print(f'Task {task_id} started, sleeping for {sleep_time} seconds')
        time.sleep(sleep_time)
        print(f'Task {task_id} completed')
        return f"Task {task_id} result"


    for i in range(10):
        pool.submit(example_task, i, i % 3 + 0.5)

    pool.wait_completion()

    print(pool.shutdown())
