# -*- coding:utf-8 -*-
import pymysql


# 控制用户表
class operate_user:
    # 初始化连接数据库
    def __init__(self):
        self.db = pymysql.connect(
            host = '**************',
            user = 'root',
            port = 3306,
            password = '200012',
            database = 'syzx',
            charset = 'utf8'
        )
        self.cursor = self.db.cursor()
        self.retuen_data = {}
        self.table = 'user'

    # 获取所有用户名
    def get_all_user(self):
        sql = f"""select user from {self.table};"""
        self.cursor.execute(sql)
        self.db.commit()
        info = self.cursor.fetchall()
        return info

    # 获取所有用户信息
    def get_all_user_info(self):
        sql = f"""select * from {self.table};"""
        self.cursor.execute(sql)
        self.db.commit()
        info = self.cursor.fetchall()
        return info

    # 获取单个用户的所有信息
    def get_user_info(self, openid):
        sql = f"""select * from {self.table} where openid="{openid}";"""
        self.cursor.execute(sql)
        self.db.commit()
        info = self.cursor.fetchall()
        return info

    # 获取openid对应的用户名
    def get_user_name(self, openid):
        sql = f"""select user from {self.table} where openid="{openid}";"""
        self.cursor.execute(sql)
        self.db.commit()
        info = self.cursor.fetchall()
        return info

    # 查询openid
    def get_openid(self, openid):
        sql = f"""select openid from {self.table} where openid="{openid}";"""
        self.cursor.execute(sql)
        self.db.commit()
        info = self.cursor.fetchall()
        return info

    # 添加用户信息
    def add_user_info(self, user, phone, openid, time):
        sql = f"""insert into {self.table} (user, phone, openid, time) values ('{user}', '{phone}', '{openid}', '{time}');"""
        self.cursor.execute(sql)
        self.db.commit()

    # 删除某用户信息
    def delete_all_data(self, openid):
        sql = f'DELETE FROM {self.table} WHERE openid="{openid}";'
        self.cursor.execute(sql)
        self.db.commit()

    # 更新wxpusher uid
    def update_uid(self, openid, uid):
        sql = f'UPDATE {self.table} SET wxpusheruid = "{uid}" WHERE openid="{openid}";'
        self.cursor.execute(sql)
        self.db.commit()

    # 由openid获取wxpusher uid
    def get_wxpusher_uid(self, openid):
        sql = f'select wxpusheruid from {self.table} where openid="{openid}";'
        self.cursor.execute(sql)
        self.db.commit()
        uid = self.cursor.fetchall()[0][0]
        # for i in uid
        return uid

    # 更新头像
    def update_avatarurl(self, openid, avatarurl):
        sql = f'UPDATE {self.table} SET avatarurl = "{avatarurl}" WHERE openid="{openid}";'
        self.cursor.execute(sql)
        self.db.commit()

    # 获取留餐微信推送目标uid
    def meal_push(self):
        sql = f'select wxpusheruid from {self.table} where meal_push="1";'
        self.cursor.execute(sql)
        self.db.commit()
        data = self.cursor.fetchall()
        uids = []
        for i in data:
            uids.append(i[0])
        return uids

    def car_push(self):
        sql = f'select wxpusheruid from {self.table} where car_push="1";'
        self.cursor.execute(sql)
        self.db.commit()
        data = self.cursor.fetchall()
        uids = []
        for i in data:
            uids.append(i[0])
        return uids

    def update_push(self, type_, state, openid):
        sql = f'UPDATE {self.table} SET {type_} = "{state}" WHERE openid="{openid}";'
        self.cursor.execute(sql)
        self.db.commit()

    def __del__(self):
        self.cursor.close()
        self.db.close()


# 控制留餐表
class operate_meal:
    # 初始化连接数据库
    def __init__(self):
        self.db = pymysql.connect(
            host = '**************',
            user = 'root',
            port = 3306,
            password = '200012',
            database = 'syzx',
            charset = 'utf8'
        )
        self.cursor = self.db.cursor()
        self.retuen_data = {}

        self.table = 'meal'

    # 获取所有留餐信息
    def get_all_info(self):
        sql = f"""select * from {self.table};"""
        self.cursor.execute(sql)
        self.db.commit()
        info = self.cursor.fetchall()
        return info

    # 获取单个用户的留餐信息
    def get_user_meal(self, openid):
        sql = f"""select sub_time, meal_time, meal_num, inform from {self.table} where openid="{openid}";"""
        self.cursor.execute(sql)
        self.db.commit()
        info = self.cursor.fetchall()
        return info

    # 添加留餐信息
    def add_meal_info(self, openid, user, phone, meal_num, meal_time, sub_time, inform = '未通知'):
        sql = f"""insert into {self.table} (openid, user, phone, meal_num, meal_time, sub_time, inform) 
        values ('{openid}', '{user}', '{phone}', '{meal_num}', '{meal_time}', '{sub_time}', '{inform}');"""
        self.cursor.execute(sql)
        self.db.commit()

    # 删除某条留餐信息
    def delete_data(self, sub_time):
        sql = f'DELETE FROM {self.table} WHERE sub_time="{sub_time}";'
        self.cursor.execute(sql)
        self.db.commit()

    def select_include(self, date):
        sql = f'SELECT * FROM {self.table} WHERE sub_time LIKE "%{date}%";'
        self.cursor.execute(sql)
        self.db.commit()
        info = self.cursor.fetchall()
        return info

    def __del__(self):
        self.cursor.close()
        self.db.close()


# 控制用车表
class operate_usecar:
    # 初始化连接数据库
    def __init__(self):
        self.db = pymysql.connect(
            host = '**************',
            user = 'root',
            port = 3306,
            password = '200012',
            database = 'syzx',
            charset = 'utf8'
        )
        self.cursor = self.db.cursor()
        self.retuen_data = {}
        self.table = 'usecar'

    # 获取所有用车信息
    def get_all_info(self):
        sql = f"""select * from {self.table};"""
        self.cursor.execute(sql)
        self.db.commit()
        info = self.cursor.fetchall()
        return info

    # 获取单个用户的用车信息
    def get_users_info(self, openid):
        sql = f"""select car_time, car_area, car_type, car_num  from {self.table} where openid="{openid}";"""
        self.cursor.execute(sql)
        self.db.commit()
        info = self.cursor.fetchall()
        return info

    # 添加用车信息
    def add_use_info(self, openid, user, phone, car_num, car_area, car_type, car_time, sub_time, inform = '未通知'):
        sql = f"""insert into {self.table} (openid, user, phone, car_num, car_area, car_type, car_time, sub_time, inform) values 
        ('{openid}', '{user}', '{phone}', '{car_num}', '{car_area}', '{car_type}', '{car_time}', '{sub_time}', '{inform}');"""
        self.cursor.execute(sql)
        self.db.commit()

    # 删除某条留餐信息, 这里使用时间作为索引
    def delete_data(self, time):
        sql = f'DELETE FROM {self.table} WHERE sub_time="{time}";'
        self.cursor.execute(sql)
        self.db.commit()

    def update_inform(self):
        pass

    def __del__(self):
        self.cursor.close()
        self.db.close()


class operate_chat:
    def __init__(self):
        self.db = pymysql.connect(
            host = '**************',
            user = 'root',
            port = 3306,
            password = '200012',
            database = 'syzx',
            charset = 'utf8'
        )
        self.cursor = self.db.cursor()
        self.retuen_data = {}
        self.table = 'roomlist'

        # 获取所有用户名

    def get_room_list(self):
        sql = f"""select * from {self.table};"""
        self.cursor.execute(sql)
        self.db.commit()
        info = self.cursor.fetchall()
        return info

if __name__ == '__main__':
    Mysql = operate_user()
    # Mysql.update_push('meal_push', '0', 'oOhud4tSTNLKs2T4IzTrJmLEiLDg')
    # print(Mysql.get_all_user_info())
    # Mysql.update_uid('高世辉', '55555')
    # print(Mysql.get_wxpusher_uid('oOhud4tSTNLKs2T4IzTrJmLEiLDg'))
    # print(Mysql.meal_push())
    # data = Mysql.get_openid('123')
    # print(Mysql.get_openid('oOhud4tSTNLKs2T4IzTrJmLEiLDg'))
    # if data:
    #     print(data is None)
    # else:
    #     print(2)
    # Mysql.add_user_info('123', '456', '789')
    # print(Mysql.get_user_info('123'))
    # print(Mysql.get_user_info('oOhud4tSTNLKs2T4IzTrJmLEiLDg'))

    # Mysql = operate_meal()
    # Mysql = operate_usecar()

    # Mysql.add_meal_info('2/', '2', '2', '2', '2', '2')
    # Mysql.add_meal_info('1', '2', '1', '1', '1', '1')
    # Mysql.delete_data('2')
    # print(Mysql.get_user_meal('oOhud4tSTNLKs2T4IzTrJmLEiLDg'))
    # print(Mysql.get_all_info())
    # print(Mysql.select_include('2025-03-13'))

    # mysql = operate_chat()
    # print(mysql.get_room_list())
