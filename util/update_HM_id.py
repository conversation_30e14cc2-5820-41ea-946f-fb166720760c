import requests

cookie = {

    'content-type': 'application/x-www-form-urlencoded; charset=utf-8',
    'cookie': 'loginidweaver=943; languageidweaver=7; extloginid=b07e5225e2ba456d8940ecb8d155a908; ecology_JSessionid=aaa4fxS_K_5S49AemhjFz; __randcode__=e891721f-b434-495a-9700-c23afef6b9bd; CASTGC=TGT-56-y7wLKanhQsv7lPHTZRRCejKK24Kxfn7zXBOZ3fTxZoRcMrVHIa-c01',
    'host': '************:8888',
    'origin': 'http://************:8888',
    'referer': 'http://************:8888/login/oalogin.jsp',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}

url = 'http://************:8888/api/ec/dev/table/datas'

data = {
    'dataKey': '46f2ecae-3ab0-4d16-88c7-0c6807d82472_224F6E390F45F051F98739F6A3A4AEAE',
    'current': 1,
    'sortParams': []
}

data = requests.post(url, headers=cookie, data=data).json()
data_list = data['datas']
for i in data_list:
    print(i['id'], i['workcode'], i['lastname'])








