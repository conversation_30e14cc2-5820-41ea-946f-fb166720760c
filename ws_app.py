# -*- coding:utf-8 -*-
import json
import time

from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
from gevent import pywsgi
from geventwebsocket.handler import WebSocketHandler

from modules.sql_operate import operate_user

app = Flask(__name__)
CORS(app)

# 存储消息和连接
websockets = {}
messages = []


# 主页路由
@app.route('/')
def index():
    return render_template('index.html')


# 历史消息API
@app.route('/messages', methods = ['GET'])
def get_messages():
    room_id = request.args.get('roomId', 'default_room')
    print(f"请求历史消息，房间ID: {room_id}")
    # 返回历史消息
    return jsonify(messages)


# WebSocket处理函数
def websocket_app(environ, start_response):
    if environ.get('PATH_INFO') == '/chat':
        ws = environ.get('wsgi.websocket')
        if not ws:
            return start_response('400 Bad Request', [('Content-Type', 'text/plain')])

        # 获取连接参数
        query_string = environ.get('QUERY_STRING', '')
        params = {}
        if query_string:
            for param in query_string.split('&'):
                if '=' in param:
                    key, value = param.split('=', 1)
                    params[key] = value

        openid = params.get('openid')
        timestamp = params.get('timestamp')

        # 简单验证: 确保有openid且时间戳不早于5分钟前
        current_time = int(time.time() * 1000)
        if not openid or openid == 'guest':
            print(f"WebSocket连接未提供有效openid: {openid}")
            # 允许游客连接，但会有功能限制

        if timestamp:
            try:
                timestamp = int(timestamp)
                time_diff = current_time - timestamp
                if time_diff > 300000:  # 5分钟(300,000毫秒)
                    print(f"WebSocket连接时间戳过期: {time_diff}ms前")
                    # 可以选择拒绝连接，但这里只记录不拒绝
            except ValueError:
                print(f"无效的时间戳格式: {timestamp}")

        # 保存websocket连接
        client_id = id(ws)
        client_info = {
            'ws': ws,
            'openid': openid,
            'connected_at': current_time,
        }
        websockets[client_id] = client_info
        print(f"新客户端连接 {client_id}，openid: {openid}，当前连接数: {len(websockets)}")

        try:
            # 保持连接并处理消息
            while not ws.closed:
                message = ws.receive()
                if message:
                    try:
                        print(f"收到消息: {message}")
                        data = json.loads(message)

                        # 处理认证消息

                        mysql = operate_user()
                        # 确保客户端已认证才处理消息转发
                        print(mysql.get_openid(data.get('openid')))
                        if mysql.get_openid(data.get('openid')) and data.get('type') != 'auth':
                            # 确保消息中的openid与认证用户一致
                            # print(websockets, websockets[client_id])
                            # 存储消息
                            messages.append(data)

                            # 广播消息给所有客户端
                            response = json.dumps({
                                'type': 'message',
                                'data': data
                            })
                            for client_id, client_data in list(websockets.items()):
                                try:
                                    client_ws = client_data['ws']
                                    if not client_ws.closed:
                                        client_ws.send(response)
                                except Exception as e:
                                    print(f"发送消息错误: {e}")

                        else:
                            print(f"{data.get('nickname')} 发送一条认证注册消息")
                            # 通知客户端未认证
                            ws.send(json.dumps({
                                'type': 'error',
                                'code': 401,
                                'message': '未认证，请先认证'
                            }))

                    except Exception as e:
                        print(f"处理消息错误: {e}")
        except Exception as e:
            print(f"WebSocket错误: {e}")
        finally:
            # 移除关闭的连接
            if client_id in websockets:
                del websockets[client_id]
            print(f"客户端断开连接 {client_id}，当前连接数: {len(websockets)}")
        return []

    # 非WebSocket请求交给Flask处理
    return app(environ, start_response)


# 使用标准Flask方式运行
def run_with_flask(debug = True, host = '0.0.0.0', port = 5000):
    from werkzeug.serving import run_simple
    run_simple(host, port, app, use_debugger = debug, use_reloader = debug)


# 使用gevent-websocket运行
def run_with_gevent(debug = True, host = '0.0.0.0', port = 8000):
    server = pywsgi.WSGIServer((host, port), websocket_app, handler_class = WebSocketHandler)
    print(f"启动WebSocket服务器在 ws://{host}:{port}/chat")
    server.serve_forever()


if __name__ == "__main__":
    # if len(sys.argv) > 1 and sys.argv[1] == '--flask':
    #     # 使用Flask模式
    #     print("使用标准Flask模式启动 (HTTP only)")
    #     run_with_flask(port = 5000)
    # else:
    # 使用gevent-websocket模式
    print("使用gevent-websocket模式启动 (HTTP + WebSocket)")
    run_with_gevent(port = 1152)
